# Agentic Fit

A comprehensive fitness tracking application built with Flutter and Firebase.

## Features

- **Comprehensive User Onboarding**: Personalized fitness profile setup
- **AI-Powered Fitness Assistant**: Chat with an AI fitness coach
- **Workout Management**: Create, track, and manage custom workouts
- **Exercise Library**: Extensive database of exercises with proper form guidance
- **Progress Tracking**: Monitor your fitness journey with detailed statistics
- **Community Features**: Connect with other fitness enthusiasts
- **Nutrition Tracking**: Log and track your nutrition goals

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Auth, Functions)
- **AI Integration**: Google Generative AI (Gemini)
- **State Management**: Riverpod
- **Agent Framework**: Firebase Genkit

## Getting Started

### Prerequisites

- Flutter SDK (3.0.0 or higher)
- Firebase CLI
- Xcode (for iOS development)
- Android Studio (for Android development)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/agentic_fit.git
cd agentic_fit
```

2. Install dependencies:
```bash
flutter pub get
```

3. Set up Firebase:
- Create a new Firebase project
- Add your iOS and Android apps
- Download and add the configuration files:
  - `ios/Runner/GoogleService-Info.plist`
  - `android/app/google-services.json`

4. Set up environment variables:
- Copy `.env.example` to `.env`
- Add your API keys and configuration

5. Run the app:
```bash
flutter run
```

## Project Structure

```
lib/
├── core/           # Core utilities and configurations
├── features/       # Feature modules (auth, workout, etc.)
├── models/         # Data models
├── pages/          # App screens
├── services/       # Business logic and API services
├── shared/         # Shared components
└── widgets/        # Reusable UI components

agent/              # Firebase Genkit agents
docs/               # Documentation and guides
scripts/            # Build and deployment scripts
```

## Documentation

See the `docs/` folder for detailed documentation:
- [Architecture Overview](docs/ARCHITECTURE.md)
- [Deployment Guide](docs/DEPLOYMENT_READY.md)
- [AI Integration Setup](docs/FIREBASE_AI_SETUP_GUIDE.md)

## Contributing

Please read our contributing guidelines before submitting PRs.

## License

This project is licensed under the MIT License.