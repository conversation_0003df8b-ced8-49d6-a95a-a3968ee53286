#!/bin/bash

# Pre-submission checklist for Agentic Fit
# This script helps ensure everything is ready for app store submission

echo "📋 Pre-Submission Checklist for Agentic Fit"
echo "=========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Checklist items
declare -a checklist=(
    "App name updated from 'DreamFlow' to 'Agentic Fit'"
    "Bundle ID updated (iOS: com.yourcompany.agenticfit)"
    "Package name updated (Android: com.yourcompany.agenticfit)"
    "App version set correctly in pubspec.yaml"
    "App icons generated for all required sizes"
    "Screenshots prepared for all device sizes"
    "Privacy Policy URL created and accessible"
    "Terms of Service URL created and accessible"
    "App Store description written (< 4000 chars)"
    "Keywords selected (iOS: 100 chars total)"
    "Firebase configuration files updated"
    "Production Firebase security rules set"
    "Signing certificates created (iOS)"
    "Keystore file created (Android)"
    "App tested on real devices"
    "No placeholder content or Lorem ipsum"
    "All debug code removed"
    "Analytics and crash reporting configured"
    "App permissions justified in Info.plist"
    "Content rating questionnaire completed"
)

# Function to check items
check_item() {
    local item="$1"
    echo -n "$item"
    read -p " [y/n]: " response
    if [[ "$response" == "y" || "$response" == "Y" ]]; then
        echo -e "${GREEN}✓ Completed${NC}"
        return 0
    else
        echo -e "${RED}✗ Not completed${NC}"
        return 1
    fi
}

# Run through checklist
completed=0
total=${#checklist[@]}

echo "Please answer 'y' for completed items and 'n' for incomplete items:"
echo ""

for item in "${checklist[@]}"; do
    if check_item "$item"; then
        ((completed++))
    fi
    echo ""
done

# Summary
echo "=========================================="
echo -e "Checklist Summary: ${completed}/${total} items completed"
echo ""

if [ $completed -eq $total ]; then
    echo -e "${GREEN}🎉 Congratulations! You're ready to submit to the app stores!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Run: ./scripts/prepare_release.sh"
    echo "2. Upload to App Store Connect (iOS)"
    echo "3. Upload to Google Play Console (Android)"
else
    remaining=$((total - completed))
    echo -e "${YELLOW}⚠️  You have ${remaining} items remaining.${NC}"
    echo ""
    echo "Please complete all items before submitting to avoid rejection."
fi

# Additional checks
echo ""
echo "Running automated checks..."
echo ""

# Check if pubspec.yaml exists
if [ -f "pubspec.yaml" ]; then
    echo -e "${GREEN}✓${NC} pubspec.yaml found"
    version=$(grep "version:" pubspec.yaml | sed 's/version: //')
    echo "  Current version: $version"
else
    echo -e "${RED}✗${NC} pubspec.yaml not found"
fi

# Check for Firebase files
if [ -f "ios/Runner/GoogleService-Info.plist" ]; then
    echo -e "${GREEN}✓${NC} iOS Firebase config found"
else
    echo -e "${YELLOW}⚠️${NC} iOS Firebase config not found"
fi

if [ -f "android/app/google-services.json" ]; then
    echo -e "${GREEN}✓${NC} Android Firebase config found"
else
    echo -e "${YELLOW}⚠️${NC} Android Firebase config not found"
fi

# Check for key.properties
if [ -f "android/key.properties" ]; then
    echo -e "${GREEN}✓${NC} Android signing config found"
else
    echo -e "${YELLOW}⚠️${NC} Android signing config not found - run prepare_release.sh to create"
fi

# Check for app icons
if [ -d "ios/Runner/Assets.xcassets/AppIcon.appiconset" ] && [ "$(ls -A ios/Runner/Assets.xcassets/AppIcon.appiconset)" ]; then
    echo -e "${GREEN}✓${NC} iOS app icons found"
else
    echo -e "${YELLOW}⚠️${NC} iOS app icons missing - run generate_app_icons.sh"
fi

if [ -f "android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" ]; then
    echo -e "${GREEN}✓${NC} Android app icons found"
else
    echo -e "${YELLOW}⚠️${NC} Android app icons missing - run generate_app_icons.sh"
fi

echo ""
echo "=========================================="
echo "Checklist complete. Good luck with your submission! 🚀" 