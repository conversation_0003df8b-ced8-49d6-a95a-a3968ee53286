curl -X PATCH \
  "https://firestore.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/databases/(default)/documents/users_consolidated/lMdwxlD2GRY5WXXj74Zoph1Oern1" \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -H "Content-Type: application/json" \
  -d '{"uid":"lMdwxlD2GRY5WXXj74Zoph1Oern1","email":"<EMAIL>","displayName":"abenuro","photoURL":null,"personalInfo":{"name":"ben","gender":"male","dateOfBirth":"2000-05-15T00:00:00.000Z","height":172.72,"weight":36.29,"preferredUnits":"metric"},"fitnessProfile":{"goals":[{"type":"healthOptimization","priority":1,"sportActivity":null,"selectedAt":1748184031376}],"cardioLevel":0.2,"weightliftingLevel":0.2,"exercisesToAvoid":null,"personalCoachNotes":null},"workoutPreferences":{"environments":["homeNoEquipment"],"workoutsPerWeek":3,"workoutDurationMinutes":45,"additionalNotes":null},"stats":{"totalWorkouts":0,"totalMinutes":0,"weeklyGoal":150,"currentStreak":0,"totalCaloriesBurned":null,"lastWorkoutDate":null,"monthlyStats":{}},"preferences":{"units":"metric","notifications":true,"theme":"system","onboardingComplete":true,"comprehensiveOnboardingComplete":true,"fitnessGoalsSet":true,"language":null,"appSettings":{}},"createdAt":1748135993883,"updatedAt":1748184038598}'
