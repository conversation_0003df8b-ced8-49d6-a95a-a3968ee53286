const admin = require('firebase-admin');
const serviceAccount = require('../agent/po2vf2ae7tal9invaj7jkf4a06hsac-firebase-adminsdk-fbsvc-de10d14763.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function debugMuscleGroups() {
  console.log('=== Debugging Muscle Groups in Exercises Collection ===\n');
  
  try {
    // 1. Get all unique primaryMuscleGroup values
    const exercisesSnapshot = await db.collection('exercises').get();
    const muscleGroups = new Set();
    const shoulderExercises = [];
    
    exercisesSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.primaryMuscleGroup) {
        muscleGroups.add(data.primaryMuscleGroup);
        
        // Check for shoulder-related exercises
        const muscleGroupLower = data.primaryMuscleGroup.toLowerCase();
        const nameLower = data.name ? data.name.toLowerCase() : '';
        
        if (muscleGroupLower.includes('shoulder') || 
            nameLower.includes('shoulder') || 
            nameLower.includes('delt') ||
            nameLower.includes('press') && (nameLower.includes('overhead') || nameLower.includes('military'))) {
          shoulderExercises.push({
            id: doc.id,
            name: data.name,
            primaryMuscleGroup: data.primaryMuscleGroup,
            secondaryMuscleGroups: data.secondaryMuscleGroups
          });
        }
      }
    });
    
    // 2. Display all unique muscle groups
    console.log('All unique primaryMuscleGroup values:');
    console.log('-'.repeat(40));
    Array.from(muscleGroups).sort().forEach(group => {
      console.log(`- "${group}"`);
    });
    
    // 3. Display shoulder-related exercises
    console.log('\n\nShoulder-related exercises found:');
    console.log('-'.repeat(40));
    if (shoulderExercises.length > 0) {
      shoulderExercises.forEach(exercise => {
        console.log(`\nExercise: ${exercise.name}`);
        console.log(`ID: ${exercise.id}`);
        console.log(`Primary Muscle Group: "${exercise.primaryMuscleGroup}"`);
        if (exercise.secondaryMuscleGroups && exercise.secondaryMuscleGroups.length > 0) {
          console.log(`Secondary Muscle Groups: ${exercise.secondaryMuscleGroups.join(', ')}`);
        }
      });
    } else {
      console.log('No shoulder exercises found with current search criteria.');
    }
    
    // 4. Check specific queries
    console.log('\n\nTesting specific queries:');
    console.log('-'.repeat(40));
    
    // Test exact match for "Shoulders"
    const shouldersQuery = await db.collection('exercises')
      .where('primaryMuscleGroup', '==', 'Shoulders')
      .get();
    console.log(`Query: primaryMuscleGroup == "Shoulders" - Results: ${shouldersQuery.size}`);
    
    // Test case-insensitive variations
    const variations = ['shoulders', 'Shoulder', 'shoulder', 'SHOULDERS'];
    for (const variant of variations) {
      const query = await db.collection('exercises')
        .where('primaryMuscleGroup', '==', variant)
        .get();
      console.log(`Query: primaryMuscleGroup == "${variant}" - Results: ${query.size}`);
    }
    
    // 5. Sample a few exercises to see their structure
    console.log('\n\nSample exercise documents (first 5):');
    console.log('-'.repeat(40));
    let count = 0;
    exercisesSnapshot.forEach(doc => {
      if (count < 5) {
        const data = doc.data();
        console.log(`\nDocument ID: ${doc.id}`);
        console.log('Fields:', Object.keys(data).join(', '));
        console.log('Sample data:', JSON.stringify({
          name: data.name,
          primaryMuscleGroup: data.primaryMuscleGroup,
          secondaryMuscleGroups: data.secondaryMuscleGroups
        }, null, 2));
        count++;
      }
    });
    
  } catch (error) {
    console.error('Error debugging muscle groups:', error);
  } finally {
    // Clean up
    await admin.app().delete();
    process.exit(0);
  }
}

debugMuscleGroups();