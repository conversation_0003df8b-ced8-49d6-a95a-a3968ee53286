#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Preparing Agentic Fit for App Store Release${NC}"
echo "================================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

if ! command_exists flutter; then
    echo -e "${RED}❌ Flutter not found. Please install Flutter first.${NC}"
    exit 1
fi

if ! command_exists pod; then
    echo -e "${RED}❌ CocoaPods not found. Please install: sudo gem install cocoapods${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites met${NC}"

# Get current version
CURRENT_VERSION=$(grep "version:" pubspec.yaml | sed 's/version: //')
echo -e "\n${YELLOW}Current version: ${CURRENT_VERSION}${NC}"

# Ask for new version
read -p "Enter new version (e.g., 1.0.1+2) or press Enter to keep current: " NEW_VERSION
if [ ! -z "$NEW_VERSION" ]; then
    sed -i '' "s/version: .*/version: $NEW_VERSION/" pubspec.yaml
    echo -e "${GREEN}✅ Updated version to $NEW_VERSION${NC}"
fi

# Update app name if needed
echo -e "\n${YELLOW}Checking app name...${NC}"
read -p "Update app name from 'DreamFlow' to 'Agentic Fit'? (y/n): " UPDATE_NAME
if [ "$UPDATE_NAME" = "y" ]; then
    # Update pubspec.yaml
    sed -i '' 's/name: dreamflow/name: agentic_fit/' pubspec.yaml
    sed -i '' 's/description: "A DreamFlow project"/description: "Your AI-powered fitness companion"/' pubspec.yaml
    
    # Update iOS
    sed -i '' 's/<string>DreamFlow<\/string>/<string>Agentic Fit<\/string>/' ios/Runner/Info.plist
    sed -i '' 's/<string>dreamflow<\/string>/<string>Agentic Fit<\/string>/' ios/Runner/Info.plist
    
    echo -e "${GREEN}✅ Updated app name${NC}"
fi

# Clean and prepare
echo -e "\n${YELLOW}Cleaning project...${NC}"
flutter clean
flutter pub get

# iOS Build
echo -e "\n${BLUE}📱 Building iOS Release${NC}"
echo "========================"

cd ios
pod install
cd ..

echo -e "${YELLOW}Building iOS release...${NC}"
flutter build ios --release --no-codesign

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ iOS build successful${NC}"
    echo -e "${YELLOW}Next steps for iOS:${NC}"
    echo "1. Open ios/Runner.xcworkspace in Xcode"
    echo "2. Select your team in Signing & Capabilities"
    echo "3. Product → Archive"
    echo "4. Upload to App Store Connect"
else
    echo -e "${RED}❌ iOS build failed${NC}"
fi

# Android Build
echo -e "\n${BLUE}🤖 Building Android Release${NC}"
echo "============================"

# Check for keystore
if [ ! -f "android/key.properties" ]; then
    echo -e "${YELLOW}No signing key found. Creating one...${NC}"
    echo -e "${YELLOW}Please follow the prompts to create your keystore:${NC}"
    
    keytool -genkey -v -keystore ~/agentic-fit-release.keystore \
        -keyalg RSA -keysize 2048 -validity 10000 \
        -alias agentic-fit
    
    echo -e "\n${YELLOW}Creating key.properties file...${NC}"
    read -p "Enter keystore password: " STORE_PASSWORD
    read -p "Enter key password: " KEY_PASSWORD
    
    cat > android/key.properties << EOF
storePassword=$STORE_PASSWORD
keyPassword=$KEY_PASSWORD
keyAlias=agentic-fit
storeFile=$HOME/agentic-fit-release.keystore
EOF
    
    echo -e "${GREEN}✅ Keystore created${NC}"
fi

echo -e "${YELLOW}Building Android App Bundle...${NC}"
flutter build appbundle --release

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Android build successful${NC}"
    echo -e "${YELLOW}Output: build/app/outputs/bundle/release/app-release.aab${NC}"
else
    echo -e "${RED}❌ Android build failed${NC}"
fi

# Summary
echo -e "\n${BLUE}📋 Build Summary${NC}"
echo "================="
echo -e "${GREEN}✅ Release preparation complete!${NC}"
echo ""
echo "iOS Archive location: ios/build/ios/iphoneos/Runner.app"
echo "Android Bundle: build/app/outputs/bundle/release/app-release.aab"
echo ""
echo -e "${YELLOW}Don't forget to:${NC}"
echo "1. Update bundle identifiers in both platforms"
echo "2. Add app icons and screenshots"
echo "3. Create privacy policy and terms of service"
echo "4. Test on real devices"
echo "5. Set up App Store Connect and Google Play Console"

echo -e "\n${BLUE}Good luck with your submission! 🎉${NC}" 