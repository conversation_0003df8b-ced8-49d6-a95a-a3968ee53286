#!/bin/bash

# Script to generate all required app icon sizes for iOS and Android
# Requires: ImageMagick (install with: brew install imagemagick)

echo "🎨 App Icon Generator for Agentic Fit"
echo "===================================="

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick is not installed."
    echo "Please install it with: brew install imagemagick"
    exit 1
fi

# Check for source image
if [ -z "$1" ]; then
    echo "Usage: $0 <source-image-1024x1024.png>"
    echo "Please provide a 1024x1024 PNG image as the source."
    exit 1
fi

SOURCE_IMAGE="$1"

if [ ! -f "$SOURCE_IMAGE" ]; then
    echo "❌ Source image not found: $SOURCE_IMAGE"
    exit 1
fi

# Create directories
echo "Creating directories..."
mkdir -p ios/Runner/Assets.xcassets/AppIcon.appiconset
mkdir -p android/app/src/main/res/mipmap-mdpi
mkdir -p android/app/src/main/res/mipmap-hdpi
mkdir -p android/app/src/main/res/mipmap-xhdpi
mkdir -p android/app/src/main/res/mipmap-xxhdpi
mkdir -p android/app/src/main/res/mipmap-xxxhdpi
mkdir -p assets/app_store

# iOS Icons
echo "Generating iOS icons..."
IOS_PATH="ios/Runner/Assets.xcassets/AppIcon.appiconset"

# iPhone Notification
convert "$SOURCE_IMAGE" -resize 40x40 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 60x60 "$IOS_PATH/<EMAIL>"

# iPhone Settings
convert "$SOURCE_IMAGE" -resize 58x58 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 87x87 "$IOS_PATH/<EMAIL>"

# iPhone Spotlight
convert "$SOURCE_IMAGE" -resize 80x80 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 120x120 "$IOS_PATH/<EMAIL>"

# iPhone App
convert "$SOURCE_IMAGE" -resize 120x120 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 180x180 "$IOS_PATH/<EMAIL>"

# iPad Notifications
convert "$SOURCE_IMAGE" -resize 20x20 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 40x40 "$IOS_PATH/<EMAIL>"

# iPad Settings
convert "$SOURCE_IMAGE" -resize 29x29 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 58x58 "$IOS_PATH/<EMAIL>"

# iPad Spotlight
convert "$SOURCE_IMAGE" -resize 40x40 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 80x80 "$IOS_PATH/<EMAIL>"

# iPad App
convert "$SOURCE_IMAGE" -resize 76x76 "$IOS_PATH/<EMAIL>"
convert "$SOURCE_IMAGE" -resize 152x152 "$IOS_PATH/<EMAIL>"

# iPad Pro App
convert "$SOURCE_IMAGE" -resize 167x167 "$IOS_PATH/<EMAIL>"

# App Store
convert "$SOURCE_IMAGE" -resize 1024x1024 "$IOS_PATH/<EMAIL>"

# Android Icons
echo "Generating Android icons..."

# Launcher icons
convert "$SOURCE_IMAGE" -resize 48x48 "android/app/src/main/res/mipmap-mdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 72x72 "android/app/src/main/res/mipmap-hdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 96x96 "android/app/src/main/res/mipmap-xhdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 144x144 "android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png"
convert "$SOURCE_IMAGE" -resize 192x192 "android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png"

# Play Store icon
convert "$SOURCE_IMAGE" -resize 512x512 "assets/app_store/play_store_icon.png"

# Create Contents.json for iOS
cat > "$IOS_PATH/Contents.json" << EOF
{
  "images" : [
    {
      "size" : "20x20",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "20x20",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "3x"
    },
    {
      "size" : "29x29",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "29x29",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "3x"
    },
    {
      "size" : "40x40",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "40x40",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "3x"
    },
    {
      "size" : "60x60",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "60x60",
      "idiom" : "iphone",
      "filename" : "<EMAIL>",
      "scale" : "3x"
    },
    {
      "size" : "20x20",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "1x"
    },
    {
      "size" : "20x20",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "29x29",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "1x"
    },
    {
      "size" : "29x29",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "40x40",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "1x"
    },
    {
      "size" : "40x40",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "76x76",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "1x"
    },
    {
      "size" : "76x76",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "83.5x83.5",
      "idiom" : "ipad",
      "filename" : "<EMAIL>",
      "scale" : "2x"
    },
    {
      "size" : "1024x1024",
      "idiom" : "ios-marketing",
      "filename" : "<EMAIL>",
      "scale" : "1x"
    }
  ],
  "info" : {
    "version" : 1,
    "author" : "xcode"
  }
}
EOF

echo "✅ App icons generated successfully!"
echo ""
echo "iOS icons: $IOS_PATH"
echo "Android icons: android/app/src/main/res/mipmap-*"
echo "Play Store icon: assets/app_store/play_store_icon.png" 