import 'firebase_options.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'core/themes/theme.dart';
import 'pages/auth_page.dart';
import 'pages/home_page.dart';
import 'services/consolidated_user_service.dart';
import 'services/comprehensive_onboarding_service.dart';
import 'services/sample_workout_data.dart';
import 'pages/comprehensive_onboarding_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';

void main() async {

  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize sample workout data
    await SampleWorkoutDataService.initializeSampleWorkouts();
  } catch (e) {
    print('Firebase initialization error: $e');
  }

  // Initialize SharedPreferences
  final prefs = await SharedPreferences.getInstance();

  // Configure image cache to limit memory usage
  PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50 MB max
  PaintingBinding.instance.imageCache.maximumSize = 50; // 50 images max

  runApp(
    const ProviderScope(
      child: FitTrackerApp(),
    ),
  );
}

class FitTrackerApp extends StatelessWidget {
  const FitTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FitTracker - Your Workout Companion',
      theme: _buildTheme(context),
      darkTheme: darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      home: const AuthWrapper(),
    );
  }

  ThemeData _buildTheme(BuildContext context) {
    final baseTheme = ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF6750A4),
        brightness: Brightness.light,
      ),
    );

    return baseTheme.copyWith(
      textTheme: GoogleFonts.interTextTheme(baseTheme.textTheme),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

class AuthWrapper extends ConsumerStatefulWidget {
  const AuthWrapper({super.key});

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  final ConsolidatedUserService _userService = ConsolidatedUserService();
  final ComprehensiveOnboardingService _onboardingService = ComprehensiveOnboardingService();
  bool _isCheckingCache = true;
  bool _hasCheckedCache = false;
  bool _isCheckingOnboarding = false;
  bool _needsOnboarding = false;
  String? _lastCheckedUserId;

  @override
  void initState() {
    super.initState();
    _checkCachedAuth();
  }

  Future<void> _checkCachedAuth() async {
    try {
      // Check if user is logged in with cache
      final isLoggedIn = await _userService.isLoggedIn;
      final cachedUser = await _userService.getUserData();

      print('Auth check - Logged in: $isLoggedIn, Cached user: ${cachedUser?.name}');

      // Only check onboarding status if we have a Firebase user (not just cached)
      // This will be handled in the StreamBuilder when Firebase auth state is confirmed
      _needsOnboarding = false;

      if (mounted) {
        setState(() {
          _isCheckingCache = false;
          _hasCheckedCache = true;
        });
      }
    } catch (e) {
      print('Error checking cached auth: $e');
      if (mounted) {
        setState(() {
          _isCheckingCache = false;
          _hasCheckedCache = true;
          _needsOnboarding = false; // Default to not showing onboarding on error
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading while checking cache
    if (_isCheckingCache) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Checking authentication...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        print('AuthWrapper - Connection state: ${snapshot.connectionState}, Has data: ${snapshot.hasData}, User: ${snapshot.data?.uid}');

        // Show loading while Firebase is connecting (but only if we haven't checked cache)
        if (snapshot.connectionState == ConnectionState.waiting && !_hasCheckedCache) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading FitTracker...',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Check Firebase auth state
        final hasFirebaseUser = snapshot.hasData;

        if (hasFirebaseUser && snapshot.data != null) {
          final currentUserId = snapshot.data!.uid;

          // Only check onboarding if we haven't checked for this user yet
          if (!_isCheckingOnboarding && _lastCheckedUserId != currentUserId) {
            _isCheckingOnboarding = true;
            _lastCheckedUserId = currentUserId;

            // Add a small delay to ensure Firebase Auth is fully established
            Future.delayed(const Duration(milliseconds: 300), () async {
              if (mounted && snapshot.data != null && snapshot.data!.uid == currentUserId) {
                try {
                  // First try to check onboarding status directly from Firestore
                  bool hasCompleted = false;

                  try {
                    // Direct Firestore check to bypass type conversion issues
                    final userDoc = await FirebaseFirestore.instance
                        .collection('users')
                        .doc(currentUserId)
                        .get();

                    if (userDoc.exists) {
                      final data = userDoc.data()!;
                      // Check multiple fields for backward compatibility
                      hasCompleted = data['preferences']?['comprehensiveOnboardingComplete'] == true ||
                                   data['onboardingCompleted'] == true ||
                                   data['preferences']?['onboardingComplete'] == true;
                      print('Direct onboarding check - hasCompleted: $hasCompleted');
                    }
                  } catch (directError) {
                    print('Direct Firestore check failed: $directError');

                    // Fallback to service method
                    try {
                      final userData = await _userService.getUserData(currentUserId);
                      if (userData != null && userData.preferences.comprehensiveOnboardingComplete) {
                        hasCompleted = true;
                        print('User has completed onboarding according to user document');
                      }
                    } catch (serviceError) {
                      print('Service check also failed: $serviceError');
                      // Default to true to avoid stuck onboarding on error
                      hasCompleted = true;
                    }
                  }

                  if (mounted) {
                    setState(() {
                      _needsOnboarding = !hasCompleted;
                      _isCheckingOnboarding = false;
                    });
                  }
                } catch (error) {
                  print('Error checking onboarding status: $error');
                  if (mounted) {
                    setState(() {
                      _needsOnboarding = false; // Default to not showing onboarding on error
                      _isCheckingOnboarding = false;
                    });
                  }
                }
              }
            });

            // Show loading while checking onboarding status
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Setting up your profile...',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // Show onboarding if needed, otherwise show home page
          if (_needsOnboarding && !_isCheckingOnboarding) {
            return const ComprehensiveOnboardingScreen();
          }
          return const WorkoutHomePage();
        } else {
          // Reset onboarding state when user logs out
          if (_needsOnboarding || _isCheckingOnboarding || _lastCheckedUserId != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _needsOnboarding = false;
                  _isCheckingOnboarding = false;
                  _lastCheckedUserId = null;
                });
              }
            });
          }
          return const AuthPage();
        }
      },
    );
  }
}