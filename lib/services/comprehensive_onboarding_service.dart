import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_profile_model.dart' as profile_models;
import '../models/consolidated_user_model.dart';
import 'consolidated_user_service.dart';

class ComprehensiveOnboardingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ConsolidatedUserService _userService = ConsolidatedUserService();

  // Only use users collection
  CollectionReference get _usersCollection => _firestore.collection('users');

  // Save complete onboarding data
  Future<bool> saveComprehensiveOnboarding(profile_models.ComprehensiveOnboardingModel onboarding) async {
    try {
      final user = await _userService.getUserData(onboarding.profile.userId);
      if (user == null) return false;

      // Convert onboarding data to consolidated user model format
      final personalInfo = PersonalInfo(
        name: onboarding.profile.name,
        gender: _convertGenderToConsolidated(onboarding.profile.gender),
        dateOfBirth: onboarding.profile.age != null 
            ? DateTime.now().subtract(Duration(days: onboarding.profile.age! * 365))
            : null,
        height: onboarding.profile.heightFeet != null 
            ? onboarding.profile.heightFeet! * 30.48 // Convert feet to cm
            : null,
        weight: onboarding.profile.weightLbs != null 
            ? onboarding.profile.weightLbs! * 0.453592 // Convert lbs to kg
            : null,
        preferredUnits: 'imperial', // Since original data was in imperial
      );

      final fitnessProfile = FitnessProfile(
        goals: _convertFitnessGoalsToConsolidated(onboarding.fitnessGoals),
        cardioLevel: onboarding.fitnessLevel.cardioLevel,
        weightliftingLevel: onboarding.fitnessLevel.weightliftingLevel,
        exercisesToAvoid: onboarding.fitnessLevel.exercisesToAvoid,
        personalCoachNotes: onboarding.personalCoachNotes,
      );

      final workoutPreferences = WorkoutPreferences(
        environments: _convertWorkoutEnvironmentsToConsolidated(onboarding.workoutPreferences.environments),
        workoutsPerWeek: onboarding.workoutPreferences.workoutsPerWeek,
        workoutDurationMinutes: onboarding.workoutPreferences.workoutDurationMinutes,
        additionalNotes: onboarding.workoutPreferences.additionalNotes,
      );

      final updatedPreferences = user.preferences.copyWith(
        onboardingComplete: true,
        comprehensiveOnboardingComplete: true,
        fitnessGoalsSet: onboarding.fitnessGoals.isNotEmpty,
      );

      final updatedUser = user.copyWith(
        personalInfo: personalInfo,
        fitnessProfile: fitnessProfile,
        workoutPreferences: workoutPreferences,
        preferences: updatedPreferences,
      );

      return await _userService.updateUserData(updatedUser);
    } catch (e) {
      print('Error saving comprehensive onboarding: $e');
      return false;
    }
  }

  // Get comprehensive onboarding data
  Future<profile_models.ComprehensiveOnboardingModel?> getComprehensiveOnboarding(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return null;

      // Convert consolidated user model back to onboarding format if needed
      final profile = profile_models.UserProfileModel(
        userId: userId,
        name: user.personalInfo.name,
        gender: _convertGender(user.personalInfo.gender),
        age: user.personalInfo.age,
        heightFeet: user.personalInfo.height != null 
            ? user.personalInfo.height! / 30.48 // Convert cm to feet
            : null,
        weightLbs: user.personalInfo.weight != null 
            ? user.personalInfo.weight! / 0.453592 // Convert kg to lbs
            : null,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      );

      final fitnessLevel = profile_models.FitnessLevelModel(
        userId: userId,
        cardioLevel: user.fitnessProfile.cardioLevel,
        weightliftingLevel: user.fitnessProfile.weightliftingLevel,
        exercisesToAvoid: user.fitnessProfile.exercisesToAvoid,
        createdAt: user.createdAt,
      );

      final workoutPrefsModel = profile_models.WorkoutPreferencesModel(
        userId: userId,
        environments: _convertWorkoutEnvironments(user.workoutPreferences.environments),
        workoutsPerWeek: user.workoutPreferences.workoutsPerWeek,
        workoutDurationMinutes: user.workoutPreferences.workoutDurationMinutes,
        additionalNotes: user.workoutPreferences.additionalNotes,
        createdAt: user.createdAt,
      );

      return profile_models.ComprehensiveOnboardingModel(
        profile: profile,
        fitnessGoals: _convertFitnessGoalsFromConsolidated(user.fitnessProfile.goals),
        personalCoachNotes: user.fitnessProfile.personalCoachNotes,
        fitnessLevel: fitnessLevel,
        workoutPreferences: workoutPrefsModel,
        isComplete: user.preferences.comprehensiveOnboardingComplete,
        completedAt: user.updatedAt,
      );
    } catch (e) {
      print('Error getting comprehensive onboarding: $e');
      return null;
    }
  }

  // Helper method to convert Gender enums from consolidated to profile models
  profile_models.Gender? _convertGender(Gender? gender) {
    if (gender == null) return null;
    switch (gender) {
      case Gender.male:
        return profile_models.Gender.male;
      case Gender.female:
        return profile_models.Gender.female;
      case Gender.other:
        return profile_models.Gender.other;
      case Gender.preferNotToSay:
        return profile_models.Gender.preferNotToSay;
    }
  }

  // Helper method to convert Gender enums from profile models to consolidated
  Gender? _convertGenderToConsolidated(profile_models.Gender? gender) {
    if (gender == null) return null;
    switch (gender) {
      case profile_models.Gender.male:
        return Gender.male;
      case profile_models.Gender.female:
        return Gender.female;
      case profile_models.Gender.other:
        return Gender.other;
      case profile_models.Gender.preferNotToSay:
        return Gender.preferNotToSay;
    }
  }

  // Helper method to convert WorkoutEnvironment enums from consolidated to profile models
  List<profile_models.WorkoutEnvironment> _convertWorkoutEnvironments(List<WorkoutEnvironment> environments) {
    return environments.map((env) {
      switch (env) {
        case WorkoutEnvironment.largeGym:
          return profile_models.WorkoutEnvironment.largeGym;
        case WorkoutEnvironment.smallGym:
          return profile_models.WorkoutEnvironment.smallGym;
        case WorkoutEnvironment.homeBasic:
          return profile_models.WorkoutEnvironment.homeBasic;
        case WorkoutEnvironment.homeNoEquipment:
          return profile_models.WorkoutEnvironment.homeNoEquipment;
      }
    }).toList();
  }

  // Helper method to convert WorkoutEnvironment enums from profile models to consolidated
  List<WorkoutEnvironment> _convertWorkoutEnvironmentsToConsolidated(List<profile_models.WorkoutEnvironment> environments) {
    return environments.map((env) {
      switch (env) {
        case profile_models.WorkoutEnvironment.largeGym:
          return WorkoutEnvironment.largeGym;
        case profile_models.WorkoutEnvironment.smallGym:
          return WorkoutEnvironment.smallGym;
        case profile_models.WorkoutEnvironment.homeBasic:
          return WorkoutEnvironment.homeBasic;
        case profile_models.WorkoutEnvironment.homeNoEquipment:
          return WorkoutEnvironment.homeNoEquipment;
      }
    }).toList();
  }

  // Helper method to convert FitnessGoals from profile models to consolidated
  List<FitnessGoal> _convertFitnessGoalsToConsolidated(List<profile_models.FitnessGoal> goals) {
    return goals.map((goal) {
      return FitnessGoal(
        type: _convertFitnessGoalTypeToConsolidated(goal.type),
        priority: goal.priority,
        sportActivity: goal.sportActivity,
        selectedAt: goal.selectedAt,
      );
    }).toList();
  }

  // Helper method to convert FitnessGoals from consolidated to profile models
  List<profile_models.FitnessGoal> _convertFitnessGoalsFromConsolidated(List<FitnessGoal> goals) {
    return goals.map((goal) {
      return profile_models.FitnessGoal(
        type: _convertFitnessGoalTypeFromConsolidated(goal.type),
        priority: goal.priority,
        sportActivity: goal.sportActivity,
        selectedAt: goal.selectedAt,
      );
    }).toList();
  }

  // Helper method to convert FitnessGoalType from profile models to consolidated
  FitnessGoalType _convertFitnessGoalTypeToConsolidated(profile_models.FitnessGoalType type) {
    switch (type) {
      case profile_models.FitnessGoalType.healthOptimization:
        return FitnessGoalType.healthOptimization;
      case profile_models.FitnessGoalType.sportSpecific:
        return FitnessGoalType.sportSpecific;
      case profile_models.FitnessGoalType.buildMuscle:
        return FitnessGoalType.buildMuscle;
      case profile_models.FitnessGoalType.weightLoss:
        return FitnessGoalType.weightLoss;
      case profile_models.FitnessGoalType.increaseStamina:
        return FitnessGoalType.increaseStamina;
      case profile_models.FitnessGoalType.increaseStrength:
        return FitnessGoalType.increaseStrength;
    }
  }

  // Helper method to convert FitnessGoalType from consolidated to profile models
  profile_models.FitnessGoalType _convertFitnessGoalTypeFromConsolidated(FitnessGoalType type) {
    switch (type) {
      case FitnessGoalType.notReady:
        return profile_models.FitnessGoalType.healthOptimization; // Map to closest equivalent
      case FitnessGoalType.sportSpecific:
        return profile_models.FitnessGoalType.sportSpecific;
      case FitnessGoalType.buildMuscle:
        return profile_models.FitnessGoalType.buildMuscle;
      case FitnessGoalType.calisthenics:
        return profile_models.FitnessGoalType.buildMuscle; // Map to closest equivalent
      case FitnessGoalType.weightLoss:
        return profile_models.FitnessGoalType.weightLoss;
      case FitnessGoalType.healthOptimization:
        return profile_models.FitnessGoalType.healthOptimization;
      case FitnessGoalType.cardioBunny:
        return profile_models.FitnessGoalType.increaseStamina; // Map to closest equivalent
      case FitnessGoalType.increaseStrength:
        return profile_models.FitnessGoalType.increaseStrength;
      case FitnessGoalType.increaseStamina:
        return profile_models.FitnessGoalType.increaseStamina;
    }
  }

  // Update comprehensive onboarding data
  Future<bool> updateComprehensiveOnboarding(profile_models.ComprehensiveOnboardingModel onboarding) async {
    try {
      // Just use the save method which handles updates
      return await saveComprehensiveOnboarding(onboarding);
    } catch (e) {
      print('Error updating comprehensive onboarding: $e');
      return false;
    }
  }

  // Check if user has completed comprehensive onboarding
  Future<bool> hasCompletedComprehensiveOnboarding(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      return user?.preferences.comprehensiveOnboardingComplete ?? false;
    } catch (e) {
      print('Error checking comprehensive onboarding status: $e');
      return false;
    }
  }

  // Get user profile
  Future<profile_models.UserProfileModel?> getUserProfile(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return null;

      return profile_models.UserProfileModel(
        userId: userId,
        name: user.personalInfo.name,
        gender: _convertGender(user.personalInfo.gender),
        age: user.personalInfo.age,
        heightFeet: user.personalInfo.height != null 
            ? user.personalInfo.height! / 30.48 // Convert cm to feet
            : null,
        weightLbs: user.personalInfo.weight != null 
            ? user.personalInfo.weight! / 0.453592 // Convert kg to lbs
            : null,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      );
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }

  // Get fitness level
  Future<profile_models.FitnessLevelModel?> getFitnessLevel(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return null;

      return profile_models.FitnessLevelModel(
        userId: userId,
        cardioLevel: user.fitnessProfile.cardioLevel,
        weightliftingLevel: user.fitnessProfile.weightliftingLevel,
        exercisesToAvoid: user.fitnessProfile.exercisesToAvoid,
        createdAt: user.createdAt,
      );
    } catch (e) {
      print('Error getting fitness level: $e');
      return null;
    }
  }

  // Get workout preferences
  Future<profile_models.WorkoutPreferencesModel?> getWorkoutPreferences(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return null;

      return profile_models.WorkoutPreferencesModel(
        userId: userId,
        environments: _convertWorkoutEnvironments(user.workoutPreferences.environments),
        workoutsPerWeek: user.workoutPreferences.workoutsPerWeek,
        workoutDurationMinutes: user.workoutPreferences.workoutDurationMinutes,
        additionalNotes: user.workoutPreferences.additionalNotes,
        createdAt: user.createdAt,
      );
    } catch (e) {
      print('Error getting workout preferences: $e');
      return null;
    }
  }

  // Delete comprehensive onboarding data (for testing)
  Future<bool> deleteComprehensiveOnboarding(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return false;

      // Reset onboarding status in consolidated user model
      final updatedPreferences = user.preferences.copyWith(
        onboardingComplete: false,
        comprehensiveOnboardingComplete: false,
        fitnessGoalsSet: false,
      );

      // Reset personal info, fitness profile, and workout preferences to defaults
      final updatedUser = user.copyWith(
        personalInfo: PersonalInfo(name: user.personalInfo.name),
        fitnessProfile: FitnessProfile(),
        workoutPreferences: WorkoutPreferences(),
        preferences: updatedPreferences,
      );

      return await _userService.updateUserData(updatedUser);
    } catch (e) {
      print('Error deleting comprehensive onboarding: $e');
      return false;
    }
  }
} 