import 'package:flutter/material.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
// Temporarily commenting out audio packages due to build issues
// import 'package:record/record.dart';
// import 'package:flutter_soloud/flutter_soloud.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:developer';

class AudioChatMessage {
  final String id;
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isLoading;
  final bool isAudio;
  final Uint8List? audioData;

  AudioChatMessage({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isLoading = false,
    this.isAudio = false,
    this.audioData,
  });

  AudioChatMessage copyWith({
    String? id,
    String? text,
    bool? isUser,
    DateTime? timestamp,
    bool? isLoading,
    bool? isAudio,
    Uint8List? audioData,
  }) {
    return AudioChatMessage(
      id: id ?? this.id,
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
      isAudio: isAudio ?? this.isAudio,
      audioData: audioData ?? this.audioData,
    );
  }
}

class FirebaseAIAudioService extends ChangeNotifier {
  static final FirebaseAIAudioService _instance = FirebaseAIAudioService._internal();
  factory FirebaseAIAudioService() => _instance;
  FirebaseAIAudioService._internal();

  // Firebase Vertex AI Live Model
  late GenerativeModel _liveModel;
  StreamSubscription<GenerateContentResponse>? _sessionSubscription;
  
  // Audio components - temporarily disabled
  // final _recorder = AudioRecorder();
  // AudioSource? _audioSource;
  // SoundHandle? _soundHandle;
  
  // State management
  final List<AudioChatMessage> _messages = [];
  bool _isInitialized = false;
  bool _isSessionActive = false;
  bool _isConversationActive = false;
  bool _isLoading = false;
  
  // Stream controllers
  StreamController<bool> _stopController = StreamController<bool>();
  Stream<Uint8List>? _inputStream;

  // Getters
  List<AudioChatMessage> get messages => List.unmodifiable(_messages);
  bool get isInitialized => _isInitialized;
  bool get isSessionActive => _isSessionActive;
  bool get isConversationActive => _isConversationActive;
  bool get isLoading => _isLoading;

  Future<void> initialize() async {
    try {
      // Temporarily disable audio initialization
      // await SoLoud.instance.init(sampleRate: 24000, channels: Channels.mono);
      
      // Check microphone permissions - temporarily disabled
      // final hasPermission = await _recorder.hasPermission();
      // if (!hasPermission) {
      //   throw Exception('Microphone permission required for voice chat');
      // }

      // Initialize Firebase Vertex AI Model
      _liveModel = FirebaseVertexAI.instance
          .generativeModel(
        model: 'gemini-1.5-flash',
        systemInstruction: Content.system('''
        You are a friendly and knowledgeable fitness AI assistant. Your role is to:
        - Help users with workout planning and exercise guidance
        - Provide motivational support during workouts
        - Answer questions about fitness, nutrition, and health
        - Maintain an encouraging and positive tone
        - Keep responses concise and actionable for voice interactions
        '''),
      );

      _isInitialized = true;
      log('Firebase Vertex AI Audio Service initialized successfully (audio temporarily disabled)');
    } catch (e) {
      log('Error initializing Firebase Vertex AI Audio Service: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<void> startConversation() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _isLoading = true;
      notifyListeners();

      // Start live session
      // _inputStream = await _startRecordingStream();
      
      // Prepare the input stream for the model
      // final audioStream = _inputStream!.map((data) {
      //   return Content.multi([Part.data('audio/pcm', data)]);
      // });

      // Start the streaming session
      // final responseStream = _liveModel.generateContentStream(audioStream);
      // _isSessionActive = true;

      // Setup audio output
      // _audioSource = SoLoud.instance.setBufferStream(
      //   bufferingType: BufferingType.released,
      //   maxBufferSizeDuration: const Duration(seconds: 5),
      //   bufferingTimeNeeds: 0,
      //   onBuffering: (isBuffering, handle, time) {
      //     log('Audio buffering: $isBuffering, Time: $time');
      //   },
      // );
      // _soundHandle = await SoLoud.instance.play(_audioSource!);

      // Start processing messages from the AI
      // _sessionSubscription = responseStream.listen(
      //   _handleLiveServerMessage,
      //   onError: (e) {
      //     log('Error in AI response stream: $e');
      //   },
      //   onDone: () {
      //     log('AI response stream closed.');
      //   }
      // );
      
      _isConversationActive = true;
      _isLoading = false;
      notifyListeners();

      // Add initial message
      _addMessage(AudioChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: 'Voice chat started! I\'m ready to help with your fitness journey.',
        isUser: false,
        timestamp: DateTime.now(),
        isAudio: true,
      ));

      log('Audio conversation started successfully');
    } catch (e) {
      log('Error starting conversation: $e');
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> stopConversation() async {
    try {
      // Stop recording
      // await _recorder.stop();

      // Stop audio playback
      // if (_audioSource != null && _soundHandle != null) {
      //   SoLoud.instance.setDataIsEnded(_audioSource!);
      //   await SoLoud.instance.stop(_soundHandle!);
      // }

      // End live session
      // if (_sessionSubscription != null) {
      //   await _sessionSubscription!.cancel();
      //   _sessionSubscription = null;
      // }

      _isSessionActive = false;
      _isConversationActive = false;
      notifyListeners();

      // Add final message
      _addMessage(AudioChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: 'Voice chat ended. Feel free to start again anytime!',
        isUser: false,
        timestamp: DateTime.now(),
      ));

      log('Audio conversation stopped successfully');
    } catch (e) {
      log('Error stopping conversation: $e');
      rethrow;
    }
  }

  // Future<Stream<Uint8List>> _startRecordingStream() async {
  //   var recordConfig = const RecordConfig(
  //     encoder: AudioEncoder.pcm16bits,
  //     sampleRate: 24000,
  //     numChannels: 1,
  //     echoCancel: true,
  //     noiseSuppress: true,
  //     androidConfig: AndroidRecordConfig(
  //       audioSource: AndroidAudioSource.voiceCommunication,
  //     ),
  //     iosConfig: IosRecordConfig(
  //       categoryOptions: [IosAudioCategoryOption.defaultToSpeaker],
  //     ),
  //   );
  //   return await _recorder.startStream(recordConfig);
  // }

  // Future<void> _handleLiveServerMessage(GenerateContentResponse response) async {
  //   final candidate = response.candidates?.firstOrNull;
  //   if (candidate != null) {
  //     for (final part in candidate.content?.parts ?? []) {
  //       if (part is TextPart) {
  //         _addMessage(AudioChatMessage(
  //           id: DateTime.now().millisecondsSinceEpoch.toString(),
  //           text: part.text,
  //           isUser: false,
  //           timestamp: DateTime.now(),
  //         ));
  //       } else if (part is BlobPart) {
  //         if (part.mimeType.startsWith('audio') && _audioSource != null) {
  //           SoLoud.instance.addAudioDataStream(_audioSource!, part.data);
            
  //           _addMessage(AudioChatMessage(
  //             id: DateTime.now().millisecondsSinceEpoch.toString(),
  //             text: '[Audio Response]',
  //             isUser: false,
  //             timestamp: DateTime.now(),
  //             isAudio: true,
  //             audioData: part.data,
  //           ));
  //         }
  //       }
  //     }
  //   }
  // }

  void _addMessage(AudioChatMessage message) {
    _messages.add(message);
    notifyListeners();
  }

  Future<void> sendTextMessage(String text) async {
    if (!_isSessionActive) {
      throw Exception('Audio session not active');
    }

    // Add user message
    _addMessage(AudioChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: text,
      isUser: true,
      timestamp: DateTime.now(),
    ));

    // Send to AI
    log("WARN: sendTextMessage needs to be re-implemented for the new FirebaseAI SDK's streaming model.");
  }

  void clearChat() {
    _messages.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    // _recorder.dispose();
    // _sessionSubscription?.cancel();
    _messages.clear();
    // SoLoud.instance.deinit();
    super.dispose();
  }
}