import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/genkit_models.dart';

class WorkoutAgentService {
  final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  
  WorkoutAgentService() {
    // Comment out emulator for production use
    // Uncomment the following lines only for local development
    // if (kDebugMode) {
    //   _functions.useFunctionsEmulator('localhost', 5001);
    // }
  }
  
  // Complete onboarding flow
  Future<Map<String, dynamic>> completeUserOnboarding() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User must be authenticated');
      }
      
      final HttpsCallable callable = _functions.httpsCallable('completeOnboarding');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      if (data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Onboarding failed');
      }
    } on FirebaseFunctionsException catch (e) {
      debugPrint('Firebase Functions error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error: $e');
      rethrow;
    }
  }
  
  // Generate fitness guide
  Future<String> generateFitnessGuide() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('generateFitnessGuide');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      if (data['success'] == true) {
        return data['guideId'];
      } else {
        throw Exception(data['message'] ?? 'Failed to generate guide');
      }
    } catch (e) {
      debugPrint('Error generating fitness guide: $e');
      rethrow;
    }
  }
  
  // Create first workout
  Future<String> createFirstWorkout({String? guideId}) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('createFirstWorkout');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
        'guideId': guideId,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      if (data['success'] == true) {
        return data['workoutId'];
      } else {
        throw Exception(data['message'] ?? 'Failed to create workout');
      }
    } catch (e) {
      debugPrint('Error creating first workout: $e');
      rethrow;
    }
  }
  
  // Get exercise recommendation
  Future<ExerciseRecommendation> getNextExercise({bool saveWorkout = false}) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('recommendNextExercise');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
        'saveWorkout': saveWorkout,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      return ExerciseRecommendation(
        exercise: GenkitExercise.fromJson(data['recommendation']),
        reason: data['reason'],
        alternatives: (data['alternatives'] as List<dynamic>)
            .map((e) => GenkitExercise.fromJson(e))
            .toList(),
        musclesWorked: List<String>.from(data['targetMuscles']),
      );
    } catch (e) {
      debugPrint('Error getting exercise recommendation: $e');
      rethrow;
    }
  }
  
  // Analyze last workout
  Future<WorkoutAnalysis> analyzeLastWorkout({String? workoutId}) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('analyzeLastWorkout');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
        'workoutId': workoutId,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      return WorkoutAnalysis(
        overallPerformance: data['overallPerformance'],
        strengths: List<String>.from(data['strengths']),
        areasForImprovement: List<String>.from(data['areasForImprovement']),
        recoveryStatus: data['recoveryStatus'],
        nextWorkoutRecommendation: data['nextWorkoutRecommendation'],
      );
    } catch (e) {
      debugPrint('Error analyzing workout: $e');
      rethrow;
    }
  }
  
  // Generate next workout
  Future<String> generateNextWorkout({
    bool skipRecoveryCheck = false,
    List<String>? targetMuscles,
    String? workoutType,
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('generateNextWorkout');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
        'skipRecoveryCheck': skipRecoveryCheck,
        'targetMuscles': targetMuscles,
        'workoutType': workoutType,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      if (data['success'] == true) {
        return data['workoutId'];
      } else {
        throw Exception(data['message'] ?? 'Failed to generate workout');
      }
    } catch (e) {
      debugPrint('Error generating next workout: $e');
      rethrow;
    }
  }
}

// Fitness Chat Service
class FitnessChatService {
  final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  String? _conversationId;
  
  FitnessChatService() {
    // Comment out emulator for production use
    // Uncomment the following lines only for local development
    // if (kDebugMode) {
    //   _functions.useFunctionsEmulator('localhost', 5001);
    // }
  }
  
  Future<ChatResponse> sendMessage(String message) async {
    try {
      debugPrint('FitnessChatService: Checking authentication...');
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('FitnessChatService: No authenticated user found');
        throw Exception('Please sign in to use the fitness chat');
      }
      
      // Ensure user is properly authenticated and token is fresh
      await user.reload(); // Refresh user state
      final idToken = await user.getIdToken(true); // Force refresh token
      debugPrint('FitnessChatService: User authenticated as ${user.uid}');
      debugPrint('FitnessChatService: ID token obtained: ${idToken?.substring(0, 20) ?? "null"}...');
      debugPrint('FitnessChatService: Calling fitnessChat function...');
      
      final HttpsCallable callable = _functions.httpsCallable(
        'fitnessChat',
        options: HttpsCallableOptions(
          timeout: const Duration(seconds: 60),
        ),
      );
      
      final Map<String, dynamic> requestData = {
        'userId': user.uid,
        'message': message,
      };
      
      if (_conversationId != null) {
        requestData['conversationId'] = _conversationId;
      }
      
      debugPrint('FitnessChatService: Request data: $requestData');
      
      final HttpsCallableResult result = await callable.call(requestData);
      
      debugPrint('FitnessChatService: Response received');
      final data = result.data as Map<String, dynamic>;
      debugPrint('FitnessChatService: Response data: $data');
      
      // Check if response has expected structure
      if (!data.containsKey('response')) {
        throw Exception('Invalid response format from server');
      }
      
      // Update conversation ID for continuity
      if (data.containsKey('conversationId')) {
        _conversationId = data['conversationId'];
      }
      
      return ChatResponse(
        response: data['response'] ?? 'No response received',
        conversationId: data['conversationId'] ?? '',
        suggestions: data['suggestions'] != null 
            ? List<String>.from(data['suggestions'])
            : null,
      );
    } on FirebaseFunctionsException catch (e) {
      debugPrint('FitnessChatService: Firebase Functions error - Code: ${e.code}, Message: ${e.message}, Details: ${e.details}');
      
      // If authentication failed, try to refresh and retry once
      if (e.code == 'unauthenticated') {
        try {
          debugPrint('FitnessChatService: Authentication failed, attempting to refresh...');
          final user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            await user.reload();
            await user.getIdToken(true);
            debugPrint('FitnessChatService: Token refreshed, retrying...');
            
            // Retry the call once
            final HttpsCallable callable = _functions.httpsCallable(
              'fitnessChat',
              options: HttpsCallableOptions(
                timeout: const Duration(seconds: 60),
              ),
            );
            
            final Map<String, dynamic> requestData = {
              'userId': user.uid,
              'message': message,
            };
            
            if (_conversationId != null) {
              requestData['conversationId'] = _conversationId;
            }
            
            final HttpsCallableResult result = await callable.call(requestData);
            final data = result.data as Map<String, dynamic>;
            
            if (data.containsKey('conversationId')) {
              _conversationId = data['conversationId'];
            }
            
            return ChatResponse(
              response: data['response'] ?? 'No response received',
              conversationId: data['conversationId'] ?? '',
              suggestions: data['suggestions'] != null 
                  ? List<String>.from(data['suggestions'])
                  : null,
            );
          }
        } catch (retryError) {
          debugPrint('FitnessChatService: Retry also failed: $retryError');
        }
      }
      
      // Handle Firebase-specific errors
      switch (e.code) {
        case 'unauthenticated':
          throw Exception('Authentication failed. Please sign out and sign in again.');
        case 'permission-denied':
          throw Exception('You do not have permission to use this feature');
        case 'invalid-argument':
          throw Exception('Invalid message format: ${e.message}');
        case 'not-found':
          throw Exception('Chat function not found. Please check if it\'s deployed.');
        case 'unavailable':
          throw Exception('Service temporarily unavailable. Please try again later.');
        case 'deadline-exceeded':
          throw Exception('Request timed out. Please try again.');
        default:
          throw Exception('Chat error: ${e.code} - ${e.message}');
      }
    } catch (e, stackTrace) {
      debugPrint('FitnessChatService: Unexpected error: $e');
      debugPrint('FitnessChatService: Stack trace: $stackTrace');
      
      if (e.toString().contains('Could not connect to the server')) {
        throw Exception('Cannot connect to chat service. Please check your internet connection.');
      }
      
      throw Exception('Failed to send message: ${e.toString()}');
    }
  }
  
  void startNewConversation() {
    _conversationId = null;
  }
  
  String? get conversationId => _conversationId;
}