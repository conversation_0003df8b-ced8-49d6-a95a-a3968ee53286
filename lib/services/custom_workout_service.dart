import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/workout_model.dart';
import '../models/exercise_model.dart';

class CustomWorkoutService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Create a new custom workout
  Future<String?> createCustomWorkout(WorkoutPlanModel workout) async {
    try {
      DocumentReference docRef = await _firestore
          .collection('userWorkouts')
          .add(workout.toJson());
      return docRef.id;
    } catch (e) {
      print('Create custom workout error: $e');
      return null;
    }
  }

  // Update an existing custom workout
  Future<bool> updateCustomWorkout(WorkoutPlanModel workout) async {
    try {
      await _firestore
          .collection('userWorkouts')
          .doc(workout.id)
          .update(workout.toJson());
      return true;
    } catch (e) {
      print('Update custom workout error: $e');
      return false;
    }
  }

  // Delete a custom workout
  Future<bool> deleteCustomWorkout(String workoutId) async {
    try {
      await _firestore
          .collection('userWorkouts')
          .doc(workoutId)
          .delete();
      return true;
    } catch (e) {
      print('Delete custom workout error: $e');
      return false;
    }
  }

  // Get all custom workouts for a user (optimized with server-side sorting)
  Future<List<WorkoutPlanModel>> getUserCustomWorkouts(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('userWorkouts')
          .where('isCustom', isEqualTo: true)
          .where('createdBy', isEqualTo: userId)
          .orderBy('createdAt', descending: true) // Server-side sorting
          .get();

      return snapshot.docs
          .map((doc) => WorkoutPlanModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get user custom workouts error: $e');
      return [];
    }
  }

  // Duplicate a workout (pre-built or custom)
  Future<String?> duplicateWorkout(WorkoutPlanModel originalWorkout, String userId) async {
    try {
      WorkoutPlanModel duplicatedWorkout = WorkoutPlanModel(
        id: '',
        name: '${originalWorkout.name} (Copy)',
        description: originalWorkout.description,
        exercises: originalWorkout.exercises,
        duration: originalWorkout.duration,
        difficulty: originalWorkout.difficulty,
        category: originalWorkout.category,
        imageUrl: originalWorkout.imageUrl,
        isCustom: true,
        createdBy: userId,
        createdAt: DateTime.now(),
        isPublic: false,
      );

      return await createCustomWorkout(duplicatedWorkout);
    } catch (e) {
      print('Duplicate workout error: $e');
      return null;
    }
  }

  // Search custom workouts
  Future<List<WorkoutPlanModel>> searchCustomWorkouts(String userId, String query) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('userWorkouts')
          .where('isCustom', isEqualTo: true)
          .where('createdBy', isEqualTo: userId)
          .get();

      List<WorkoutPlanModel> allWorkouts = snapshot.docs
          .map((doc) => WorkoutPlanModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();

      // Filter by name or description containing query
      String lowercaseQuery = query.toLowerCase();
      return allWorkouts.where((workout) =>
          workout.name.toLowerCase().contains(lowercaseQuery) ||
          workout.description.toLowerCase().contains(lowercaseQuery)
      ).toList();
    } catch (e) {
      print('Search custom workouts error: $e');
      return [];
    }
  }

  // Calculate estimated workout duration based on exercises
  int calculateWorkoutDuration(List<WorkoutExercise> exercises) {
    int totalDuration = 0;
    
    for (WorkoutExercise exercise in exercises) {
      // Add exercise time (duration or estimated time for reps)
      if (exercise.duration > 0) {
        totalDuration += exercise.duration * exercise.sets;
      } else {
        // Estimate 2 seconds per rep
        totalDuration += (exercise.reps * 2 * exercise.sets);
      }
      
      // Add rest time between sets (exclude last set)
      if (exercise.sets > 1) {
        totalDuration += exercise.restTime * (exercise.sets - 1);
      }
    }
    
    // Convert to minutes and round up
    return (totalDuration / 60).ceil();
  }

  // Validate workout data
  Map<String, String> validateWorkout(WorkoutPlanModel workout) {
    Map<String, String> errors = {};

    if (workout.name.trim().isEmpty) {
      errors['name'] = 'Workout name is required';
    }

    if (workout.name.trim().length < 3) {
      errors['name'] = 'Workout name must be at least 3 characters';
    }

    if (workout.exercises.isEmpty) {
      errors['exercises'] = 'At least one exercise is required';
    }

    if (workout.description.trim().isEmpty) {
      errors['description'] = 'Workout description is required';
    }

    return errors;
  }

  // Get workout statistics
  Future<Map<String, dynamic>> getWorkoutStats(String workoutId, {String? userId}) async {
    try {
      Query query = _firestore
          .collection('workoutHistory')
          .where('workoutPlanId', isEqualTo: workoutId)
          .where('isCompleted', isEqualTo: true);
      
      // Add userId filter if provided
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }
      
      QuerySnapshot sessionsSnapshot = await query.get();

      int totalSessions = sessionsSnapshot.docs.length;
      
      if (totalSessions == 0) {
        return {
          'totalSessions': 0,
          'averageDuration': 0,
          'lastCompleted': null,
        };
      }

      List<int> durations = [];
      DateTime? lastCompleted;

      for (var doc in sessionsSnapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        if (data['endTime'] != null && data['startTime'] != null) {
          int duration = data['endTime'] - data['startTime'];
          durations.add(duration);
        }
        
        DateTime sessionDate = DateTime.fromMillisecondsSinceEpoch(data['startTime']);
        if (lastCompleted == null || sessionDate.isAfter(lastCompleted)) {
          lastCompleted = sessionDate;
        }
      }

      int averageDuration = durations.isNotEmpty 
          ? (durations.reduce((a, b) => a + b) / durations.length / 1000 / 60).round()
          : 0;

      return {
        'totalSessions': totalSessions,
        'averageDuration': averageDuration,
        'lastCompleted': lastCompleted,
      };
    } catch (e) {
      print('Get workout stats error: $e');
      return {
        'totalSessions': 0,
        'averageDuration': 0,
        'lastCompleted': null,
      };
    }
  }
}