import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class AuthTestService {
  final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  
  AuthTestService() {
    // Comment out emulator for production use
    // if (kDebugMode) {
    //   _functions.useFunctionsEmulator('localhost', 5001);
    // }
  }
  
  Future<Map<String, dynamic>> testAuthentication() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        return {
          'success': false,
          'error': 'No authenticated user',
          'details': 'User must be signed in'
        };
      }
      
      // Get fresh token
      await user.reload();
      final idToken = await user.getIdToken(true);
      
      debugPrint('AuthTest: User UID: ${user.uid}');
      debugPrint('AuthTest: User Email: ${user.email}');
      debugPrint('AuthTest: Token length: ${idToken?.length ?? 0}');
      
      // Test with fitnessChat function
      final HttpsCallable callable = _functions.httpsCallable(
        'fitnessChat',
        options: HttpsCallableOptions(
          timeout: const Duration(seconds: 30),
        ),
      );
      
      final result = await callable.call({
        'userId': user.uid,
        'message': 'Hello, this is a test message',
      });
      
      return {
        'success': true,
        'user_uid': user.uid,
        'user_email': user.email,
        'token_exists': idToken != null,
        'function_response': result.data,
      };
      
    } on FirebaseFunctionsException catch (e) {
      debugPrint('AuthTest: Functions error - ${e.code}: ${e.message}');
      return {
        'success': false,
        'error': 'Firebase Functions Error',
        'code': e.code,
        'message': e.message,
        'details': e.details,
      };
    } catch (e) {
      debugPrint('AuthTest: Unexpected error - $e');
      return {
        'success': false,
        'error': 'Unexpected Error',
        'details': e.toString(),
      };
    }
  }
} 