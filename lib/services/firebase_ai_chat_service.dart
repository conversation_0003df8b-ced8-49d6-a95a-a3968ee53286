import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ChatMessage {
  final String id;
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isLoading;

  ChatMessage({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isLoading = false,
  });

  ChatMessage copyWith({
    String? id,
    String? text,
    bool? isUser,
    DateTime? timestamp,
    bool? isLoading,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class FirebaseAIChatService extends ChangeNotifier {
  static final FirebaseAIChatService _instance = FirebaseAIChatService._internal();
  factory FirebaseAIChatService() => _instance;
  FirebaseAIChatService._internal();

  GenerativeModel? _model;
  ChatSession? _chatSession;
  final List<ChatMessage> _messages = [];
  bool _isInitialized = false;
  bool _isLoading = false;

  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;

  Future<void> initialize() async {
    try {
      // Get API key from .env file
      final apiKey = dotenv.env['GOOGLE_GENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('GOOGLE_GENAI_API_KEY not found in .env file');
      }
      
      _model = GenerativeModel(
        model: 'gemini-2.0-flash-exp',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
      );

      _chatSession = _model!.startChat();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing AI Chat: $e');
      debugPrint('Make sure GOOGLE_GENAI_API_KEY is set in your .env file');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<void> sendMessage(String message) async {
    if (!_isInitialized || _model == null || _chatSession == null) {
      await initialize();
      if (!_isInitialized) {
        throw Exception('Failed to initialize AI chat service');
      }
    }

    final userMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: message,
      isUser: true,
      timestamp: DateTime.now(),
    );

    _messages.add(userMessage);
    notifyListeners();

    final loadingMessage = ChatMessage(
      id: '${DateTime.now().millisecondsSinceEpoch}_loading',
      text: '',
      isUser: false,
      timestamp: DateTime.now(),
      isLoading: true,
    );

    _messages.add(loadingMessage);
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _chatSession!.sendMessage(Content.text(message));
      final responseText = response.text ?? 'Sorry, I couldn\'t generate a response.';

      _messages.removeLast();
      
      final aiMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: responseText,
        isUser: false,
        timestamp: DateTime.now(),
      );

      _messages.add(aiMessage);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error sending message: $e');
      _messages.removeLast();
      
      final errorMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: 'Sorry, I encountered an error. Please try again.',
        isUser: false,
        timestamp: DateTime.now(),
      );

      _messages.add(errorMessage);
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearChat() {
    _messages.clear();
    _chatSession = _model?.startChat();
    notifyListeners();
  }

  @override
  void dispose() {
    _messages.clear();
    _chatSession = null;
    _model = null;
    _isInitialized = false;
    super.dispose();
  }
}