import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_profile_model.dart';
import '../services/auth_service.dart';
import '../services/comprehensive_onboarding_service.dart';

class GenKitWorkoutService {
  static const String _functionName = 'generateAndSaveWorkout';
  static const String _baseUrl = 'https://generateandsaveworkout-hidgimzz2q-uc.a.run.app';
  
  final AuthService _authService = AuthService();
  final ComprehensiveOnboardingService _onboardingService = ComprehensiveOnboardingService();
  final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(region: 'us-central1');

  /// Generate a workout using the deployed GenKit function
  Future<Map<String, dynamic>?> generateWorkout({
    required String workoutType,
    required int durationMinutes,
    required String fitnessLevel,
    required List<String> equipment,
    required List<String> targetMuscleGroups,
    String? additionalInstructions,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get user profile for personalization
      final userProfile = await _getUserContext(user.uid);

      // Try different request formats for GenKit
      final requestFormats = [
        // Firebase Functions format
        {
          'data': {
            'workoutType': workoutType,
            'durationMinutes': durationMinutes,
            'fitnessLevel': fitnessLevel,
            'equipment': equipment,
            'targetMuscleGroups': targetMuscleGroups,
            'additionalInstructions': additionalInstructions,
            'userProfile': userProfile,
            'userId': user.uid,
          }
        },
        // Direct format
        {
          'workoutType': workoutType,
          'durationMinutes': durationMinutes,
          'fitnessLevel': fitnessLevel,
          'equipment': equipment,
          'targetMuscleGroups': targetMuscleGroups,
          'additionalInstructions': additionalInstructions,
          'userProfile': userProfile,
          'userId': user.uid,
        },
      ];

      final endpoints = [
        '$_baseUrl/generateandsaveworkout',
        _baseUrl,
      ];

      print('Sending workout generation request to GenKit function...');
      
      for (String endpoint in endpoints) {
        for (Map<String, dynamic> requestBody in requestFormats) {
          try {
            print('Trying endpoint: $endpoint');
            print('Request format: ${jsonEncode(requestBody)}');
            
            final response = await http.post(
              Uri.parse(endpoint),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'AgenticFit-Flutter/1.0',
              },
              body: jsonEncode(requestBody),
            ).timeout(const Duration(seconds: 30));

            print('GenKit response status: ${response.statusCode}');
            print('GenKit response body: ${response.body}');

            if (response.statusCode == 200) {
              final responseData = jsonDecode(response.body);
              print('✅ Workout generated successfully via GenKit');
              
              // Handle different response formats
              return responseData['result'] ?? responseData['data'] ?? responseData;
            }
          } catch (e) {
            print('Error with endpoint $endpoint: $e');
            continue;
          }
        }
      }
      
      throw Exception('All GenKit endpoints failed');
    } catch (e) {
      print('Error calling GenKit function: $e');
      return null;
    }
  }

  /// Generate a quick workout with minimal parameters
  Future<Map<String, dynamic>?> generateQuickWorkout({
    required String workoutType,
    required int durationMinutes,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get user profile to determine fitness level and equipment
      final onboarding = await _onboardingService.getComprehensiveOnboarding(user.uid);
      
      String fitnessLevel = 'beginner';
      List<String> equipment = ['bodyweight'];
      
      if (onboarding != null) {
        // Determine fitness level from cardio and weightlifting levels
        final avgLevel = (onboarding.fitnessLevel.cardioLevel + onboarding.fitnessLevel.weightliftingLevel) / 2;
        if (avgLevel >= 0.8) {
          fitnessLevel = 'advanced';
        } else if (avgLevel >= 0.5) {
          fitnessLevel = 'intermediate';
        }

        // Set equipment based on available environments
        if (onboarding.workoutPreferences.environments.contains(WorkoutEnvironment.largeGym)) {
          equipment = ['dumbbells', 'barbells', 'machines', 'bodyweight'];
        } else if (onboarding.workoutPreferences.environments.contains(WorkoutEnvironment.smallGym)) {
          equipment = ['dumbbells', 'bodyweight', 'resistance_bands'];
        } else if (onboarding.workoutPreferences.environments.contains(WorkoutEnvironment.homeBasic)) {
          equipment = ['dumbbells', 'bodyweight', 'resistance_bands'];
        } else {
          equipment = ['bodyweight'];
        }
      }

      return await generateWorkout(
        workoutType: workoutType,
        durationMinutes: durationMinutes,
        fitnessLevel: fitnessLevel,
        equipment: equipment,
        targetMuscleGroups: _getTargetMuscleGroups(workoutType),
      );
    } catch (e) {
      print('Error generating quick workout: $e');
      return null;
    }
  }

  /// Generate a personalized workout based on user goals
  Future<Map<String, dynamic>?> generatePersonalizedWorkout() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final onboarding = await _onboardingService.getComprehensiveOnboarding(user.uid);
      if (onboarding == null) {
        throw Exception('User onboarding data not found');
      }

      // Determine workout type from fitness goals
      String workoutType = 'full_body';
      if (onboarding.fitnessGoals.isNotEmpty) {
        final primaryGoal = onboarding.fitnessGoals.first.type;
        switch (primaryGoal) {
          case FitnessGoalType.buildMuscle:
            workoutType = 'strength';
            break;
          case FitnessGoalType.weightLoss:
            workoutType = 'cardio';
            break;
          case FitnessGoalType.increaseStamina:
            workoutType = 'endurance';
            break;
          case FitnessGoalType.increaseStrength:
            workoutType = 'strength';
            break;
          case FitnessGoalType.sportSpecific:
            workoutType = 'functional';
            break;
          case FitnessGoalType.healthOptimization:
            workoutType = 'full_body';
            break;
          default:
            workoutType = 'full_body';
        }
      }

      final durationMinutes = onboarding.workoutPreferences.workoutDurationMinutes ?? 30;

      return await generateQuickWorkout(
        workoutType: workoutType,
        durationMinutes: durationMinutes,
      );
    } catch (e) {
      print('Error generating personalized workout: $e');
      return null;
    }
  }

  /// Call GenKit function with chat message context using Firebase Callable Function
  Future<String?> generateWorkoutFromChat(String message) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final userProfile = await _getUserContext(user.uid);

      print('🚀 Calling Firebase Callable Function: $_functionName');
      
      // Call Firebase Callable Function
      final callable = _functions.httpsCallable(_functionName);
      
      final requestData = {
        'message': message,
        'userProfile': userProfile,
        'userId': user.uid,
        'requestType': 'chat_workout_generation',
      };

      print('📤 Request data: ${jsonEncode(requestData)}');

      final result = await callable.call(requestData);
      
      print('📨 Function result: ${result.data}');

      if (result.data != null) {
        final data = result.data;
        
        // Try different response formats from GenKit
        String? response = data['response'] ?? 
                          data['message'] ??
                          data['result']?['response'] ??
                          data['result']?['message'] ??
                          data.toString();
        
        if (response != null && response.isNotEmpty) {
          print('✅ GenKit callable function success');
          return response;
        }
      }
      
      print('⚠️ GenKit callable function returned empty response');
      return null;
    } catch (e) {
      print('❌ Error calling GenKit callable function: $e');
      
      // Fallback to HTTP if callable fails
      return await _fallbackToHttpCall(message);
    }
  }
  
  /// Fallback to HTTP call if callable function fails
  Future<String?> _fallbackToHttpCall(String message) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return null;

      final userProfile = await _getUserContext(user.uid);
      
      print('🔄 Falling back to HTTP call...');
      
      final requestBody = {
        'data': {
          'message': message,
          'userProfile': userProfile,
          'userId': user.uid,
          'requestType': 'chat_workout_generation',
        }
      };

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'AgenticFit-Flutter/1.0',
        },
        body: jsonEncode(requestBody),
      ).timeout(const Duration(seconds: 30));

      print('HTTP fallback status: ${response.statusCode}');
      print('HTTP fallback body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return responseData['response'] ?? responseData['message'];
      }
      
      return null;
    } catch (e) {
      print('❌ HTTP fallback also failed: $e');
      return null;
    }
  }

  /// Get user context for GenKit function
  Future<Map<String, dynamic>> _getUserContext(String userId) async {
    final context = <String, dynamic>{};
    
    try {
      final onboarding = await _onboardingService.getComprehensiveOnboarding(userId);
      if (onboarding != null) {
        context['profile'] = {
          'name': onboarding.profile.name,
          'age': onboarding.profile.age,
          'gender': onboarding.profile.gender?.name,
          'heightFeet': onboarding.profile.heightFeet,
          'weightLbs': onboarding.profile.weightLbs,
        };
        
        context['fitnessGoals'] = onboarding.fitnessGoals.map((goal) => {
          'type': goal.type.name,
          'priority': goal.priority,
          'sportActivity': goal.sportActivity,
        }).toList();
        
        context['fitnessLevel'] = {
          'cardioLevel': onboarding.fitnessLevel.cardioLevel,
          'weightliftingLevel': onboarding.fitnessLevel.weightliftingLevel,
          'exercisesToAvoid': onboarding.fitnessLevel.exercisesToAvoid,
        };
        
        context['workoutPreferences'] = {
          'workoutsPerWeek': onboarding.workoutPreferences.workoutsPerWeek,
          'workoutDurationMinutes': onboarding.workoutPreferences.workoutDurationMinutes,
          'environments': onboarding.workoutPreferences.environments.map((e) => e.name).toList(),
          'additionalNotes': onboarding.workoutPreferences.additionalNotes,
        };
        
        context['personalCoachNotes'] = onboarding.personalCoachNotes;
      }
      
      final userData = await _authService.getUserData(userId);
      if (userData != null) {
        context['stats'] = userData.stats;
        context['preferences'] = userData.preferences;
      }
    } catch (e) {
      print('Error building user context: $e');
    }
    
    return context;
  }

  /// Get target muscle groups based on workout type
  List<String> _getTargetMuscleGroups(String workoutType) {
    switch (workoutType.toLowerCase()) {
      case 'upper_body':
        return ['chest', 'back', 'shoulders', 'arms'];
      case 'lower_body':
        return ['quads', 'hamstrings', 'glutes', 'calves'];
      case 'push':
        return ['chest', 'shoulders', 'triceps'];
      case 'pull':
        return ['back', 'biceps'];
      case 'legs':
        return ['quads', 'hamstrings', 'glutes', 'calves'];
      case 'abs':
      case 'core':
        return ['abs', 'core'];
      case 'cardio':
        return ['cardiovascular'];
      default:
        return ['full_body'];
    }
  }

  /// Test the GenKit function connection
  Future<bool> testConnection() async {
    try {
      // Test different endpoints and methods
      final testCases = [
        {'url': _baseUrl, 'method': 'GET'},
        {'url': _baseUrl, 'method': 'POST'},
        {'url': '$_baseUrl/generateandsaveworkout', 'method': 'POST'},
        {'url': '$_baseUrl/health', 'method': 'GET'},
      ];

      for (var testCase in testCases) {
        try {
          print('Testing ${testCase['method']} ${testCase['url']}');
          
          http.Response response;
          if (testCase['method'] == 'GET') {
            response = await http.get(
              Uri.parse(testCase['url']!),
              headers: {
                'Accept': 'application/json',
                'User-Agent': 'AgenticFit-Flutter/1.0',
              },
            ).timeout(const Duration(seconds: 10));
          } else {
            response = await http.post(
              Uri.parse(testCase['url']!),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'AgenticFit-Flutter/1.0',
              },
              body: jsonEncode({'data': {'test': true}}),
            ).timeout(const Duration(seconds: 10));
          }
          
          print('${testCase['method']} ${testCase['url']} - Status: ${response.statusCode}');
          print('Response: ${response.body}');
          
          // Consider various success codes
          if ([200, 201, 400, 405].contains(response.statusCode)) {
            return true;
          }
        } catch (e) {
          print('${testCase['method']} ${testCase['url']} - Error: $e');
        }
      }
      
      return false;
    } catch (e) {
      print('GenKit function connection test failed: $e');
      return false;
    }
  }
}