import 'package:firebase_vertexai/firebase_vertexai.dart';

// Tool to get user's fitness profile and preferences
final getUserProfileTool = FunctionDeclaration(
  'getUserProfile',
  'Get the user\'s complete fitness profile including goals, preferences, fitness level, and personal information to provide personalized advice.',
  parameters: {},
);

// Tool to get user's workout history
final getUserWorkoutHistoryTool = FunctionDeclaration(
  'getUserWorkoutHistory',
  'Get the user\'s recent workout history to understand their training patterns and progress.',
  parameters: {
    'limit': Schema.integer(
      description: 'Number of recent workouts to retrieve (default: 10, max: 50)',
      nullable: true,
    ),
  },
);

// Tool to generate a personalized workout
final generateWorkoutTool = FunctionDeclaration(
  'generateWorkout',
  'Generate a personalized workout plan based on user preferences, goals, and fitness level.',
  parameters: {
    'workoutType': Schema.string(
      description: '''The type of workout to generate. Options include:
        - strength: Weight training and resistance exercises
        - cardio: Cardiovascular and endurance training  
        - hiit: High-intensity interval training
        - yoga: Yoga and flexibility training
        - fullbody: Complete full-body workout
        - push: Push muscles (chest, shoulders, triceps)
        - pull: Pull muscles (back, biceps)
        - legs: Lower body focused workout
        - core: Core and abs focused workout''',
    ),
    'duration': Schema.integer(
      description: 'Desired workout duration in minutes (15-90 minutes)',
    ),
    'equipment': Schema.string(
      description: '''Available equipment. Options:
        - gym: Full gym access with all equipment
        - home_basic: Basic home equipment (dumbbells, resistance bands)
        - bodyweight: No equipment needed
        - minimal: Very basic equipment (one set of dumbbells)''',
    ),
    'difficulty': Schema.string(
      description: 'Workout difficulty level: beginner, intermediate, or advanced',
    ),
  },
);

// Tool to save a workout to user's library
final saveWorkoutTool = FunctionDeclaration(
  'saveWorkout',
  'Save a generated workout to the user\'s personal workout library for future use.',
  parameters: {
    'workoutName': Schema.string(
      description: 'Name for the workout (e.g., "Morning HIIT", "Leg Day Blast")',
    ),
    'workoutData': Schema.string(
      description: 'JSON string containing the complete workout data including exercises, sets, reps, and instructions',
    ),
  },
);

// Tool to get exercise recommendations
final getExerciseRecommendationsTool = FunctionDeclaration(
  'getExerciseRecommendations',
  'Get exercise recommendations based on muscle groups, equipment, or specific goals.',
  parameters: {
    'muscleGroup': Schema.string(
      description: '''Target muscle group. Options:
        - chest: Chest muscles
        - back: Back muscles  
        - shoulders: Shoulder muscles
        - arms: Biceps and triceps
        - legs: Quadriceps, hamstrings, glutes
        - core: Abdominal and core muscles
        - cardio: Cardiovascular exercises
        - fullbody: Full body exercises''',
      nullable: true,
    ),
    'equipment': Schema.string(
      description: 'Available equipment (same options as generateWorkout)',
      nullable: true,
    ),
    'count': Schema.integer(
      description: 'Number of exercise recommendations to return (1-10)',
      nullable: true,
    ),
  },
);

// Tool to track workout progress
final trackWorkoutProgressTool = FunctionDeclaration(
  'trackWorkoutProgress',
  'Track and analyze the user\'s workout progress over time.',
  parameters: {
    'timeframe': Schema.string(
      description: 'Time period to analyze: week, month, quarter, or year',
    ),
    'metric': Schema.string(
      description: '''Progress metric to focus on:
        - frequency: Workout frequency and consistency
        - duration: Total workout time
        - calories: Calories burned
        - strength: Strength progression (weights, reps)
        - overall: Overall fitness progress summary''',
    ),
  },
);

// Tool to get nutrition advice
final getNutritionAdviceTool = FunctionDeclaration(
  'getNutritionAdvice',
  'Provide personalized nutrition advice based on fitness goals and current activity level.',
  parameters: {
    'goal': Schema.string(
      description: '''Nutrition goal:
        - weight_loss: Advice for losing weight
        - muscle_gain: Nutrition for building muscle
        - maintenance: Maintaining current weight
        - performance: Optimizing athletic performance
        - recovery: Post-workout recovery nutrition''',
    ),
    'mealType': Schema.string(
      description: 'Specific meal type: breakfast, lunch, dinner, snack, pre_workout, post_workout',
      nullable: true,
    ),
  },
);

// Tool to set fitness goals
final setFitnessGoalTool = FunctionDeclaration(
  'setFitnessGoal',
  'Help the user set or update their fitness goals with specific, measurable targets.',
  parameters: {
    'goalType': Schema.string(
      description: '''Type of fitness goal:
        - weight_loss: Lose a specific amount of weight
        - muscle_gain: Build muscle mass
        - strength: Increase strength in specific exercises
        - endurance: Improve cardiovascular endurance
        - flexibility: Improve flexibility and mobility
        - habit: Build consistent workout habits''',
    ),
    'target': Schema.string(
      description: 'Specific target or measurement for the goal',
    ),
    'timeframe': Schema.string(
      description: 'Target timeframe to achieve the goal (e.g., "3 months", "6 weeks")',
    ),
  },
); 