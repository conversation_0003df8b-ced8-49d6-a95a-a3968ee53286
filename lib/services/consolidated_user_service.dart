import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/consolidated_user_model_fixed.dart';

class ConsolidatedUserService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static const String _userDataKey = 'cached_consolidated_user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const Duration _cacheValidityDuration = Duration(hours: 1);

  // Singleton pattern for better performance
  static final ConsolidatedUserService _instance = ConsolidatedUserService._internal();
  factory ConsolidatedUserService() => _instance;
  ConsolidatedUserService._internal() {
    _initializeAuth();
  }

  // Cache for in-memory user data
  ConsolidatedUserModel? _cachedUser;
  DateTime? _lastCacheTime;

  // Initialize auth with persistence
  Future<void> _initializeAuth() async {
    try {
      if (kIsWeb) {
        await _auth.setPersistence(Persistence.LOCAL);
        print('✅ Auth persistence set to LOCAL for web');
      }
    } catch (e) {
      print('⚠️ Auth persistence error: $e');
    }
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Check if user is logged in with optimized caching
  Future<bool> get isLoggedIn async {
    try {
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) return false;

      // Check memory cache first
      if (_cachedUser != null && _isMemoryCacheValid()) {
        return true;
      }

      // Check local storage cache
      final prefs = await SharedPreferences.getInstance();
      final cachedLoginState = prefs.getBool(_isLoggedInKey) ?? false;
      
      return cachedLoginState;
    } catch (e) {
      print('⚠️ Error checking login state: $e');
      return false;
    }
  }

  // Check if memory cache is valid
  bool _isMemoryCacheValid() {
    if (_lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < _cacheValidityDuration;
  }

  // Get user data with multi-level caching
  Future<ConsolidatedUserModel?> getUserData([String? uid]) async {
    try {
      final userId = uid ?? currentUser?.uid;
      if (userId == null || userId.isEmpty) {
        print('⚠️ Cannot get user data: UID is null or empty');
        return null;
      }

      // 1. Check memory cache first (fastest)
      if (_cachedUser != null && _cachedUser!.uid == userId && _isMemoryCacheValid()) {
        print('📱 Retrieved user data from memory cache');
        return _cachedUser;
      }

      // 2. Check local storage cache
      final cachedUser = await _getCachedUserData();
      final isLocalCacheValid = await _isLocalCacheValid();
      if (cachedUser != null && cachedUser.uid == userId && isLocalCacheValid) {
        _cachedUser = cachedUser;
        _lastCacheTime = DateTime.now();
        print('💾 Retrieved user data from local cache');
        return cachedUser;
      }

      // 3. Fetch from Firestore (slowest)
      print('🔄 Fetching user data from Firestore...');
      final userData = await _fetchUserFromFirestore(userId);
      
      if (userData != null) {
        // Validate the fetched user data has a proper UID
        if (userData.uid.isEmpty) {
          print('⚠️ Fetched user data has empty UID, fixing...');
          final fixedUserData = userData.copyWith(uid: userId);
          await _cacheUserData(fixedUserData);
          _cachedUser = fixedUserData;
          _lastCacheTime = DateTime.now();
          print('✅ User data fetched, fixed, and cached');
          return fixedUserData;
        } else {
          await _cacheUserData(userData);
          _cachedUser = userData;
          _lastCacheTime = DateTime.now();
          print('✅ User data fetched and cached');
        }
      }

      return userData;
    } catch (e) {
      print('⚠️ Error getting user data: $e');
      return null;
    }
  }

  // Fetch user data from Firestore
  Future<ConsolidatedUserModel?> _fetchUserFromFirestore(String uid) async {
    try {
      if (uid.isEmpty) {
        print('⚠️ Cannot fetch user data: UID is empty');
        return null;
      }

      final doc = await _firestore.collection('users').doc(uid).get();
      
      if (doc.exists) {
        final data = doc.data()!;
        
        // Add detailed logging to identify the problematic field
        print('🔍 Raw Firestore data structure:');
        data.forEach((key, value) {
          print('  $key: ${value.runtimeType} = $value');
          
          // Log nested structures
          if (value is Map) {
            print('    Nested map for $key:');
            value.forEach((nestedKey, nestedValue) {
              print('      $nestedKey: ${nestedValue.runtimeType} = $nestedValue');
            });
          }
        });
        
        try {
          final userData = ConsolidatedUserModel.fromJson(data);
          // Ensure the UID is set correctly
          if (userData.uid.isEmpty) {
            print('⚠️ Fetched user data has empty UID, fixing with document ID...');
            return userData.copyWith(uid: uid);
          }
          return userData;
        } catch (parseError, stackTrace) {
          print('❌ Error parsing user data:');
          print('   Error: $parseError');
          print('   Stack trace: $stackTrace');
          
          // Try parsing each section individually to identify the issue
          try {
            print('🔍 Testing individual sections:');
            
            // Test PersonalInfo
            try {
              final personalInfoData = data['personalInfo'] ?? {};
              PersonalInfo.fromJson(personalInfoData);
              print('   ✅ PersonalInfo parsed successfully');
            } catch (e) {
              print('   ❌ PersonalInfo parsing failed: $e');
            }
            
            // Test FitnessProfile
            try {
              final fitnessData = data['fitnessProfile'] ?? {};
              FitnessProfile.fromJson(fitnessData);
              print('   ✅ FitnessProfile parsed successfully');
            } catch (e) {
              print('   ❌ FitnessProfile parsing failed: $e');
            }
            
            // Test WorkoutPreferences
            try {
              final workoutData = data['workoutPreferences'] ?? {};
              WorkoutPreferences.fromJson(workoutData);
              print('   ✅ WorkoutPreferences parsed successfully');
            } catch (e) {
              print('   ❌ WorkoutPreferences parsing failed: $e');
            }
            
            // Test UserStats
            try {
              final statsData = data['stats'] ?? {};
              UserStats.fromJson(statsData);
              print('   ✅ UserStats parsed successfully');
            } catch (e) {
              print('   ❌ UserStats parsing failed: $e');
            }
            
            // Test UserPreferences
            try {
              final prefsData = data['preferences'] ?? {};
              UserPreferences.fromJson(prefsData);
              print('   ✅ UserPreferences parsed successfully');
            } catch (e) {
              print('   ❌ UserPreferences parsing failed: $e');
            }
          } catch (e) {
            print('   Error during section testing: $e');
          }
          
          // Try to identify which field caused the error
          print('🔍 Attempting to parse individual fields:');
          
          // Check each major field separately
          try {
            print('   - uid: ${data['uid']?.runtimeType} = ${data['uid']}');
          } catch (e) {
            print('   ❌ Error with uid: $e');
          }
          
          try {
            print('   - email: ${data['email']?.runtimeType} = ${data['email']}');
          } catch (e) {
            print('   ❌ Error with email: $e');
          }
          
          try {
            if (data['personalInfo'] != null) {
              print('   - personalInfo fields:');
              final pi = data['personalInfo'] as Map<String, dynamic>;
              pi.forEach((k, v) {
                print('     $k: ${v.runtimeType} = $v');
              });
            }
          } catch (e) {
            print('   ❌ Error with personalInfo: $e');
          }
          
          try {
            if (data['stats'] != null) {
              print('   - stats fields:');
              final stats = data['stats'] as Map<String, dynamic>;
              stats.forEach((k, v) {
                print('     $k: ${v.runtimeType} = $v');
              });
            }
          } catch (e) {
            print('   ❌ Error with stats: $e');
          }
          
          try {
            if (data['preferences'] != null) {
              print('   - preferences fields:');
              final prefs = data['preferences'] as Map<String, dynamic>;
              prefs.forEach((k, v) {
                print('     $k: ${v.runtimeType} = $v');
              });
            }
          } catch (e) {
            print('   ❌ Error with preferences: $e');
          }
          
          // Re-throw to maintain existing behavior
          rethrow;
        }
      }
      
      // Try to migrate from old collections if new collection doesn't exist
      return await _migrateUserData(uid);
    } catch (e) {
      print('⚠️ Error fetching user from Firestore: $e');
      return null;
    }
  }

  // Migrate user data from old collections to new consolidated collection
  Future<ConsolidatedUserModel?> _migrateUserData(String uid) async {
    try {
      print('🔄 Migrating user data from old collections...');
      
      final batch = _firestore.batch();
      final userData = <String, dynamic>{};

      // Fetch from old collections
      final futures = await Future.wait([
        _firestore.collection('users').doc(uid).get(),
        _firestore.collection('user_profiles').doc(uid).get(),
        _firestore.collection('comprehensive_onboarding').doc(uid).get(),
        _firestore.collection('fitness_goals').doc(uid).get(),
        _firestore.collection('fitness_levels').doc(uid).get(),
        _firestore.collection('workout_preferences').doc(uid).get(),
      ]);

      // Merge data from all collections
      for (final doc in futures) {
        if (doc.exists) {
          userData.addAll(doc.data() as Map<String, dynamic>);
        }
      }

      if (userData.isEmpty) return null;

      // Ensure UID is set in the data
      userData['uid'] = uid;
      
      // Create consolidated user model
      final consolidatedUser = ConsolidatedUserModel.fromLegacyUserModel(userData);
      
      // Save to consolidated users collection
      batch.set(
        _firestore.collection('users').doc(uid),
        consolidatedUser.toJson(),
      );

      await batch.commit();
      print('✅ User data migration completed');
      
      return consolidatedUser;
    } catch (e) {
      print('⚠️ Error migrating user data: $e');
      return null;
    }
  }

  // Check if local cache is valid
  Future<bool> _isLocalCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getInt(_lastSyncKey);
      if (lastSync == null) return false;
      
      final lastSyncTime = DateTime.fromMillisecondsSinceEpoch(lastSync);
      return DateTime.now().difference(lastSyncTime) < _cacheValidityDuration;
    } catch (e) {
      return false;
    }
  }

  // Get cached user data from local storage
  Future<ConsolidatedUserModel?> _getCachedUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataJson = prefs.getString(_userDataKey);
      
      if (userDataJson != null) {
        return ConsolidatedUserModel.fromJsonString(userDataJson);
      }
    } catch (e) {
      print('⚠️ Error getting cached user data: $e');
    }
    return null;
  }

  // Cache user data locally
  Future<void> _cacheUserData(ConsolidatedUserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.setString(_userDataKey, user.toJsonString()),
        prefs.setBool(_isLoggedInKey, true),
        prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch),
      ]);
      print('💾 User data cached locally');
    } catch (e) {
      print('⚠️ Error caching user data: $e');
    }
  }

  // Clear all cached data
  Future<void> _clearCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_userDataKey),
        prefs.setBool(_isLoggedInKey, false),
        prefs.remove(_lastSyncKey),
      ]);
      _cachedUser = null;
      _lastCacheTime = null;
      print('🗑️ Cleared all cached data');
    } catch (e) {
      print('⚠️ Error clearing cached data: $e');
    }
  }

  // Sign in with email and password
  Future<ConsolidatedUserModel?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        final userData = await getUserData(result.user!.uid);
        if (userData != null) {
          print('✅ User signed in successfully: ${userData.name}');
        }
        return userData;
      }
      return null;
    } catch (e) {
      print('⚠️ Sign in error: $e');
      rethrow;
    }
  }

  // Register with email and password
  Future<ConsolidatedUserModel?> registerWithEmailAndPassword(
      String email, String password, String name) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        final now = DateTime.now();
        final newUser = ConsolidatedUserModel(
          uid: result.user!.uid,
          email: email,
          displayName: name,
          personalInfo: PersonalInfo(name: name),
          fitnessProfile: FitnessProfile(),
          workoutPreferences: WorkoutPreferences(),
          stats: UserStats(),
          preferences: UserPreferences(),
          createdAt: now,
          updatedAt: now,
        );

        await _firestore
            .collection('users')
            .doc(result.user!.uid)
            .set(newUser.toJson());

        await _cacheUserData(newUser);
        _cachedUser = newUser;
        _lastCacheTime = DateTime.now();
        
        print('✅ User registered successfully: ${newUser.name}');
        return newUser;
      }
      return null;
    } catch (e) {
      print('⚠️ Registration error: $e');
      rethrow;
    }
  }

  // Update user data with optimized batch operations
  Future<bool> updateUserData(ConsolidatedUserModel user) async {
    try {
      // Validate that UID is not empty
      if (user.uid.isEmpty) {
        print('⚠️ Cannot update user data: UID is empty');
        return false;
      }

      final updatedUser = user.copyWith(updatedAt: DateTime.now());
      
      await _firestore
          .collection('users')
          .doc(user.uid)
          .update(updatedUser.toJson());

      // Update caches
      await _cacheUserData(updatedUser);
      _cachedUser = updatedUser;
      _lastCacheTime = DateTime.now();
      
      print('✅ User data updated successfully');
      return true;
    } catch (e) {
      print('⚠️ Update user data error: $e');
      print('   User UID: "${user.uid}"');
      print('   User email: "${user.email}"');
      return false;
    }
  }

  // Update personal info
  Future<bool> updatePersonalInfo(PersonalInfo personalInfo) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedUser = user.copyWith(personalInfo: personalInfo);
      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Update personal info error: $e');
      return false;
    }
  }

  // Update fitness profile
  Future<bool> updateFitnessProfile(FitnessProfile fitnessProfile) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedUser = user.copyWith(fitnessProfile: fitnessProfile);
      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Update fitness profile error: $e');
      return false;
    }
  }

  // Update workout preferences
  Future<bool> updateWorkoutPreferences(WorkoutPreferences workoutPreferences) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedUser = user.copyWith(workoutPreferences: workoutPreferences);
      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Update workout preferences error: $e');
      return false;
    }
  }

  // Update user stats
  Future<bool> updateUserStats(UserStats stats) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedUser = user.copyWith(stats: stats);
      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Update user stats error: $e');
      return false;
    }
  }

  // Update user preferences
  Future<bool> updateUserPreferences(UserPreferences preferences) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedUser = user.copyWith(preferences: preferences);
      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Update user preferences error: $e');
      return false;
    }
  }

  // Update workout stats after completing a workout
  Future<bool> updateWorkoutStats({
    required int workoutDuration,
    required double caloriesBurned,
  }) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final now = DateTime.now();
      final updatedStats = user.stats.copyWith(
        totalWorkouts: user.stats.totalWorkouts + 1,
        totalMinutes: user.stats.totalMinutes + workoutDuration,
        totalCaloriesBurned: (user.stats.totalCaloriesBurned ?? 0) + caloriesBurned,
        lastWorkoutDate: now,
        currentStreak: _calculateStreak(user.stats.lastWorkoutDate, now),
      );

      return await updateUserStats(updatedStats);
    } catch (e) {
      print('⚠️ Update workout stats error: $e');
      return false;
    }
  }

  // Calculate workout streak
  int _calculateStreak(DateTime? lastWorkout, DateTime currentWorkout) {
    if (lastWorkout == null) return 1;
    
    final daysDifference = currentWorkout.difference(lastWorkout).inDays;
    
    if (daysDifference == 1) {
      // Consecutive day
      return 1; // This will be added to existing streak
    } else if (daysDifference > 1) {
      // Streak broken
      return 1;
    } else {
      // Same day workout
      return 0; // Don't increment streak
    }
  }

  // Complete onboarding
  Future<bool> completeOnboarding({
    required PersonalInfo personalInfo,
    required FitnessProfile fitnessProfile,
    required WorkoutPreferences workoutPreferences,
  }) async {
    try {
      final user = await getUserData();
      if (user == null) return false;

      final updatedPreferences = user.preferences.copyWith(
        onboardingComplete: true,
        comprehensiveOnboardingComplete: true,
        fitnessGoalsSet: fitnessProfile.goals.isNotEmpty,
      );

      final updatedUser = user.copyWith(
        personalInfo: personalInfo,
        fitnessProfile: fitnessProfile,
        workoutPreferences: workoutPreferences,
        preferences: updatedPreferences,
      );

      return await updateUserData(updatedUser);
    } catch (e) {
      print('⚠️ Complete onboarding error: $e');
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _clearCachedData();
      await _auth.signOut();
      print('✅ User signed out successfully');
    } catch (e) {
      print('⚠️ Sign out error: $e');
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      print('⚠️ Reset password error: $e');
      return false;
    }
  }

  // Check and restore authentication state
  Future<ConsolidatedUserModel?> checkAndRestoreAuth() async {
    try {
      final firebaseUser = currentUser;
      if (firebaseUser == null) {
        print('❌ No Firebase user found');
        await _clearCachedData();
        return null;
      }

      final userData = await getUserData(firebaseUser.uid);
      if (userData != null) {
        print('✅ Authentication restored for: ${userData.name}');
        return userData;
      }

      return null;
    } catch (e) {
      print('⚠️ Error checking and restoring auth: $e');
      return null;
    }
  }

  // Force refresh user data from server
  Future<ConsolidatedUserModel?> refreshUserData() async {
    try {
      final userId = currentUser?.uid;
      if (userId == null) return null;

      // Clear caches
      _cachedUser = null;
      _lastCacheTime = null;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey);
      await prefs.remove(_lastSyncKey);

      // Fetch fresh data
      return await getUserData(userId);
    } catch (e) {
      print('⚠️ Error refreshing user data: $e');
      return null;
    }
  }

  // Get user data stream for real-time updates
  Stream<ConsolidatedUserModel?> getUserDataStream([String? uid]) {
    final userId = uid ?? currentUser?.uid;
    if (userId == null) {
      return Stream.value(null);
    }

    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        final user = ConsolidatedUserModel.fromJson(doc.data()!);
        // Update memory cache
        _cachedUser = user;
        _lastCacheTime = DateTime.now();
        return user;
      }
      return null;
    });
  }

  // Debug method to check auth state
  Future<void> debugAuthState() async {
    print('=== CONSOLIDATED AUTH DEBUG ===');
    print('Firebase user: ${currentUser?.uid}');
    print('Firebase user email: ${currentUser?.email}');
    
    final prefs = await SharedPreferences.getInstance();
    print('Cached login state: ${prefs.getBool(_isLoggedInKey)}');
    print('Has cached user data: ${prefs.getString(_userDataKey) != null}');
    print('Memory cache valid: ${_isMemoryCacheValid()}');
    print('Local cache valid: ${await _isLocalCacheValid()}');
    
    final cachedUser = await _getCachedUserData();
    print('Cached user: ${cachedUser?.name} (${cachedUser?.uid})');
    print('Memory cached user: ${_cachedUser?.name} (${_cachedUser?.uid})');
    print('===============================');
  }

  // Cleanup old collections (use with caution)
  Future<bool> cleanupOldCollections(String uid) async {
    try {
      final batch = _firestore.batch();
      
      // Delete from old collections
      batch.delete(_firestore.collection('user_profiles').doc(uid));
      batch.delete(_firestore.collection('comprehensive_onboarding').doc(uid));
      batch.delete(_firestore.collection('fitness_goals').doc(uid));
      batch.delete(_firestore.collection('fitness_levels').doc(uid));
      batch.delete(_firestore.collection('workout_preferences').doc(uid));
      
      await batch.commit();
      print('✅ Old collections cleaned up for user: $uid');
      return true;
    } catch (e) {
      print('⚠️ Error cleaning up old collections: $e');
      return false;
    }
  }
} 