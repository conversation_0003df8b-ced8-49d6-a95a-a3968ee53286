import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/workout_model.dart';
import '../models/exercise_model.dart';

class SampleWorkoutDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Initialize sample workout plans in Firestore
  static Future<void> initializeSampleWorkouts() async {
    try {
      // Check if sample workouts already exist
      final existingWorkouts = await _firestore
          .collection('workoutPlans')
          .where('isCustom', isEqualTo: false)
          .limit(1)
          .get();

      if (existingWorkouts.docs.isNotEmpty) {
        print('Sample workouts already exist, skipping initialization');
        return;
      }

      print('Initializing sample workout plans...');

      // Create sample workout plans
      final sampleWorkouts = _createSampleWorkouts();

      // Add each workout to Firestore
      for (final workout in sampleWorkouts) {
        await _firestore.collection('workoutPlans').add(workout.toJson());
        print('Added workout: ${workout.name}');
      }

      print('Sample workout plans initialized successfully!');
    } catch (e) {
      print('Error initializing sample workouts: $e');
    }
  }

  // Create sample workout plans
  static List<WorkoutPlanModel> _createSampleWorkouts() {
    return [
      // Beginner Full Body Workout
      WorkoutPlanModel(
        id: '',
        name: 'Beginner Full Body Blast',
        description: 'A complete full-body workout perfect for beginners. No equipment needed!',
        duration: 30,
        difficulty: 'beginner',
        category: 'full_body',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        isCustom: false,
        isPublic: true,
        exercises: [
          WorkoutExercise(
            exerciseId: 'jumping_jacks',
            sets: 1,
            reps: 30,
            duration: 30,
            restTime: 30,
            notes: {
              'name': 'Jumping Jacks',
              'instructions': 'Start with feet together, jump while spreading legs and raising arms overhead',
              'muscleGroups': ['cardio', 'full_body'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'modified_pushups',
            sets: 3,
            reps: 10,
            duration: 0,
            restTime: 60,
            notes: {
              'name': 'Push-ups (Modified)',
              'instructions': 'Start on knees if needed. Keep body straight, lower chest to ground',
              'muscleGroups': ['chest', 'shoulders', 'triceps'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'bodyweight_squats',
            sets: 3,
            reps: 12,
            duration: 0,
            restTime: 60,
            notes: {
              'name': 'Bodyweight Squats',
              'instructions': 'Feet shoulder-width apart, lower as if sitting in chair',
              'muscleGroups': ['legs', 'glutes'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'plank_hold',
            sets: 3,
            reps: 1,
            duration: 30,
            restTime: 60,
            notes: {
              'name': 'Plank Hold',
              'instructions': 'Hold straight line from head to heels, engage core',
              'muscleGroups': ['core', 'shoulders'],
              'equipment': 'none',
            },
          ),
        ],
      ),

      // Intermediate HIIT Workout
      WorkoutPlanModel(
        id: '',
        name: 'HIIT Fat Burner',
        description: 'High-intensity interval training to torch calories and build endurance',
        duration: 25,
        difficulty: 'intermediate',
        category: 'hiit',
        imageUrl: 'https://images.unsplash.com/photo-1549060279-7e168fcee0c2?w=400',
        isCustom: false,
        isPublic: true,
        exercises: [
          WorkoutExercise(
            exerciseId: 'burpees',
            sets: 4,
            reps: 1,
            duration: 45,
            restTime: 15,
            notes: {
              'name': 'Burpees',
              'instructions': 'Squat down, jump back to plank, push-up, jump forward, jump up',
              'muscleGroups': ['full_body', 'cardio'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'mountain_climbers',
            sets: 4,
            reps: 1,
            duration: 45,
            restTime: 15,
            notes: {
              'name': 'Mountain Climbers',
              'instructions': 'Plank position, alternate bringing knees to chest rapidly',
              'muscleGroups': ['core', 'cardio'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'jump_squats',
            sets: 4,
            reps: 1,
            duration: 45,
            restTime: 15,
            notes: {
              'name': 'Jump Squats',
              'instructions': 'Squat down, explode up into jump, land softly',
              'muscleGroups': ['legs', 'glutes', 'cardio'],
              'equipment': 'none',
            },
          ),
        ],
      ),

      // Upper Body Strength
      WorkoutPlanModel(
        id: '',
        name: 'Upper Body Strength',
        description: 'Build strength and muscle in your upper body with these targeted exercises',
        duration: 40,
        difficulty: 'intermediate',
        category: 'strength',
        imageUrl: 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400',
        isCustom: false,
        isPublic: true,
        exercises: [
          WorkoutExercise(
            exerciseId: 'pushups',
            sets: 4,
            reps: 12,
            duration: 0,
            restTime: 90,
            notes: {
              'name': 'Push-ups',
              'instructions': 'Standard push-ups, keep body straight',
              'muscleGroups': ['chest', 'shoulders', 'triceps'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'pike_pushups',
            sets: 3,
            reps: 10,
            duration: 0,
            restTime: 90,
            notes: {
              'name': 'Pike Push-ups',
              'instructions': 'Downward dog position, lower head toward ground',
              'muscleGroups': ['shoulders', 'triceps'],
              'equipment': 'none',
            },
          ),
          WorkoutExercise(
            exerciseId: 'tricep_dips',
            sets: 3,
            reps: 12,
            duration: 0,
            restTime: 90,
            notes: {
              'name': 'Tricep Dips',
              'instructions': 'Use chair or bench, lower body by bending elbows',
              'muscleGroups': ['triceps', 'shoulders'],
              'equipment': 'chair',
            },
          ),
        ],
      ),

      // Yoga Flow
      WorkoutPlanModel(
        id: '',
        name: 'Morning Yoga Flow',
        description: 'Gentle yoga sequence to start your day with energy and mindfulness',
        duration: 20,
        difficulty: 'beginner',
        category: 'yoga',
        imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
        isCustom: false,
        isPublic: true,
        exercises: [
          WorkoutExercise(
            exerciseId: 'sun_salutation_a',
            sets: 3,
            reps: 1,
            duration: 60,
            restTime: 30,
            notes: {
              'name': 'Sun Salutation A',
              'instructions': 'Mountain pose → Forward fold → Half lift → Low push-up → Upward dog → Downward dog',
              'muscleGroups': ['full_body', 'flexibility'],
              'equipment': 'yoga_mat',
            },
          ),
          WorkoutExercise(
            exerciseId: 'warrior_ii_flow',
            sets: 2,
            reps: 1,
            duration: 60,
            restTime: 30,
            notes: {
              'name': 'Warrior II Flow',
              'instructions': 'Step into warrior II, hold, then flow to reverse warrior',
              'muscleGroups': ['legs', 'core', 'flexibility'],
              'equipment': 'yoga_mat',
            },
          ),
          WorkoutExercise(
            exerciseId: 'childs_pose_to_cobra',
            sets: 3,
            reps: 1,
            duration: 45,
            restTime: 15,
            notes: {
              'name': 'Child\'s Pose to Cobra',
              'instructions': 'Flow between child\'s pose and cobra pose',
              'muscleGroups': ['back', 'flexibility'],
              'equipment': 'yoga_mat',
            },
          ),
        ],
      ),
    ];
  }
}
