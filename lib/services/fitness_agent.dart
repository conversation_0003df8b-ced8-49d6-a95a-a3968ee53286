import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import '../models/user_profile_model.dart';
import '../models/workout_model.dart';
import '../services/auth_service.dart';
import '../services/comprehensive_onboarding_service.dart';
import '../services/firestore_service.dart';
import 'fitness_tools.dart';

class FitnessAgent {
  final AuthService _authService = AuthService();
  final ComprehensiveOnboardingService _onboardingService = ComprehensiveOnboardingService();
  final FirestoreService _firestoreService = FirestoreService();
  
  late GenerativeModel gemini;
  late ChatSession chat;
  bool _isInitialized = false;

  Future<void> initialize() async {
    try {
      // Initialize Firebase Vertex AI
      gemini = FirebaseVertexAI.instance.generativeModel(
        model: 'gemini-1.5-flash',
        systemInstruction: Content.text('''
          You are an expert personal fitness trainer and nutritionist AI assistant. Your role is to:
          
          1. Provide personalized workout plans based on user goals, fitness level, and available equipment
          2. Offer nutrition advice tailored to fitness objectives
          3. Track and analyze workout progress
          4. Motivate and encourage users in their fitness journey
          5. Answer fitness-related questions with evidence-based information
          
          IMPORTANT GUIDELINES:
          - Always consider the user's current fitness level and any limitations
          - Prioritize safety and proper form over intensity
          - Use available tools to access user data before making recommendations
          - Be encouraging and supportive while being realistic about expectations
          - When generating workouts, always include proper warm-up and cool-down
          - Provide clear instructions for exercises, including sets, reps, and rest periods
          
          TOOL USAGE:
          - Use getUserProfile to understand the user's goals and preferences
          - Use getUserWorkoutHistory to track progress and avoid overtraining
          - Use generateWorkout to create personalized workout plans
          - Use saveWorkout to store workouts the user likes
          - Use trackWorkoutProgress to analyze improvement over time
          - Use getNutritionAdvice for meal and supplement recommendations
          - Use setFitnessGoal to help users set achievable targets
          
          Always be conversational, encouraging, and focus on helping users achieve their fitness goals safely and effectively.
        '''),
        toolConfig: ToolConfig(
          functionCallingConfig: FunctionCallingConfig.any({
            'getUserProfile',
            'getUserWorkoutHistory', 
            'generateWorkout',
            'saveWorkout',
            'getExerciseRecommendations',
            'trackWorkoutProgress',
            'getNutritionAdvice',
            'setFitnessGoal',
          }),
        ),
        tools: [
          Tool.functionDeclarations([
            getUserProfileTool,
            getUserWorkoutHistoryTool,
            generateWorkoutTool,
            saveWorkoutTool,
            getExerciseRecommendationsTool,
            trackWorkoutProgressTool,
            getNutritionAdviceTool,
            setFitnessGoalTool,
          ]),
        ],
      );
      
      chat = gemini.startChat();
      _isInitialized = true;
      print('Fitness Agent initialized successfully with Firebase Vertex AI');
    } catch (e) {
      print('Error initializing Fitness Agent: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<String> sendMessage(String message) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final response = await chat.sendMessage(Content.text(message));
      
      // Handle function calls if present
      if (response.functionCalls.isNotEmpty) {
        await _handleFunctionCalls(response.functionCalls);
        // Get the final response after function calls
        final finalResponse = await chat.sendMessage(Content.text('Please provide your response based on the function call results.'));
        return finalResponse.text ?? 'I processed your request but couldn\'t generate a response.';
      }
      
      return response.text ?? 'I\'m sorry, I couldn\'t generate a response. Please try again.';
    } catch (e) {
      print('Error sending message to Fitness Agent: $e');
      return 'I\'m experiencing some technical difficulties. Please try again in a moment.';
    }
  }

  Future<void> _handleFunctionCalls(Iterable<FunctionCall> functionCalls) async {
    for (var functionCall in functionCalls) {
      print('Executing function: ${functionCall.name}');
      
      try {
        String result;
        switch (functionCall.name) {
          case 'getUserProfile':
            result = await _getUserProfile();
            break;
          case 'getUserWorkoutHistory':
            final limit = functionCall.args['limit'] as int? ?? 10;
            result = await _getUserWorkoutHistory(limit);
            break;
          case 'generateWorkout':
            result = await _generateWorkout(functionCall.args);
            break;
          case 'saveWorkout':
            result = await _saveWorkout(functionCall.args);
            break;
          case 'getExerciseRecommendations':
            result = await _getExerciseRecommendations(functionCall.args);
            break;
          case 'trackWorkoutProgress':
            result = await _trackWorkoutProgress(functionCall.args);
            break;
          case 'getNutritionAdvice':
            result = await _getNutritionAdvice(functionCall.args);
            break;
          case 'setFitnessGoal':
            result = await _setFitnessGoal(functionCall.args);
            break;
          default:
            result = 'Unknown function: ${functionCall.name}';
        }
        
        // Send the function result back to the model
        await chat.sendMessage(Content.text('Function ${functionCall.name} result: $result'));
      } catch (e) {
        print('Error executing function ${functionCall.name}: $e');
        await chat.sendMessage(Content.text('Error executing ${functionCall.name}: $e'));
      }
    }
  }

  Future<String> _getUserProfile() async {
    try {
      final user = _authService.currentUser;
      if (user == null) return 'User not authenticated';
      
      final profile = await _onboardingService.getUserProfile(user.uid);
      if (profile == null) return 'User profile not found';
      
      return jsonEncode({
        'name': profile.name,
        'age': profile.age,
        'gender': profile.gender,
        'height': profile.height,
        'weight': profile.weight,
        'fitnessLevel': profile.fitnessLevel,
        'goals': profile.goals,
        'preferences': profile.preferences,
        'equipment': profile.equipment,
        'workoutFrequency': profile.workoutFrequency,
        'timeAvailable': profile.timeAvailable,
      });
    } catch (e) {
      return 'Error retrieving user profile: $e';
    }
  }

  Future<String> _getUserWorkoutHistory(int limit) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return 'User not authenticated';
      
      final workouts = await _firestoreService.getUserWorkouts(user.uid, limit: limit);
      
      return jsonEncode({
        'totalWorkouts': workouts.length,
        'recentWorkouts': workouts.map((w) => {
          'name': w.name,
          'date': w.date?.toIso8601String(),
          'duration': w.duration,
          'type': w.type,
          'exercises': w.exercises.length,
          'caloriesBurned': w.caloriesBurned,
        }).toList(),
      });
    } catch (e) {
      return 'Error retrieving workout history: $e';
    }
  }

  Future<String> _generateWorkout(Map<String, dynamic> args) async {
    try {
      final workoutType = args['workoutType'] as String;
      final duration = args['duration'] as int;
      final equipment = args['equipment'] as String;
      final difficulty = args['difficulty'] as String;
      
      // This would typically call your workout generation logic
      // For now, return a structured response
      return jsonEncode({
        'success': true,
        'workoutType': workoutType,
        'duration': duration,
        'equipment': equipment,
        'difficulty': difficulty,
        'message': 'Workout generation parameters received. Use your existing workout generation logic here.',
      });
    } catch (e) {
      return 'Error generating workout: $e';
    }
  }

  Future<String> _saveWorkout(Map<String, dynamic> args) async {
    try {
      final workoutName = args['workoutName'] as String;
      final workoutData = args['workoutData'] as String;
      
      // Save to Firestore
      final user = _authService.currentUser;
      if (user == null) return 'User not authenticated';
      
      // This would save to your workout collection
      return jsonEncode({
        'success': true,
        'message': 'Workout "$workoutName" saved successfully',
      });
    } catch (e) {
      return 'Error saving workout: $e';
    }
  }

  Future<String> _getExerciseRecommendations(Map<String, dynamic> args) async {
    try {
      final muscleGroup = args['muscleGroup'] as String?;
      final equipment = args['equipment'] as String?;
      final count = args['count'] as int? ?? 5;
      
      return jsonEncode({
        'success': true,
        'muscleGroup': muscleGroup,
        'equipment': equipment,
        'count': count,
        'message': 'Exercise recommendation parameters received',
      });
    } catch (e) {
      return 'Error getting exercise recommendations: $e';
    }
  }

  Future<String> _trackWorkoutProgress(Map<String, dynamic> args) async {
    try {
      final timeframe = args['timeframe'] as String;
      final metric = args['metric'] as String;
      
      return jsonEncode({
        'success': true,
        'timeframe': timeframe,
        'metric': metric,
        'message': 'Progress tracking parameters received',
      });
    } catch (e) {
      return 'Error tracking progress: $e';
    }
  }

  Future<String> _getNutritionAdvice(Map<String, dynamic> args) async {
    try {
      final goal = args['goal'] as String;
      final mealType = args['mealType'] as String?;
      
      return jsonEncode({
        'success': true,
        'goal': goal,
        'mealType': mealType,
        'message': 'Nutrition advice parameters received',
      });
    } catch (e) {
      return 'Error getting nutrition advice: $e';
    }
  }

  Future<String> _setFitnessGoal(Map<String, dynamic> args) async {
    try {
      final goalType = args['goalType'] as String;
      final target = args['target'] as String;
      final timeframe = args['timeframe'] as String;
      
      return jsonEncode({
        'success': true,
        'goalType': goalType,
        'target': target,
        'timeframe': timeframe,
        'message': 'Fitness goal parameters received',
      });
    } catch (e) {
      return 'Error setting fitness goal: $e';
    }
  }
} 