import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/exercise_model.dart';
import '../models/workout_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all exercises
  Future<List<ExerciseModel>> getAllExercises() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection('exercises').get();
      return snapshot.docs
          .map((doc) => ExerciseModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get exercises error: $e');
      return [];
    }
  }



  // Get all workout plans (from workoutPlans collection for pre-built workouts)
  Future<List<WorkoutPlanModel>> getAllWorkoutPlans() async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('workoutPlans')
          .where('isCustom', isEqualTo: false) // Only pre-built workout plans
          .get();

      List<WorkoutPlanModel> workouts = [];
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          workouts.add(WorkoutPlanModel.fromJson({
            'id': doc.id,
            ...data,
          }));
        } catch (e) {
          print('Error parsing document ${doc.id} as WorkoutPlanModel: $e');
          // Skip invalid documents
        }
      }

      return workouts;
    } catch (e) {
      print('Get workout plans error: $e');
      return [];
    }
  }

  // Get workout plans by difficulty (from workoutPlans collection)
  Future<List<WorkoutPlanModel>> getWorkoutPlansByDifficulty(
      String difficulty) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('workoutPlans')
          .where('isCustom', isEqualTo: false) // Only pre-built workout plans
          .where('difficulty', isEqualTo: difficulty)
          .get();

      List<WorkoutPlanModel> workouts = [];
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          workouts.add(WorkoutPlanModel.fromJson({
            'id': doc.id,
            ...data,
          }));
        } catch (e) {
          print('Error parsing document ${doc.id} as WorkoutPlanModel: $e');
          // Skip invalid documents
        }
      }

      return workouts;
    } catch (e) {
      print('Get workout plans by difficulty error: $e');
      return [];
    }
  }

  // Get workout plan by ID (check both workoutPlans and userWorkouts collections)
  Future<WorkoutPlanModel?> getWorkoutPlanById(String id) async {
    try {
      // First check workoutPlans collection (pre-built workouts)
      DocumentSnapshot doc =
          await _firestore.collection('workoutPlans').doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return WorkoutPlanModel.fromJson({
          'id': doc.id,
          ...data,
        });
      }

      // Then check userWorkouts collection (custom workouts)
      doc = await _firestore.collection('userWorkouts').doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        // Make sure it's a workout plan, not a session
        if (data.containsKey('name') && data.containsKey('exercises')) {
          return WorkoutPlanModel.fromJson({
            'id': doc.id,
            ...data,
          });
        }
      }
      return null;
    } catch (e) {
      print('Get workout plan by ID error: $e');
      return null;
    }
  }

  // Start workout session
  Future<String?> startWorkoutSession(String userId, String workoutPlanId) async {
    try {
      WorkoutSessionModel session = WorkoutSessionModel(
        id: '',
        userId: userId,
        workoutPlanId: workoutPlanId,
        startTime: DateTime.now(),
      );

      DocumentReference docRef = await _firestore
          .collection('userWorkouts')
          .add(session.toJson());

      return docRef.id;
    } catch (e) {
      print('Start workout session error: $e');
      return null;
    }
  }

  // Complete workout session
  Future<bool> completeWorkoutSession(
    String sessionId,
    List<CompletedExercise> completedExercises,
    String notes,
    int totalCaloriesBurned,
  ) async {
    try {
      // Get the current session data
      DocumentSnapshot sessionDoc = await _firestore.collection('userWorkouts').doc(sessionId).get();
      if (!sessionDoc.exists) {
        print('Session not found: $sessionId');
        return false;
      }

      Map<String, dynamic> sessionData = sessionDoc.data() as Map<String, dynamic>;

      // Update session data with completion info
      sessionData.addAll({
        'endTime': DateTime.now().millisecondsSinceEpoch,
        'completedExercises': completedExercises.map((e) => e.toJson()).toList(),
        'notes': notes,
        'isCompleted': true,
        'totalCaloriesBurned': totalCaloriesBurned,
      });

      // Move completed session to workoutHistory
      await _firestore.collection('workoutHistory').doc(sessionId).set(sessionData);

      // Remove from userWorkouts (active sessions)
      await _firestore.collection('userWorkouts').doc(sessionId).delete();

      return true;
    } catch (e) {
      print('Complete workout session error: $e');
      return false;
    }
  }

  // Get user workout history with pagination
  Future<List<WorkoutSessionModel>> getUserWorkoutHistory(
    String userId, {
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      // Verify user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        print('Get user workout history error: User not authenticated');
        return [];
      }

      // Verify user has permission (only allow access to own data)
      if (currentUser.uid != userId) {
        print('Get user workout history error: Permission denied - user can only access own data');
        return [];
      }

      Query query = _firestore
          .collection('workoutHistory')
          .where('userId', isEqualTo: userId)
          .orderBy('startTime', descending: true)
          .limit(limit);

      // Add pagination if lastDocument is provided
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      QuerySnapshot snapshot = await query.get();
      print('📊 Found ${snapshot.docs.length} workout history records for user $userId');

      return snapshot.docs
          .map((doc) => WorkoutSessionModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get user workout history error: $e');
      return [];
    }
  }

  // Get user workout history (legacy method for backward compatibility)
  Future<List<WorkoutSessionModel>> getUserWorkoutHistoryLegacy(String userId) async {
    return getUserWorkoutHistory(userId, limit: 20);
  }

  // Get workout history summary (lightweight for lists)
  Future<List<Map<String, dynamic>>> getUserWorkoutSummary(
    String userId, {
    int limit = 50,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection('workoutHistory')
          .where('userId', isEqualTo: userId)
          .orderBy('startTime', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      QuerySnapshot snapshot = await query.get();

      // Return only essential fields for performance
      return snapshot.docs
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return {
              'id': doc.id,
              'workoutPlanId': data['workoutPlanId'],
              'startTime': data['startTime'],
              'endTime': data['endTime'],
              'isCompleted': data['isCompleted'],
              'totalCalories': data['totalCalories'],
              'duration': data['duration'],
            };
          })
          .toList();
    } catch (e) {
      print('Get workout summary error: $e');
      return [];
    }
  }

  // Get recent workouts (last 7 days)
  Future<List<WorkoutSessionModel>> getRecentWorkouts(String userId) async {
    try {
      DateTime sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      QuerySnapshot snapshot = await _firestore
          .collection('workoutHistory')
          .where('userId', isEqualTo: userId)
          .where('startTime', isGreaterThan: sevenDaysAgo.millisecondsSinceEpoch)
          .orderBy('startTime', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => WorkoutSessionModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get recent workouts error: $e');
      return [];
    }
  }

  // Get exercise by ID
  Future<ExerciseModel?> getExerciseById(String id) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection('exercises').doc(id).get();
      if (doc.exists) {
        return ExerciseModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }
      return null;
    } catch (e) {
      print('Get exercise by ID error: $e');
      return null;
    }
  }

  // Get exercises by category (optimized query)
  Future<List<ExerciseModel>> getExercisesByCategory(String category) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('User is not authenticated');
        return [];
      }

      QuerySnapshot snapshot = await _firestore
          .collection('exercises')
          .where('category', isEqualTo: category)
          .get();

      return snapshot.docs
          .map((doc) => ExerciseModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get exercises by category error: $e');
      return [];
    }
  }

  // Get exercises by muscle group (optimized query)
  Future<List<ExerciseModel>> getExercisesByMuscleGroup(String muscleGroup) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('User is not authenticated');
        return [];
      }

      QuerySnapshot snapshot = await _firestore
          .collection('exercises')
          .where('primaryMuscles', arrayContains: muscleGroup)
          .get();

      return snapshot.docs
          .map((doc) => ExerciseModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Get exercises by muscle group error: $e');
      return [];
    }
  }

  // Search exercises with server-side filtering (optimized)
  Future<List<ExerciseModel>> searchExercises(String query) async {
    try {
      if (query.isEmpty) {
        return [];
      }

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('User is not authenticated');
        return [];
      }

      // Use server-side prefix matching for better performance
      // Note: For full-text search, consider using Algolia or similar service
      final String queryEnd = '$query\uf8ff';

      QuerySnapshot snapshot = await _firestore
          .collection('exercises')
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThan: queryEnd)
          .limit(20) // Limit results for performance
          .get();

      return snapshot.docs
          .map((doc) => ExerciseModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      print('Search exercises error: $e');
      // Fallback to client-side search for backward compatibility
      return await _fallbackSearchExercises(query);
    }
  }

  // Fallback method for complex searches (limited use)
  Future<List<ExerciseModel>> _fallbackSearchExercises(String query) async {
    try {
      print('Using fallback search for query: $query');

      // Only fetch a limited set for fallback
      QuerySnapshot snapshot = await _firestore
          .collection('exercises')
          .limit(100) // Limit to reduce data transfer
          .get();

      final lowercaseQuery = query.toLowerCase();

      final results = snapshot.docs
          .where((doc) {
            try {
              final data = doc.data() as Map<String, dynamic>?;
              if (data == null) return false;

              final name = data['name']?.toString().toLowerCase() ?? '';
              final description = data['description']?.toString().toLowerCase() ?? '';
              final tags = List<String>.from(data['tags'] ?? []).map((t) => t.toString().toLowerCase()).toList();

              return name.contains(lowercaseQuery) ||
                     description.contains(lowercaseQuery) ||
                     tags.any((tag) => tag.contains(lowercaseQuery));
            } catch (e) {
              print('Error processing document ${doc.id}: $e');
              return false;
            }
          })
          .map((doc) {
            try {
              return ExerciseModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              });
            } catch (e) {
              print('Error mapping document ${doc.id} to ExerciseModel: $e');
              return null;
            }
          })
          .whereType<ExerciseModel>()
          .toList();

      print('Found ${results.length} matching exercises');
      return results;

    } catch (e, stackTrace) {
      print('Search exercises error: $e');
      print('Stack trace: $stackTrace');
      return [];
    }
  }

  // Create custom workout plan
  Future<String?> createCustomWorkoutPlan(WorkoutPlanModel workoutPlan) async {
    try {
      DocumentReference docRef = await _firestore
          .collection('userWorkouts')
          .add(workoutPlan.toJson());
      return docRef.id;
    } catch (e) {
      print('Create custom workout plan error: $e');
      return null;
    }
  }

  // Update custom workout plan
  Future<bool> updateCustomWorkoutPlan(WorkoutPlanModel workoutPlan) async {
    try {
      await _firestore
          .collection('userWorkouts')
          .doc(workoutPlan.id)
          .update(workoutPlan.toJson());
      return true;
    } catch (e) {
      print('Update custom workout plan error: $e');
      return false;
    }
  }

  // Delete custom workout plan
  Future<bool> deleteCustomWorkoutPlan(String workoutPlanId) async {
    try {
      await _firestore
          .collection('userWorkouts')
          .doc(workoutPlanId)
          .delete();
      return true;
    } catch (e) {
      print('Delete custom workout plan error: $e');
      return false;
    }
  }

  // Get custom workout plans for a user
  Future<List<WorkoutPlanModel>> getCustomWorkoutPlans(String userId) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('userWorkouts')
          .where('isCustom', isEqualTo: true)
          .where('createdBy', isEqualTo: userId)
          .get();

      List<WorkoutPlanModel> workouts = snapshot.docs
          .map((doc) => WorkoutPlanModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();

      // Sort by createdAt in memory (descending - newest first)
      workouts.sort((a, b) {
        if (a.createdAt == null && b.createdAt == null) return 0;
        if (a.createdAt == null) return 1;
        if (b.createdAt == null) return -1;
        return b.createdAt!.compareTo(a.createdAt!);
      });

      return workouts;
    } catch (e) {
      print('Get custom workout plans error: $e');
      return [];
    }
  }

  // Log a completed workout
  Future<bool> logWorkout({
    required String userId,
    required String workoutName,
    required int durationInMinutes,
    required List<Map<String, dynamic>> exercises,
  }) async {
    try {
      final now = DateTime.now();
      final startTime = now.subtract(Duration(minutes: durationInMinutes));
      
      // Create a workout session model
      final WorkoutSessionModel session = WorkoutSessionModel(
        id: '',
        userId: userId,
        workoutPlanId: 'custom_logged', // Mark as a manually logged workout
        startTime: startTime,
        endTime: now,
        isCompleted: true,
        totalCaloriesBurned: 0, // Could be calculated if exercise data contains this
        notes: 'Manually logged workout',
        completedExercises: exercises.map((exercise) {
          // Convert exercise map to CompletedExercise
          return CompletedExercise(
            exerciseId: exercise['id'] ?? 'custom_exercise',
            completedSets: exercise['sets'] ?? 1,
            sets: [
              ExerciseSet(
                reps: exercise['reps'] ?? 0,
                weight: (exercise['weight'] ?? 0).toDouble(),
                duration: exercise['duration'] ?? 0,
              )
            ],
            completedAt: now,
          );
        }).toList(),
      );

      // Add directly to workout history
      await _firestore.collection('workoutHistory').add(session.toJson());
      
      return true;
    } catch (e) {
      print('Log workout error: $e');
      return false;
    }
  }
}