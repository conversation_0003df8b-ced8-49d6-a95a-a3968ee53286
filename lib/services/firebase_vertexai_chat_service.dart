import 'package:flutter/foundation.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';

class VertexAIChatMessage {
  final String id;
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final bool isLoading;

  VertexAIChatMessage({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.isLoading = false,
  });

  VertexAIChatMessage copyWith({
    String? id,
    String? text,
    bool? isUser,
    DateTime? timestamp,
    bool? isLoading,
  }) {
    return VertexAIChatMessage(
      id: id ?? this.id,
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class FirebaseVertexAIChatService extends ChangeNotifier {
  static final FirebaseVertexAIChatService _instance = FirebaseVertexAIChatService._internal();
  factory FirebaseVertexAIChatService() => _instance;
  FirebaseVertexAIChatService._internal();

  GenerativeModel? _model;
  ChatSession? _chatSession;
  final List<VertexAIChatMessage> _messages = [];
  bool _isInitialized = false;
  bool _isLoading = false;

  List<VertexAIChatMessage> get messages => List.unmodifiable(_messages);
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;

  Future<void> initialize() async {
    try {
      // Initialize Firebase Vertex AI model
      // No API key needed - uses Firebase Auth automatically
      _model = FirebaseVertexAI.instance.generativeModel(
        model: 'gemini-1.5-flash',
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
        systemInstruction: Content.system('''
        You are a friendly and knowledgeable fitness AI assistant. Your role is to:
        - Help users with workout planning and exercise guidance
        - Provide nutrition advice and meal planning
        - Answer questions about fitness, health, and wellness
        - Offer motivation and encouragement
        - Provide form corrections and safety tips
        - Suggest modifications for different fitness levels
        - Keep responses concise and actionable
        '''),
      );

      _chatSession = _model!.startChat();
      _isInitialized = true;
      notifyListeners();
      
      debugPrint('✅ Firebase Vertex AI Chat Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Firebase Vertex AI Chat: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<void> sendMessage(String message) async {
    if (!_isInitialized || _model == null || _chatSession == null) {
      await initialize();
      if (!_isInitialized) {
        throw Exception('Failed to initialize Firebase Vertex AI chat service');
      }
    }

    final userMessage = VertexAIChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: message,
      isUser: true,
      timestamp: DateTime.now(),
    );

    _messages.add(userMessage);
    notifyListeners();

    final loadingMessage = VertexAIChatMessage(
      id: '${DateTime.now().millisecondsSinceEpoch}_loading',
      text: '',
      isUser: false,
      timestamp: DateTime.now(),
      isLoading: true,
    );

    _messages.add(loadingMessage);
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _chatSession!.sendMessage(Content.text(message));
      final responseText = response.text ?? 'Sorry, I couldn\'t generate a response.';

      _messages.removeLast();
      
      final aiMessage = VertexAIChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: responseText,
        isUser: false,
        timestamp: DateTime.now(),
      );

      _messages.add(aiMessage);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error sending message: $e');
      _messages.removeLast();
      
      String errorMessage = 'Sorry, I encountered an error. Please try again.';
      
      // Provide more specific error messages
      if (e.toString().contains('VertexAI is not available')) {
        errorMessage = 'Firebase Vertex AI is not available. Please check your Firebase project configuration.';
      } else if (e.toString().contains('User not signed in')) {
        errorMessage = 'Please sign in to use the AI chat feature.';
      }
      
      final errorMsg = VertexAIChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: errorMessage,
        isUser: false,
        timestamp: DateTime.now(),
      );

      _messages.add(errorMsg);
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearChat() {
    _messages.clear();
    _chatSession = _model?.startChat();
    notifyListeners();
  }

  @override
  void dispose() {
    _messages.clear();
    _chatSession = null;
    _model = null;
    _isInitialized = false;
    super.dispose();
  }
}