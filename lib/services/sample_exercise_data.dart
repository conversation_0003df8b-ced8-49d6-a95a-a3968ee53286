import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/exercise_model.dart';

class SampleExerciseDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Initialize sample exercises in Firestore
  static Future<void> initializeSampleExercises() async {
    try {
      // Check if exercises already exist
      final existingExercises = await _firestore
          .collection('exercises')
          .limit(1)
          .get();

      if (existingExercises.docs.isNotEmpty) {
        print('Exercises already exist, skipping initialization');
        return;
      }

      print('Initializing sample exercises...');

      // Create sample exercises
      final sampleExercises = _createSampleExercises();

      // Add each exercise to Firestore
      for (final exercise in sampleExercises) {
        final docRef = await _firestore.collection('exercises').add(exercise.toJson());
        print('Added exercise: ${exercise.name} with ID: ${docRef.id}');
      }

      print('Sample exercises initialized successfully!');
    } catch (e) {
      print('Error initializing sample exercises: $e');
    }
  }

  // Create comprehensive sample exercises
  static List<ExerciseModel> _createSampleExercises() {
    final now = DateTime.now();
    
    return [
      // Shoulder Exercises with Dumbbells
      ExerciseModel(
        id: '',
        name: 'Dumbbell Shoulder Press',
        category: 'strength',
        description: 'A fundamental shoulder exercise that targets all three deltoid heads',
        instructions: [
          'Sit on a bench with back support',
          'Hold dumbbells at shoulder height with palms facing forward',
          'Press the weights overhead until arms are fully extended',
          'Lower back to starting position with control'
        ],
        muscleGroups: ['shoulders', 'triceps'],
        primaryMuscleGroup: 'shoulders',
        secondaryMuscleGroups: ['triceps'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 8.5,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['push', 'compound', 'shoulders'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Dumbbell Lateral Raises',
        category: 'strength',
        description: 'Isolation exercise for the lateral (side) deltoids',
        instructions: [
          'Stand with dumbbells at your sides',
          'Keep a slight bend in your elbows',
          'Raise the weights out to the sides until parallel with shoulders',
          'Lower back down with control'
        ],
        muscleGroups: ['shoulders'],
        primaryMuscleGroup: 'shoulders',
        secondaryMuscleGroups: [],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 7.0,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['isolation', 'shoulders'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Dumbbell Front Raises',
        category: 'strength',
        description: 'Targets the anterior (front) deltoids',
        instructions: [
          'Stand with dumbbells in front of thighs',
          'Keep arms straight with slight elbow bend',
          'Raise one or both dumbbells forward to shoulder height',
          'Lower back down slowly'
        ],
        muscleGroups: ['shoulders'],
        primaryMuscleGroup: 'shoulders',
        secondaryMuscleGroups: [],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 6.5,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['isolation', 'shoulders'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Arnold Press',
        category: 'strength',
        description: 'A variation of shoulder press that hits all deltoid heads',
        instructions: [
          'Start with dumbbells at chest level, palms facing you',
          'As you press up, rotate palms to face forward',
          'Press until arms are extended overhead',
          'Reverse the motion on the way down'
        ],
        muscleGroups: ['shoulders', 'triceps'],
        primaryMuscleGroup: 'shoulders',
        secondaryMuscleGroups: ['triceps'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 8.5,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['compound', 'shoulders', 'push'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Dumbbell Rear Delt Fly',
        category: 'strength',
        description: 'Targets the posterior (rear) deltoids',
        instructions: [
          'Bend forward at the hips with knees slightly bent',
          'Hold dumbbells hanging down with palms facing each other',
          'Raise arms out to the sides, squeezing shoulder blades',
          'Lower back down with control'
        ],
        muscleGroups: ['shoulders', 'back'],
        primaryMuscleGroup: 'shoulders',
        secondaryMuscleGroups: ['back'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 7.5,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['isolation', 'shoulders', 'pull'],
        createdAt: now,
        updatedAt: now,
      ),
      
      // Chest Exercises
      ExerciseModel(
        id: '',
        name: 'Push-ups',
        category: 'strength',
        description: 'Classic bodyweight exercise for chest, shoulders, and triceps',
        instructions: [
          'Start in plank position with hands shoulder-width apart',
          'Lower your body until chest nearly touches the floor',
          'Push back up to starting position',
          'Keep core engaged throughout'
        ],
        muscleGroups: ['chest', 'shoulders', 'triceps'],
        primaryMuscleGroup: 'chest',
        secondaryMuscleGroups: ['shoulders', 'triceps'],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1598266663439-2056e6aacded?w=400',
        caloriesPerMinute: 8.0,
        isBodyweight: true,
        equipment: [],
        tags: ['push', 'compound', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Dumbbell Chest Press',
        category: 'strength',
        description: 'Fundamental chest exercise with dumbbells',
        instructions: [
          'Lie on bench with dumbbells at chest level',
          'Press weights up until arms are extended',
          'Lower back down with control to chest level',
          'Keep feet flat on floor for stability'
        ],
        muscleGroups: ['chest', 'shoulders', 'triceps'],
        primaryMuscleGroup: 'chest',
        secondaryMuscleGroups: ['shoulders', 'triceps'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 9.0,
        isBodyweight: false,
        equipment: ['dumbbells', 'bench'],
        tags: ['push', 'compound', 'chest'],
        createdAt: now,
        updatedAt: now,
      ),
      
      // Leg Exercises
      ExerciseModel(
        id: '',
        name: 'Bodyweight Squats',
        category: 'strength',
        description: 'Fundamental lower body exercise',
        instructions: [
          'Stand with feet shoulder-width apart',
          'Lower body by bending knees and hips',
          'Go down until thighs are parallel to ground',
          'Push through heels to return to standing'
        ],
        muscleGroups: ['legs', 'glutes'],
        primaryMuscleGroup: 'legs',
        secondaryMuscleGroups: ['glutes'],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1574680096145-d05b474e2155?w=400',
        caloriesPerMinute: 8.0,
        isBodyweight: true,
        equipment: [],
        tags: ['legs', 'compound', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Dumbbell Lunges',
        category: 'strength',
        description: 'Unilateral leg exercise for strength and balance',
        instructions: [
          'Hold dumbbells at sides',
          'Step forward into lunge position',
          'Lower back knee toward ground',
          'Push back to starting position'
        ],
        muscleGroups: ['legs', 'glutes'],
        primaryMuscleGroup: 'legs',
        secondaryMuscleGroups: ['glutes'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400',
        caloriesPerMinute: 8.5,
        isBodyweight: false,
        equipment: ['dumbbells'],
        tags: ['legs', 'compound', 'unilateral'],
        createdAt: now,
        updatedAt: now,
      ),
      
      // Core Exercises
      ExerciseModel(
        id: '',
        name: 'Plank',
        category: 'strength',
        description: 'Isometric core exercise',
        instructions: [
          'Start in forearm plank position',
          'Keep body in straight line from head to heels',
          'Engage core and hold position',
          'Breathe normally throughout'
        ],
        muscleGroups: ['core', 'shoulders', 'back'],
        primaryMuscleGroup: 'core',
        secondaryMuscleGroups: ['shoulders', 'back'],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1566241142559-40e1dab266c6?w=400',
        caloriesPerMinute: 5.0,
        isBodyweight: true,
        equipment: [],
        tags: ['core', 'isometric', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
        isTimerBased: true,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Mountain Climbers',
        category: 'cardio',
        description: 'Dynamic cardio exercise that targets core',
        instructions: [
          'Start in high plank position',
          'Drive one knee toward chest',
          'Quickly switch legs in running motion',
          'Keep core engaged and hips level'
        ],
        muscleGroups: ['core', 'legs', 'shoulders'],
        primaryMuscleGroup: 'core',
        secondaryMuscleGroups: ['legs', 'shoulders'],
        difficulty: 'intermediate',
        imageUrl: 'https://images.unsplash.com/photo-1601422407692-ec4eeec1d9b3?w=400',
        caloriesPerMinute: 12.0,
        isBodyweight: true,
        equipment: [],
        tags: ['cardio', 'core', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
      ),
      
      // Cardio Exercises
      ExerciseModel(
        id: '',
        name: 'Jumping Jacks',
        category: 'cardio',
        description: 'Classic cardio warm-up exercise',
        instructions: [
          'Start with feet together, arms at sides',
          'Jump feet apart while raising arms overhead',
          'Jump back to starting position',
          'Maintain steady rhythm'
        ],
        muscleGroups: ['full body'],
        primaryMuscleGroup: 'cardio',
        secondaryMuscleGroups: [],
        difficulty: 'beginner',
        imageUrl: 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=400',
        caloriesPerMinute: 10.0,
        isBodyweight: true,
        equipment: [],
        tags: ['cardio', 'warmup', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
      ),
      
      ExerciseModel(
        id: '',
        name: 'Burpees',
        category: 'cardio',
        description: 'Full-body cardio exercise',
        instructions: [
          'Start standing, drop to push-up position',
          'Perform a push-up',
          'Jump feet back to hands',
          'Jump up with arms overhead'
        ],
        muscleGroups: ['full body'],
        primaryMuscleGroup: 'cardio',
        secondaryMuscleGroups: ['chest', 'legs', 'shoulders'],
        difficulty: 'advanced',
        imageUrl: 'https://images.unsplash.com/photo-1601422407692-ec4eeec1d9b3?w=400',
        caloriesPerMinute: 12.0,
        isBodyweight: true,
        equipment: [],
        tags: ['cardio', 'hiit', 'bodyweight'],
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}