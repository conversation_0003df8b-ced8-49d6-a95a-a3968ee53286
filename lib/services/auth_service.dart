import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static const String _userDataKey = 'cached_user_data';
  static const String _isLoggedInKey = 'is_logged_in';

  AuthService() {
    _initializeAuth();
  }

  // Initialize auth with persistence
  Future<void> _initializeAuth() async {
    try {
      // Set persistence only for web
      if (kIsWeb) {
        await _auth.setPersistence(Persistence.LOCAL);
        print('Auth persistence set to LOCAL for web');
      } else {
        print('Auth persistence not needed for mobile platforms');
      }
    } catch (e) {
      print('Auth persistence error: $e');
    }
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Check if user is logged in (with local cache)
  Future<bool> get isLoggedIn async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedLoginState = prefs.getBool(_isLoggedInKey) ?? false;
      final firebaseUser = _auth.currentUser;
      
      print('Cached login state: $cachedLoginState');
      print('Firebase user: ${firebaseUser?.uid}');
      
      return firebaseUser != null && cachedLoginState;
    } catch (e) {
      print('Error checking login state: $e');
      return false;
    }
  }

  // Get cached user data
  Future<UserModel?> getCachedUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataJson = prefs.getString(_userDataKey);
      
      if (userDataJson != null) {
        final userData = UserModel.fromJsonString(userDataJson);
        print('Retrieved cached user data for: ${userData.name}');
        return userData;
      }
    } catch (e) {
      print('Error getting cached user data: $e');
    }
    return null;
  }

  // Cache user data locally
  Future<void> _cacheUserData(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userDataKey, user.toJsonString());
      await prefs.setBool(_isLoggedInKey, true);
      print('Cached user data for: ${user.name}');
    } catch (e) {
      print('Error caching user data: $e');
    }
  }

  // Clear cached data
  Future<void> _clearCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey);
      await prefs.setBool(_isLoggedInKey, false);
      print('Cleared cached user data');
    } catch (e) {
      print('Error clearing cached data: $e');
    }
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        final userData = await getUserData(result.user!.uid);
        if (userData != null) {
          await _cacheUserData(userData);
          print('User signed in and cached: ${userData.name}');
        }
        return userData;
      }
      return null;
    } catch (e) {
      print('Sign in error: $e');
      rethrow;
    }
  }

  // Register with email and password
  Future<UserModel?> registerWithEmailAndPassword(
      String email, String password, String name) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        // Create user document in Firestore
        UserModel newUser = UserModel(
          uid: result.user!.uid,
          name: name,
          email: email,
          createdAt: DateTime.now(),
          preferences: {
            'units': 'metric',
            'notifications': true,
            'theme': 'system',
          },
          stats: {
            'totalWorkouts': 0,
            'totalMinutes': 0,
            'weeklyGoal': 150, // minutes per week
            'currentStreak': 0,
          },
        );

        await _firestore
            .collection('users')
            .doc(result.user!.uid)
            .set(newUser.toJson());

        await _cacheUserData(newUser);
        print('User registered and cached: ${newUser.name}');
        return newUser;
      }
      return null;
    } catch (e) {
      print('Registration error: $e');
      rethrow;
    }
  }

  // Get user data from Firestore
  Future<UserModel?> getUserData(String uid) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection('users').doc(uid).get();

      if (doc.exists) {
        return UserModel.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Get user data error: $e');
      return null;
    }
  }

  // Update user data
  Future<bool> updateUserData(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.uid).update(user.toJson());
      await _cacheUserData(user); // Update cache
      return true;
    } catch (e) {
      print('Update user data error: $e');
      return false;
    }
  }

  // Update user profile information
  Future<bool> updateUserProfile({
    String? name,
    String? email,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final user = currentUser;
      if (user == null) return false;

      // Get current user data
      final userData = await getUserData(user.uid);
      if (userData == null) return false;

      // Create updated user model
      final updatedUser = userData.copyWith(
        name: name ?? userData.name,
        email: email ?? userData.email,
        preferences: preferences ?? userData.preferences,
      );

      // Update in Firestore and cache
      final success = await updateUserData(updatedUser);
      if (success) {
        print('User profile updated successfully');
      }
      return success;
    } catch (e) {
      print('Update user profile error: $e');
      return false;
    }
  }

  // Update user preferences
  Future<bool> updateUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final user = currentUser;
      if (user == null) return false;

      // Get current user data
      final userData = await getUserData(user.uid);
      if (userData == null) return false;

      // Merge with existing preferences
      final updatedPreferences = Map<String, dynamic>.from(userData.preferences);
      updatedPreferences.addAll(preferences);

      return await updateUserProfile(preferences: updatedPreferences);
    } catch (e) {
      print('Update user preferences error: $e');
      return false;
    }
  }

  // Update user stats
  Future<bool> updateUserStats(Map<String, dynamic> stats) async {
    try {
      final user = currentUser;
      if (user == null) return false;

      // Get current user data
      final userData = await getUserData(user.uid);
      if (userData == null) return false;

      // Merge with existing stats
      final updatedStats = Map<String, dynamic>.from(userData.stats);
      updatedStats.addAll(stats);

      final updatedUser = userData.copyWith(
        stats: updatedStats,
      );

      return await updateUserData(updatedUser);
    } catch (e) {
      print('Update user stats error: $e');
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _clearCachedData();
      await _auth.signOut();
      print('User signed out and cache cleared');
    } catch (e) {
      print('Sign out error: $e');
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      print('Reset password error: $e');
      return false;
    }
  }

  // Update user stats after workout
  Future<bool> updateWorkoutStats(int workoutDuration, int caloriesBurned) async {
    try {
      if (currentUser == null) return false;

      UserModel? user = await getUserData(currentUser!.uid);
      if (user == null) return false;

      Map<String, dynamic> updatedStats = Map.from(user.stats);
      updatedStats['totalWorkouts'] = (updatedStats['totalWorkouts'] ?? 0) + 1;
      updatedStats['totalMinutes'] =
          (updatedStats['totalMinutes'] ?? 0) + workoutDuration;
      updatedStats['totalCalories'] = 
          (updatedStats['totalCalories'] ?? 0) + caloriesBurned;

      // Simple streak logic (can be improved)
      // This assumes workouts are logged on the day they happen.
      // For a more robust streak, you'd compare last workout date.
      DateTime lastWorkoutDate = user.stats['lastWorkoutDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(user.stats['lastWorkoutDate'])
          : DateTime.now().subtract(const Duration(days: 2)); // Assume streak broken if no last date
      
      DateTime today = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
      DateTime lastWorkoutDay = DateTime(lastWorkoutDate.year, lastWorkoutDate.month, lastWorkoutDate.day);

      if (today.difference(lastWorkoutDay).inDays == 1) {
        updatedStats['currentStreak'] = (updatedStats['currentStreak'] ?? 0) + 1;
      } else if (today.difference(lastWorkoutDay).inDays > 1) {
        updatedStats['currentStreak'] = 1; // Reset streak
      } else if (today.difference(lastWorkoutDay).inDays == 0) {
        // Same day workout, streak doesn't change yet, unless it's the first workout of a new streak
        if (updatedStats['currentStreak'] == 0) updatedStats['currentStreak'] = 1;
      }
      updatedStats['lastWorkoutDate'] = today.millisecondsSinceEpoch;

      UserModel updatedUser = user.copyWith(stats: updatedStats);
      
      await _cacheUserData(updatedUser);
      
      return await updateUserData(updatedUser);
    } catch (e) {
      print('Update workout stats error: $e');
      return false;
    }
  }

  // Check and restore authentication state
  Future<UserModel?> checkAndRestoreAuth() async {
    try {
      // First check if Firebase user exists
      final firebaseUser = currentUser;
      if (firebaseUser == null) {
        print('No Firebase user found');
        await _clearCachedData();
        return null;
      }

      // Check cached data
      final cachedUser = await getCachedUserData();
      if (cachedUser != null && cachedUser.uid == firebaseUser.uid) {
        print('Restored user from cache: ${cachedUser.name}');
        return cachedUser;
      }

      // If no cache or mismatch, fetch from Firestore
      final userData = await getUserData(firebaseUser.uid);
      if (userData != null) {
        await _cacheUserData(userData);
        print('Fetched and cached user data: ${userData.name}');
        return userData;
      }

      return null;
    } catch (e) {
      print('Error checking and restoring auth: $e');
      return null;
    }
  }

  // Debug method to check auth state
  Future<void> debugAuthState() async {
    print('=== AUTH DEBUG ===');
    print('Firebase user: ${currentUser?.uid}');
    print('Firebase user email: ${currentUser?.email}');
    
    final prefs = await SharedPreferences.getInstance();
    print('Cached login state: ${prefs.getBool(_isLoggedInKey)}');
    print('Has cached user data: ${prefs.getString(_userDataKey) != null}');
    
    final cachedUser = await getCachedUserData();
    print('Cached user: ${cachedUser?.name} (${cachedUser?.uid})');
    print('==================');
  }
}