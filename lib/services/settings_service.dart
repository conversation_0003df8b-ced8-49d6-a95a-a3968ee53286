import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings_model.dart';

class SettingsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Cache settings locally
  Future<void> _cacheSettings(AppSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('app_settings', settings.toJson().toString());
  }

  // Load cached settings
  Future<AppSettings?> _loadCachedSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString('app_settings');
      if (settingsString != null) {
        // Parse the cached settings
        // Note: You'd need to implement proper JSON parsing here
        return AppSettings(updatedAt: DateTime.now());
      }
    } catch (e) {
      print('Error loading cached settings: $e');
    }
    return null;
  }

  // Save settings to Firestore
  Future<void> saveSettings(AppSettings settings) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    try {
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('settings')
          .doc('app_settings')
          .set(settings.toJson());

      // Cache locally
      await _cacheSettings(settings);
    } catch (e) {
      throw Exception('Failed to save settings: $e');
    }
  }

  // Load settings from Firestore
  Future<AppSettings> loadSettings() async {
    final user = _auth.currentUser;
    if (user == null) {
      // Return default settings if not authenticated
      return AppSettings(updatedAt: DateTime.now());
    }

    try {
      final doc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('settings')
          .doc('app_settings')
          .get();

      if (doc.exists && doc.data() != null) {
        final settings = AppSettings.fromJson(doc.data()!);
        await _cacheSettings(settings);
        return settings;
      }
    } catch (e) {
      print('Error loading settings from Firestore: $e');
      // Try to load from cache
      final cachedSettings = await _loadCachedSettings();
      if (cachedSettings != null) return cachedSettings;
    }

    // Return default settings
    return AppSettings(updatedAt: DateTime.now());
  }

  // Update specific setting
  Future<void> updateSetting<T>(String key, T value) async {
    final currentSettings = await loadSettings();
    AppSettings updatedSettings;

    switch (key) {
      case 'themeMode':
        updatedSettings = currentSettings.copyWith(
          themeMode: value as ThemeMode,
          updatedAt: DateTime.now(),
        );
        break;
      case 'units':
        updatedSettings = currentSettings.copyWith(
          units: value as Units,
          updatedAt: DateTime.now(),
        );
        break;
      case 'notificationsEnabled':
        updatedSettings = currentSettings.copyWith(
          notificationsEnabled: value as bool,
          updatedAt: DateTime.now(),
        );
        break;
      case 'workoutReminders':
        updatedSettings = currentSettings.copyWith(
          workoutReminders: value as bool,
          updatedAt: DateTime.now(),
        );
        break;
      case 'autoPlayVideos':
        updatedSettings = currentSettings.copyWith(
          autoPlayVideos: value as bool,
          updatedAt: DateTime.now(),
        );
        break;
      case 'restTimerDuration':
        updatedSettings = currentSettings.copyWith(
          restTimerDuration: value as int,
          updatedAt: DateTime.now(),
        );
        break;
      default:
        throw Exception('Unknown setting key: $key');
    }

    await saveSettings(updatedSettings);
  }
} 