import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/consolidated_user_model_fixed.dart';

/// Fixed version of ConsolidatedUserService with improved error handling
class ConsolidatedUserServiceFixed {
  static final ConsolidatedUserServiceFixed _instance = ConsolidatedUserServiceFixed._internal();
  factory ConsolidatedUserServiceFixed() => _instance;
  ConsolidatedUserServiceFixed._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Cache for user data
  ConsolidatedUserModel? _cachedUser;
  StreamController<ConsolidatedUserModel?>? _userStreamController;
  StreamSubscription<DocumentSnapshot>? _userDocSubscription;
  String? _currentUserId;
  
  /// Get the current Firebase user
  User? get currentUser => _auth.currentUser;

  /// Get the current user's data stream
  Stream<ConsolidatedUserModel?> getUserStream() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value(null);
    }

    // If we're already listening to this user, return existing stream
    if (_currentUserId == user.uid && _userStreamController != null) {
      return _userStreamController!.stream;
    }

    // Clean up previous subscriptions
    _cleanup();

    // Set up new stream
    _currentUserId = user.uid;
    _userStreamController = StreamController<ConsolidatedUserModel?>.broadcast();

    // Start listening to user document
    _userDocSubscription = _firestore
        .collection('users')
        .doc(user.uid)
        .snapshots()
        .listen(
          (snapshot) async {
            try {
              if (snapshot.exists) {
                final userData = ConsolidatedUserModel.fromFirestore(snapshot);
                _cachedUser = userData;
                _userStreamController?.add(userData);
              } else {
                // Create default user if document doesn't exist
                final defaultUser = ConsolidatedUserModel.createDefault(
                  uid: user.uid,
                  email: user.email ?? '',
                );
                await saveUser(defaultUser);
                _cachedUser = defaultUser;
                _userStreamController?.add(defaultUser);
              }
            } catch (e) {
              print('Error in user stream: $e');
              // Try to use cached data or create default
              if (_cachedUser != null) {
                _userStreamController?.add(_cachedUser);
              } else {
                final defaultUser = ConsolidatedUserModel.createDefault(
                  uid: user.uid,
                  email: user.email ?? '',
                );
                _cachedUser = defaultUser;
                _userStreamController?.add(defaultUser);
              }
            }
          },
          onError: (error) {
            print('Error listening to user document: $error');
            _userStreamController?.addError(error);
          },
        );

    return _userStreamController!.stream;
  }

  /// Get current user data (one-time fetch)
  Future<ConsolidatedUserModel?> getCurrentUser() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      // Try cache first
      if (_cachedUser != null && _cachedUser!.uid == user.uid) {
        return _cachedUser;
      }

      // Fetch from Firestore
      final doc = await _firestore.collection('users').doc(user.uid).get();
      
      if (doc.exists) {
        final userData = ConsolidatedUserModel.fromFirestore(doc);
        _cachedUser = userData;
        return userData;
      }

      // Try to migrate from old collections
      final migratedUser = await _migrateUserData(user.uid);
      if (migratedUser != null) {
        _cachedUser = migratedUser;
        return migratedUser;
      }

      // Create default user
      final defaultUser = ConsolidatedUserModel.createDefault(
        uid: user.uid,
        email: user.email ?? '',
      );
      await saveUser(defaultUser);
      _cachedUser = defaultUser;
      return defaultUser;

    } catch (e) {
      print('Error getting current user: $e');
      // Return cached user if available
      return _cachedUser;
    }
  }

  /// Save or update user data
  Future<void> saveUser(ConsolidatedUserModel user) async {
    try {
      final userData = user.toJson();
      userData['lastUpdated'] = FieldValue.serverTimestamp();
      
      await _firestore
          .collection('users')
          .doc(user.uid)
          .set(userData, SetOptions(merge: true));
      
      _cachedUser = user;
    } catch (e) {
      print('Error saving user: $e');
      throw Exception('Failed to save user data: $e');
    }
  }

  /// Update specific user fields
  Future<void> updateUser(String uid, Map<String, dynamic> updates) async {
    try {
      updates['lastUpdated'] = FieldValue.serverTimestamp();
      
      await _firestore
          .collection('users')
          .doc(uid)
          .update(updates);
      
      // Update cache if it's the current user
      if (_cachedUser?.uid == uid) {
        final doc = await _firestore.collection('users').doc(uid).get();
        if (doc.exists) {
          _cachedUser = ConsolidatedUserModel.fromFirestore(doc);
        }
      }
    } catch (e) {
      print('Error updating user: $e');
      throw Exception('Failed to update user data: $e');
    }
  }

  /// Migrate user data from old collections
  Future<ConsolidatedUserModel?> _migrateUserData(String uid) async {
    try {
      print('Attempting to migrate user data for $uid');
      
      // Try to get data from old user collection
      final oldUserDoc = await _firestore.collection('users').doc(uid).get();
      if (!oldUserDoc.exists) {
        print('No old user data found');
        return null;
      }

      final oldData = oldUserDoc.data()!;
      
      // Create new model from old data
      final migratedUser = ConsolidatedUserModel.fromLegacyUserModel(oldData);

      // Save to new structure
      await saveUser(migratedUser);
      print('Successfully migrated user data');
      
      return migratedUser;
    } catch (e) {
      print('Error migrating user data: $e');
      return null;
    }
  }

  /// Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding(String uid) async {
    try {
      final user = await getCurrentUser();
      return user?.comprehensiveOnboardingCompleted ?? false;
    } catch (e) {
      print('Error checking onboarding status: $e');
      return false;
    }
  }

  /// Update onboarding status
  Future<void> completeOnboarding(String uid) async {
    try {
      await updateUser(uid, {
        'preferences.comprehensiveOnboardingComplete': true,
        'preferences.onboardingComplete': true,
        'onboardingCompletedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error completing onboarding: $e');
      throw Exception('Failed to update onboarding status: $e');
    }
  }

  /// Clean up resources
  void _cleanup() {
    _userDocSubscription?.cancel();
    _userStreamController?.close();
    _userDocSubscription = null;
    _userStreamController = null;
    _currentUserId = null;
  }

  /// Dispose of all resources
  void dispose() {
    _cleanup();
    _cachedUser = null;
  }

  /// Sign out and clean up
  Future<void> signOut() async {
    _cleanup();
    _cachedUser = null;
    await _auth.signOut();
  }
  
  /// Check if user is logged in
  Future<bool> get isLoggedIn async {
    return _auth.currentUser != null;
  }
  
  /// Get user data (wrapper for getCurrentUser for backward compatibility)
  Future<ConsolidatedUserModel?> getUserData([String? uid]) async {
    if (uid != null && uid != _auth.currentUser?.uid) {
      // If requesting a different user's data, fetch directly
      try {
        final doc = await _firestore.collection('users').doc(uid).get();
        if (doc.exists) {
          return ConsolidatedUserModel.fromFirestore(doc);
        }
        return null;
      } catch (e) {
        print('Error fetching user data for $uid: $e');
        return null;
      }
    }
    // Otherwise use getCurrentUser
    return getCurrentUser();
  }

  /// Get user by ID (alias for getUserData)
  Future<ConsolidatedUserModel?> getUser(String uid) async {
    return getUserData(uid);
  }

  /// Update user data (wrapper for saveUser for backward compatibility)
  Future<void> updateUserData(ConsolidatedUserModel user) async {
    await saveUser(user);
  }

  /// Update user model (overload for updateUser)
  Future<void> updateUserModel(ConsolidatedUserModel user) async {
    await saveUser(user);
  }
  
  /// Update workout stats after completing a workout
  Future<void> updateWorkoutStats({
    required int workoutDuration,
    required double caloriesBurned,
  }) async {
    try {
      final user = await getCurrentUser();
      if (user == null) return;
      
      // Update stats
      final updatedStats = user.stats.copyWith(
        totalWorkouts: user.stats.totalWorkouts + 1,
        totalMinutes: user.stats.totalMinutes + workoutDuration,
        totalCaloriesBurned: (user.stats.totalCaloriesBurned ?? 0) + caloriesBurned.round(),
        lastWorkoutDate: DateTime.now(),
      );
      
      // Update user with new stats
      final updatedUser = user.copyWith(stats: updatedStats);
      await saveUser(updatedUser);
      
    } catch (e) {
      print('Error updating workout stats: $e');
    }
  }
  
  /// Sign in with email and password
  Future<ConsolidatedUserModel?> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        return await getCurrentUser();
      }
      return null;
    } catch (e) {
      print('Error signing in: $e');
      throw e;
    }
  }
  
  /// Register with email and password
  Future<ConsolidatedUserModel?> registerWithEmailAndPassword(
    String email, 
    String password, 
    String displayName,
  ) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(displayName);
        
        // Create default user profile
        final newUser = ConsolidatedUserModel.createDefault(
          uid: credential.user!.uid,
          email: email,
          displayName: displayName,
        );
        
        await saveUser(newUser);
        return newUser;
      }
      return null;
    } catch (e) {
      print('Error registering: $e');
      throw e;
    }
  }
  
  /// Delete user data
  Future<void> deleteUserData(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).delete();
      
      if (_cachedUser?.uid == uid) {
        _cachedUser = null;
      }
    } catch (e) {
      print('Error deleting user data: $e');
      throw Exception('Failed to delete user data: $e');
    }
  }
}