import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/consolidated_user_model.dart';

class UserDataMigrationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Migrate all users from old collections to new consolidated collection
  Future<MigrationResult> migrateAllUsers() async {
    final result = MigrationResult();
    
    try {
      print('🚀 Starting user data migration...');
      
      // Get all users from the main users collection
      final usersSnapshot = await _firestore.collection('users').get();
      
      for (final userDoc in usersSnapshot.docs) {
        try {
          final migrationSuccess = await _migrateUserData(userDoc.id);
          if (migrationSuccess) {
            result.successCount++;
            print('✅ Migrated user: ${userDoc.id}');
          } else {
            result.failureCount++;
            result.failedUsers.add(userDoc.id);
            print('❌ Failed to migrate user: ${userDoc.id}');
          }
        } catch (e) {
          result.failureCount++;
          result.failedUsers.add(userDoc.id);
          print('❌ Error migrating user ${userDoc.id}: $e');
        }
      }
      
      print('📊 Migration completed:');
      print('   ✅ Successful: ${result.successCount}');
      print('   ❌ Failed: ${result.failureCount}');
      
      return result;
    } catch (e) {
      print('💥 Migration failed: $e');
      result.error = e.toString();
      return result;
    }
  }

  // Migrate a single user's data
  Future<bool> _migrateUserData(String uid) async {
    try {
      // Check if user already exists in consolidated collection
      final existingDoc = await _firestore
          .collection('users_consolidated')
          .doc(uid)
          .get();
      
      if (existingDoc.exists) {
        print('⏭️ User $uid already migrated, skipping...');
        return true;
      }

      final userData = <String, dynamic>{};

      // Fetch from all old collections
      final futures = await Future.wait([
        _firestore.collection('users').doc(uid).get(),
        _firestore.collection('user_profiles').doc(uid).get(),
        _firestore.collection('comprehensive_onboarding').doc(uid).get(),
        _firestore.collection('fitness_goals').doc(uid).get(),
        _firestore.collection('fitness_levels').doc(uid).get(),
        _firestore.collection('workout_preferences').doc(uid).get(),
      ]);

      // Merge data from all collections
      for (final doc in futures) {
        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          userData.addAll(data);
        }
      }

      if (userData.isEmpty) {
        print('⚠️ No data found for user: $uid');
        return false;
      }

      // Create consolidated user model
      final consolidatedUser = ConsolidatedUserModel.fromLegacyUserModel(userData);
      
      // Save to new consolidated collection
      await _firestore
          .collection('users_consolidated')
          .doc(uid)
          .set(consolidatedUser.toJson());

      return true;
    } catch (e) {
      print('⚠️ Error migrating user $uid: $e');
      return false;
    }
  }

  // Migrate a specific user by UID
  Future<bool> migrateUser(String uid) async {
    try {
      print('🔄 Migrating user: $uid');
      final success = await _migrateUserData(uid);
      
      if (success) {
        print('✅ User $uid migrated successfully');
      } else {
        print('❌ Failed to migrate user $uid');
      }
      
      return success;
    } catch (e) {
      print('💥 Error migrating user $uid: $e');
      return false;
    }
  }

  // Verify migration integrity
  Future<VerificationResult> verifyMigration() async {
    final result = VerificationResult();
    
    try {
      print('🔍 Verifying migration integrity...');
      
      // Get counts from old and new collections
      final oldUsersCount = (await _firestore.collection('users').get()).docs.length;
      final newUsersCount = (await _firestore.collection('users_consolidated').get()).docs.length;
      
      result.oldCollectionCount = oldUsersCount;
      result.newCollectionCount = newUsersCount;
      result.isComplete = oldUsersCount == newUsersCount;
      
      if (result.isComplete) {
        print('✅ Migration verification passed');
        print('   📊 Users in old collection: $oldUsersCount');
        print('   📊 Users in new collection: $newUsersCount');
      } else {
        print('⚠️ Migration verification failed');
        print('   📊 Users in old collection: $oldUsersCount');
        print('   📊 Users in new collection: $newUsersCount');
        print('   📊 Missing users: ${oldUsersCount - newUsersCount}');
      }
      
      return result;
    } catch (e) {
      print('💥 Verification failed: $e');
      result.error = e.toString();
      return result;
    }
  }

  // Get migration status
  Future<MigrationStatus> getMigrationStatus() async {
    try {
      final oldUsersSnapshot = await _firestore.collection('users').get();
      final newUsersSnapshot = await _firestore.collection('users_consolidated').get();
      
      final oldUserIds = oldUsersSnapshot.docs.map((doc) => doc.id).toSet();
      final newUserIds = newUsersSnapshot.docs.map((doc) => doc.id).toSet();
      
      final migratedUsers = oldUserIds.intersection(newUserIds);
      final pendingUsers = oldUserIds.difference(newUserIds);
      
      return MigrationStatus(
        totalUsers: oldUserIds.length,
        migratedUsers: migratedUsers.length,
        pendingUsers: pendingUsers.toList(),
        isComplete: pendingUsers.isEmpty,
      );
    } catch (e) {
      print('⚠️ Error getting migration status: $e');
      return MigrationStatus(
        totalUsers: 0,
        migratedUsers: 0,
        pendingUsers: [],
        isComplete: false,
        error: e.toString(),
      );
    }
  }

  // Clean up old collections after successful migration
  Future<CleanupResult> cleanupOldCollections({
    bool dryRun = true,
    List<String>? specificUsers,
  }) async {
    final result = CleanupResult();
    
    try {
      print('🧹 ${dryRun ? "Simulating" : "Performing"} cleanup of old collections...');
      
      // Get users to clean up
      List<String> usersToCleanup;
      if (specificUsers != null) {
        usersToCleanup = specificUsers;
      } else {
        final usersSnapshot = await _firestore.collection('users_consolidated').get();
        usersToCleanup = usersSnapshot.docs.map((doc) => doc.id).toList();
      }
      
      final collectionsToCleanup = [
        'user_profiles',
        'comprehensive_onboarding',
        'fitness_goals',
        'fitness_levels',
        'workout_preferences',
      ];
      
      for (final userId in usersToCleanup) {
        try {
          if (!dryRun) {
            final batch = _firestore.batch();
            
            for (final collection in collectionsToCleanup) {
              batch.delete(_firestore.collection(collection).doc(userId));
            }
            
            await batch.commit();
          }
          
          result.cleanedUsers.add(userId);
          print('${dryRun ? "Would clean" : "Cleaned"} user: $userId');
        } catch (e) {
          result.failedUsers.add(userId);
          print('❌ Failed to clean user $userId: $e');
        }
      }
      
      result.successCount = result.cleanedUsers.length;
      result.failureCount = result.failedUsers.length;
      
      print('📊 Cleanup ${dryRun ? "simulation" : "operation"} completed:');
      print('   ✅ Successful: ${result.successCount}');
      print('   ❌ Failed: ${result.failureCount}');
      
      return result;
    } catch (e) {
      print('💥 Cleanup failed: $e');
      result.error = e.toString();
      return result;
    }
  }

  // Rollback migration (restore from old collections)
  Future<RollbackResult> rollbackMigration({
    List<String>? specificUsers,
  }) async {
    final result = RollbackResult();
    
    try {
      print('🔄 Rolling back migration...');
      
      List<String> usersToRollback;
      if (specificUsers != null) {
        usersToRollback = specificUsers;
      } else {
        final snapshot = await _firestore.collection('users_consolidated').get();
        usersToRollback = snapshot.docs.map((doc) => doc.id).toList();
      }
      
      for (final userId in usersToRollback) {
        try {
          await _firestore.collection('users_consolidated').doc(userId).delete();
          result.rolledBackUsers.add(userId);
          print('✅ Rolled back user: $userId');
        } catch (e) {
          result.failedUsers.add(userId);
          print('❌ Failed to rollback user $userId: $e');
        }
      }
      
      result.successCount = result.rolledBackUsers.length;
      result.failureCount = result.failedUsers.length;
      
      print('📊 Rollback completed:');
      print('   ✅ Successful: ${result.successCount}');
      print('   ❌ Failed: ${result.failureCount}');
      
      return result;
    } catch (e) {
      print('💥 Rollback failed: $e');
      result.error = e.toString();
      return result;
    }
  }
}

// Result classes
class MigrationResult {
  int successCount = 0;
  int failureCount = 0;
  List<String> failedUsers = [];
  String? error;
  
  bool get isSuccessful => error == null && failureCount == 0;
  int get totalProcessed => successCount + failureCount;
}

class VerificationResult {
  int oldCollectionCount = 0;
  int newCollectionCount = 0;
  bool isComplete = false;
  String? error;
}

class MigrationStatus {
  final int totalUsers;
  final int migratedUsers;
  final List<String> pendingUsers;
  final bool isComplete;
  final String? error;
  
  MigrationStatus({
    required this.totalUsers,
    required this.migratedUsers,
    required this.pendingUsers,
    required this.isComplete,
    this.error,
  });
  
  double get progress => totalUsers > 0 ? migratedUsers / totalUsers : 0.0;
}

class CleanupResult {
  int successCount = 0;
  int failureCount = 0;
  List<String> cleanedUsers = [];
  List<String> failedUsers = [];
  String? error;
  
  bool get isSuccessful => error == null && failureCount == 0;
}

class RollbackResult {
  int successCount = 0;
  int failureCount = 0;
  List<String> rolledBackUsers = [];
  List<String> failedUsers = [];
  String? error;
  
  bool get isSuccessful => error == null && failureCount == 0;
} 