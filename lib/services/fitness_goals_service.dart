import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/fitness_goals_model.dart' as goals_models;
import '../models/consolidated_user_model_fixed.dart';
import 'consolidated_user_service_fixed.dart';

class FitnessGoalsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ConsolidatedUserServiceFixed _userService = ConsolidatedUserServiceFixed();

  // Only use users collection
  CollectionReference get _usersCollection => _firestore.collection('users');

  // Save fitness goals
  Future<bool> saveFitnessGoals(goals_models.FitnessGoalsModel goals) async {
    try {
      final user = await _userService.getUserData(goals.userId);
      if (user == null) return false;

      // Convert fitness goals to consolidated user model format
      final convertedGoals = goals.goals.map((goal) => FitnessGoal(
        type: _convertGoalType(goal.type),
        priority: goal.priority,
        sportActivity: goal.sportActivity,
        selectedAt: goal.selectedAt,
      )).toList();

      final fitnessProfile = user.fitnessProfile.copyWith(
        goals: convertedGoals,
      );

      final updatedPreferences = user.preferences.copyWith(
        onboardingComplete: true,
        fitnessGoalsSet: true,
      );

      final updatedUser = user.copyWith(
        fitnessProfile: fitnessProfile,
        preferences: updatedPreferences,
      );

      return await _userService.updateUserData(updatedUser);
    } catch (e) {
      print('Error saving fitness goals: $e');
      return false;
    }
  }

  // Helper method to convert FitnessGoalType enums
  FitnessGoalType _convertGoalType(goals_models.FitnessGoalType type) {
    switch (type) {
      case goals_models.FitnessGoalType.notReady:
        return FitnessGoalType.notReady;
      case goals_models.FitnessGoalType.sportSpecific:
        return FitnessGoalType.sportSpecific;
      case goals_models.FitnessGoalType.buildMuscle:
        return FitnessGoalType.buildMuscle;
      case goals_models.FitnessGoalType.calisthenics:
        return FitnessGoalType.calisthenics;
      case goals_models.FitnessGoalType.weightLoss:
        return FitnessGoalType.weightLoss;
      case goals_models.FitnessGoalType.healthOptimization:
        return FitnessGoalType.healthOptimization;
      case goals_models.FitnessGoalType.cardioBunny:
        return FitnessGoalType.cardioBunny;
      case goals_models.FitnessGoalType.increaseStrength:
        return FitnessGoalType.increaseStrength;
    }
  }

  // Helper method to convert back to goals model format
  goals_models.FitnessGoalType _convertBackGoalType(FitnessGoalType type) {
    switch (type) {
      case FitnessGoalType.notReady:
        return goals_models.FitnessGoalType.notReady;
      case FitnessGoalType.sportSpecific:
        return goals_models.FitnessGoalType.sportSpecific;
      case FitnessGoalType.buildMuscle:
        return goals_models.FitnessGoalType.buildMuscle;
      case FitnessGoalType.calisthenics:
        return goals_models.FitnessGoalType.calisthenics;
      case FitnessGoalType.weightLoss:
        return goals_models.FitnessGoalType.weightLoss;
      case FitnessGoalType.healthOptimization:
        return goals_models.FitnessGoalType.healthOptimization;
      case FitnessGoalType.cardioBunny:
        return goals_models.FitnessGoalType.cardioBunny;
      case FitnessGoalType.increaseStrength:
        return goals_models.FitnessGoalType.increaseStrength;
      case FitnessGoalType.increaseStamina:
        return goals_models.FitnessGoalType.cardioBunny; // Map to closest equivalent
    }
  }

  // Get fitness goals for a user
  Future<goals_models.FitnessGoalsModel?> getFitnessGoals(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return null;

      // Convert consolidated user model to fitness goals model
      final convertedGoals = user.fitnessProfile.goals.map((goal) => goals_models.FitnessGoal(
        type: _convertBackGoalType(goal.type),
        priority: goal.priority,
        sportActivity: goal.sportActivity,
        selectedAt: goal.selectedAt,
      )).toList();

      return goals_models.FitnessGoalsModel(
        userId: userId,
        goals: convertedGoals,
        additionalNotes: user.fitnessProfile.personalCoachNotes,
        completedAt: user.updatedAt,
        isOnboardingComplete: user.preferences.fitnessGoalsSet,
      );
    } catch (e) {
      print('Error getting fitness goals: $e');
      return null;
    }
  }

  // Update fitness goals
  Future<bool> updateFitnessGoals(goals_models.FitnessGoalsModel goals) async {
    try {
      // Just use the save method which handles updates
      return await saveFitnessGoals(goals);
    } catch (e) {
      print('Error updating fitness goals: $e');
      return false;
    }
  }

  // Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      return user?.preferences.fitnessGoalsSet ?? false;
    } catch (e) {
      print('Error checking onboarding status: $e');
      return false;
    }
  }

  // Delete fitness goals (for testing or user request)
  Future<bool> deleteFitnessGoals(String userId) async {
    try {
      final user = await _userService.getUserData(userId);
      if (user == null) return false;

      // Reset fitness goals in consolidated user model
      final fitnessProfile = user.fitnessProfile.copyWith(
        goals: <FitnessGoal>[],
        personalCoachNotes: null,
      );

      final updatedPreferences = user.preferences.copyWith(
        onboardingComplete: false,
        fitnessGoalsSet: false,
      );

      final updatedUser = user.copyWith(
        fitnessProfile: fitnessProfile,
        preferences: updatedPreferences,
      );

      return await _userService.updateUserData(updatedUser);
    } catch (e) {
      print('Error deleting fitness goals: $e');
      return false;
    }
  }
} 