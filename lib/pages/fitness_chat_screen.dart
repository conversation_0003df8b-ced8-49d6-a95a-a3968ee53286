import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/workout_agent_service.dart';
import '../models/genkit_models.dart';

class FitnessChatScreen extends StatefulWidget {
  const FitnessChatScreen({Key? key}) : super(key: key);

  @override
  State<FitnessChatScreen> createState() => _FitnessChatScreenState();
}

class _FitnessChatScreenState extends State<FitnessChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();
  final FitnessChatService _chatService = FitnessChatService();
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _authError;

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }
  
  void _checkAuthentication() {
    final user = FirebaseAuth.instance.currentUser;
    setState(() {
      _isAuthenticated = user != null;
      if (!_isAuthenticated) {
        _authError = 'Please sign in to use the AI fitness coach';
      } else {
        // Add welcome message only if authenticated
        _messages.add(ChatMessage(
          text: "👋 Hi! I'm your AI fitness coach. I can help you with workout recommendations, exercise tips, nutrition advice, and answer any fitness-related questions. How can I help you today?",
          isUser: false,
          timestamp: DateTime.now(),
          suggestions: [
            "What's a good workout for beginners?",
            "How do I build muscle?",
            "Recommend exercises for weight loss",
            "Create a workout plan for me",
          ],
        ));
      }
    });
    
    // Listen to auth state changes
    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      if (mounted) {
        setState(() {
          _isAuthenticated = user != null;
          _authError = user == null ? 'Please sign in to use the AI fitness coach' : null;
        });
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage([String? messageText]) async {
    final text = messageText ?? _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(
        text: text,
        isUser: true,
        timestamp: DateTime.now(),
      ));
      _messageController.clear();
      _isLoading = true;
    });

    // Scroll to bottom
    _scrollToBottom();

    try {
      final response = await _chatService.sendMessage(text);
      
      setState(() {
        _messages.add(ChatMessage(
          text: response.response,
          isUser: false,
          timestamp: DateTime.now(),
          suggestions: response.suggestions,
        ));
        _isLoading = false;
      });
      
      _scrollToBottom();
    } catch (e) {
      setState(() {
        String errorMessage = 'Sorry, I couldn\'t process your message.';
        
        if (e.toString().contains('sign in')) {
          errorMessage = '🔒 Please sign in to use the AI fitness coach.';
        } else if (e.toString().contains('internet connection') || 
                   e.toString().contains('connect to chat service')) {
          errorMessage = '📡 Connection error. Please check your internet and try again.';
        } else if (e.toString().contains('not found')) {
          errorMessage = '⚠️ Chat service is being set up. Please try again later.';
        } else if (e.toString().contains('timed out')) {
          errorMessage = '⏱️ Request timed out. Please try again.';
        } else {
          errorMessage = '❌ $errorMessage\n\nError details: ${e.toString().replaceAll('Exception: ', '')}';
        }
        
        _messages.add(ChatMessage(
          text: errorMessage,
          isUser: false,
          timestamp: DateTime.now(),
          suggestions: e.toString().contains('sign in') ? null : ['Try again'],
        ));
        _isLoading = false;
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Fitness Coach'),
        centerTitle: true,
        actions: [
          // Connection status indicator
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(
              _isAuthenticated ? Icons.cloud_done : Icons.cloud_off,
              color: _isAuthenticated ? Colors.green : Colors.orange,
              size: 20,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _chatService.startNewConversation();
              setState(() {
                _messages.clear();
                _messages.add(ChatMessage(
                  text: "👋 New conversation started! How can I help you with your fitness journey?",
                  isUser: false,
                  timestamp: DateTime.now(),
                  suggestions: [
                    "What's a good workout for beginners?",
                    "How do I build muscle?",
                    "Recommend exercises for weight loss",
                    "Create a workout plan for me",
                  ],
                ));
              });
            },
            tooltip: 'Start new conversation',
          ),
        ],
      ),
      body: Column(
        children: [
          if (_authError != null)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _authError!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length + (_isLoading ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _messages.length && _isLoading) {
                  return _buildLoadingIndicator();
                }
                return _buildMessage(_messages[index]);
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Ask about workouts, nutrition, exercises...',
                      filled: true,
                      fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                    onSubmitted: (_) => _isAuthenticated ? _sendMessage() : null,
                    enabled: !_isLoading && _isAuthenticated,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: (_isLoading || !_isAuthenticated) ? null : _sendMessage,
                  icon: const Icon(Icons.send),
                  style: IconButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage(ChatMessage message) {
    return Column(
      crossAxisAlignment: 
          message.isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            decoration: BoxDecoration(
              color: message.isUser
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              message.text,
              style: TextStyle(
                color: message.isUser
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ),
        if (message.suggestions != null && message.suggestions!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: message.suggestions!.map((suggestion) {
                return ActionChip(
                  label: Text(suggestion),
                  onPressed: () {
                    if (suggestion == 'Try again' && _messages.length >= 2) {
                      // Resend the last user message
                      final lastUserMessage = _messages
                          .where((m) => m.isUser)
                          .lastOrNull;
                      if (lastUserMessage != null) {
                        _sendMessage(lastUserMessage.text);
                      }
                    } else {
                      _sendMessage(suggestion);
                    }
                  },
                  backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingIndicator() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 12),
            const Text('AI is thinking...'),
          ],
        ),
      ),
    );
  }
}