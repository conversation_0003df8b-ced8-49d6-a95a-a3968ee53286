import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_profile_model.dart';
import '../features/onboarding/providers/onboarding_providers.dart';
import '../features/auth/providers/auth_providers.dart';

class ComprehensiveOnboardingScreen extends ConsumerStatefulWidget {
  final bool isFromProfile;

  const ComprehensiveOnboardingScreen({
    super.key,
    this.isFromProfile = false,
  });

  @override
  ConsumerState<ComprehensiveOnboardingScreen> createState() =>
      _ComprehensiveOnboardingScreenState();
}

class _ComprehensiveOnboardingScreenState
    extends ConsumerState<ComprehensiveOnboardingScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();

  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // State variables
  int _currentStep = 0;
  bool _isLoading = false;

  // Text controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _ageController = TextEditingController();
  final TextEditingController _heightFeetController = TextEditingController();
  final TextEditingController _heightInchesController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _sportController = TextEditingController();
  final TextEditingController _personalCoachNotesController = TextEditingController();
  final TextEditingController _exercisesToAvoidController = TextEditingController();
  final TextEditingController _additionalNotesController = TextEditingController();

  // Form data
  Gender? _selectedGender;
  List<FitnessGoal> _selectedGoals = [];
  String? _sportActivity;
  double _cardioLevel = 0.5;
  double _weightliftingLevel = 0.5;
  List<WorkoutEnvironment> _selectedEnvironments = [];
  int _workoutsPerWeek = 3;
  int? _workoutDurationMinutes = 45;
  bool _optimizeTimeForMe = false;

  // Services
  late final _userService = ref.read(authServiceProvider);
  late final _onboardingService = ref.read(comprehensiveOnboardingServiceProvider);

  final List<String> _stepTitles = [
    'About You',
    'Fitness Goals',
    'Current Fitness Level',
    'Workout Environment',
    'Workout Schedule',
    'Anything Else',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();
    _loadExistingData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _heightFeetController.dispose();
    _heightInchesController.dispose();
    _weightController.dispose();
    _sportController.dispose();
    _personalCoachNotesController.dispose();
    _exercisesToAvoidController.dispose();
    _additionalNotesController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingData() async {
    if (widget.isFromProfile && _userService.currentUser != null) {
      try {
        final onboarding = await _onboardingService.getComprehensiveOnboarding(_userService.currentUser!.uid);
        if (!mounted) return;

        if (onboarding != null) {
          setState(() {
            // Profile data
            _nameController.text = onboarding.profile.name;
            _selectedGender = onboarding.profile.gender;
            if (onboarding.profile.age != null) {
              _ageController.text = onboarding.profile.age.toString();
            }
            if (onboarding.profile.heightFeet != null) {
              final totalFeet = onboarding.profile.heightFeet!;
              final feet = totalFeet.floor();
              final inches = ((totalFeet - feet) * 12).round();
              _heightFeetController.text = feet.toString();
              _heightInchesController.text = inches.toString();
            }
            if (onboarding.profile.weightLbs != null) {
              _weightController.text = onboarding.profile.weightLbs.toString();
            }

            // Fitness goals
            _selectedGoals = List.from(onboarding.fitnessGoals);
            _personalCoachNotesController.text = onboarding.personalCoachNotes ?? '';

            // Find sport-specific goal
            final sportGoal = onboarding.fitnessGoals.firstWhere(
              (g) => g.type == FitnessGoalType.sportSpecific,
              orElse: () => FitnessGoal(
                type: FitnessGoalType.sportSpecific,
                priority: 0,
                selectedAt: DateTime.now(),
              ),
            );
            if (sportGoal.sportActivity != null) {
              _sportController.text = sportGoal.sportActivity!;
              _sportActivity = sportGoal.sportActivity;
            }

            // Fitness level
            _cardioLevel = onboarding.fitnessLevel.cardioLevel;
            _weightliftingLevel = onboarding.fitnessLevel.weightliftingLevel;
            _exercisesToAvoidController.text = onboarding.fitnessLevel.exercisesToAvoid ?? '';

            // Workout preferences
            _selectedEnvironments = List.from(onboarding.workoutPreferences.environments);
            _workoutsPerWeek = onboarding.workoutPreferences.workoutsPerWeek;
            _workoutDurationMinutes = onboarding.workoutPreferences.workoutDurationMinutes;
            _optimizeTimeForMe = _workoutDurationMinutes == null;
            _additionalNotesController.text = onboarding.workoutPreferences.additionalNotes ?? '';
          });
        }
      } catch (e) {
        print('Error loading existing onboarding data: $e');
      }
    }
  }

  void _nextStep() {
    FocusScope.of(context).unfocus();

    if (!_canProceedFromStep(_currentStep)) {
      _showSnackBar('Please complete all required fields', isError: true);
      return;
    }

    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    FocusScope.of(context).unfocus();

    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceedFromStep(int step) {
    switch (step) {
      case 0: // About You
        return _nameController.text.trim().isNotEmpty &&
               _selectedGender != null &&
               _ageController.text.trim().isNotEmpty &&
               _heightFeetController.text.trim().isNotEmpty &&
               _heightInchesController.text.trim().isNotEmpty &&
               _weightController.text.trim().isNotEmpty;
      case 1: // Fitness Goals
        print('🔍 Validating Fitness Goals Step:');
        print('   Selected goals count: ${_selectedGoals.length}');
        if (_selectedGoals.isNotEmpty) {
          for (int i = 0; i < _selectedGoals.length; i++) {
            print('   Goal $i: ${_selectedGoals[i].type}');
          }
        }
        
        if (_selectedGoals.isEmpty) {
          print('   ❌ No goals selected');
          return false;
        }
        
        // Check if sport-specific goal requires input
        final hasSportSpecific = _selectedGoals.any((g) => g.type == FitnessGoalType.sportSpecific);
        print('   Has sport-specific goal: $hasSportSpecific');
        if (hasSportSpecific) {
          print('   Sport activity: "$_sportActivity"');
          print('   Sport controller text: "${_sportController.text.trim()}"');
        }
        
        if (hasSportSpecific) {
          final sportText = _sportActivity?.trim() ?? _sportController.text.trim();
          if (sportText.isEmpty) {
            print('   ❌ Sport-specific goal selected but no sport activity provided');
            return false;
          }
          // Update _sportActivity if it's empty but controller has text
          if ((_sportActivity == null || _sportActivity!.isEmpty) && _sportController.text.trim().isNotEmpty) {
            _sportActivity = _sportController.text.trim();
            print('   🔄 Updated _sportActivity from controller: "$_sportActivity"');
          }
        }
        
        print('   ✅ Fitness goals validation passed');
        return true;
      case 2: // Current Fitness Level
        return true; // Sliders have default values
      case 3: // Workout Environment
        return _selectedEnvironments.isNotEmpty;
      case 4: // Workout Schedule
        return _workoutsPerWeek >= 2 && _workoutsPerWeek <= 7;
      case 5: // Anything Else
        return true; // Optional
      default:
        return true;
    }
  }

  double _convertHeightToFeet() {
    final feet = int.tryParse(_heightFeetController.text.trim()) ?? 0;
    final inches = int.tryParse(_heightInchesController.text.trim()) ?? 0;
    return feet + (inches / 12.0);
  }

  Future<void> _saveOnboarding() async {
    FocusScope.of(context).unfocus();

    if (!_canProceedFromStep(_currentStep)) {
      _showSnackBar('Please complete all required fields', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _userService.currentUser!.uid;

      // Update sport activity for sport-specific goals
      final updatedGoals = _selectedGoals.map((goal) {
        if (goal.type == FitnessGoalType.sportSpecific) {
          return FitnessGoal(
            type: goal.type,
            priority: goal.priority,
            sportActivity: _sportActivity,
            selectedAt: goal.selectedAt,
          );
        }
        return goal;
      }).toList();

      final onboardingModel = ComprehensiveOnboardingModel(
        profile: UserProfileModel(
          userId: userId,
          name: _nameController.text.trim(),
          gender: _selectedGender,
          age: int.tryParse(_ageController.text.trim()),
          heightFeet: _convertHeightToFeet(),
          weightLbs: double.tryParse(_weightController.text.trim()),
          createdAt: DateTime.now(),
          updatedAt: widget.isFromProfile ? DateTime.now() : null,
        ),
        fitnessGoals: updatedGoals,
        personalCoachNotes: _personalCoachNotesController.text.trim().isNotEmpty
            ? _personalCoachNotesController.text.trim()
            : null,
        fitnessLevel: FitnessLevelModel(
          userId: userId,
          cardioLevel: _cardioLevel,
          weightliftingLevel: _weightliftingLevel,
          exercisesToAvoid: _exercisesToAvoidController.text.trim().isNotEmpty
              ? _exercisesToAvoidController.text.trim()
              : null,
          createdAt: DateTime.now(),
        ),
        workoutPreferences: WorkoutPreferencesModel(
          userId: userId,
          environments: _selectedEnvironments,
          workoutsPerWeek: _workoutsPerWeek,
          workoutDurationMinutes: _optimizeTimeForMe ? null : _workoutDurationMinutes,
          additionalNotes: _additionalNotesController.text.trim().isNotEmpty
              ? _additionalNotesController.text.trim()
              : null,
          createdAt: DateTime.now(),
        ),
        isComplete: true,
        completedAt: DateTime.now(),
      );

      final success = widget.isFromProfile
          ? await _onboardingService.updateComprehensiveOnboarding(onboardingModel)
          : await _onboardingService.saveComprehensiveOnboarding(onboardingModel);

      if (success) {
        _showSnackBar(
          widget.isFromProfile
              ? 'Profile updated successfully!'
              : 'Welcome to AgenticFit! Your personalized journey begins now 🎯',
          isSuccess: true,
        );

        await Future.delayed(const Duration(milliseconds: 100));

        if (mounted) {
          if (widget.isFromProfile) {
            Navigator.of(context).pop(true);
          } else {
            // Let AuthWrapper handle navigation to home page
            // Force a rebuild by updating the auth state
            await Future.delayed(const Duration(milliseconds: 500));
            // The auth state will trigger a rebuild and show the home page
          }
        }
      } else {
        _showSnackBar('Failed to save profile. Please try again.', isError: true);
      }
    } catch (e) {
      _showSnackBar('An error occurred. Please try again.', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message, {bool isError = false, bool isSuccess = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : isSuccess
                ? Theme.of(context).colorScheme.secondary
                : Theme.of(context).colorScheme.surface,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildProgressIndicator(),
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildAboutYouStep(),
                        _buildFitnessGoalsStep(),
                        _buildFitnessLevelStep(),
                        _buildWorkoutEnvironmentStep(),
                        _buildWorkoutScheduleStep(),
                        _buildAnythingElseStep(),
                      ],
                    ),
                  ),
                ),
              ),
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          if (widget.isFromProfile)
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_ios,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            )
          else
            const SizedBox(width: 48),
          Expanded(
            child: Text(
              widget.isFromProfile ? 'Update Profile' : 'Welcome to AgenticFit',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          Row(
            children: List.generate(_stepTitles.length, (index) {
              final isActive = index == _currentStep;
              final isCompleted = index < _currentStep;
              
              return Expanded(
                child: Container(
                  height: 4,
                  margin: EdgeInsets.only(right: index < _stepTitles.length - 1 ? 8 : 0),
                  decoration: BoxDecoration(
                    color: isCompleted || isActive
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          Text(
            '${_currentStep + 1} of ${_stepTitles.length}: ${_stepTitles[_currentStep]}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Step 1: About You
  Widget _buildAboutYouStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About You',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tell us a bit about yourself to personalize your fitness journey',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          // Name
          _buildTextField(
            controller: _nameController,
            label: 'Name',
            hint: 'Enter your name',
            icon: Icons.person,
          ),
          const SizedBox(height: 16),
          
          // Gender
          _buildSectionTitle('Gender'),
          const SizedBox(height: 8),
          Wrap(
            spacing: 12,
            children: Gender.values.map((gender) {
              final isSelected = gender == _selectedGender;
              return ChoiceChip(
                label: Text(_getGenderLabel(gender)),
                selected: isSelected,
                onSelected: (_) => setState(() => _selectedGender = gender),
                backgroundColor: Theme.of(context).colorScheme.surface,
                selectedColor: Theme.of(context).colorScheme.primary,
                labelStyle: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          
          // Age
          _buildTextField(
            controller: _ageController,
            label: 'Age',
            hint: 'Enter your age',
            icon: Icons.cake,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          
          // Height (Imperial)
          _buildSectionTitle('Height'),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  controller: _heightFeetController,
                  label: 'Feet',
                  hint: '5',
                  icon: Icons.height,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                  controller: _heightInchesController,
                  label: 'Inches',
                  hint: '6',
                  icon: Icons.straighten,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Weight (Imperial)
          _buildTextField(
            controller: _weightController,
            label: 'Weight (lbs)',
            hint: 'Enter your weight in pounds',
            icon: Icons.monitor_weight,
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  // Step 2: Fitness Goals
  Widget _buildFitnessGoalsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fitness Goals',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select all that apply and arrange them in your preferred order',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          if (_selectedGoals.isNotEmpty) ...[
            _buildSelectedGoalsSection(),
            const SizedBox(height: 24),
          ],
          
          _buildGoalsList(),
          const SizedBox(height: 24),
          
          // Personal Coach Notes
          _buildSectionTitle('Is there anything else I should know as your personal health coach?'),
          const SizedBox(height: 8),
          TextField(
            controller: _personalCoachNotesController,
            maxLines: 3,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => FocusScope.of(context).unfocus(),
            decoration: _getInputDecoration('Share your fitness journey, challenges, or specific needs...'),
          ),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  // Step 3: Current Fitness Level
  Widget _buildFitnessLevelStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Fitness Level',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Help us understand your current fitness level',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),
          
          // Cardio Level
          _buildSectionTitle('Cardio'),
          const SizedBox(height: 8),
          Text(
            _getCardioLevelDescription(_cardioLevel),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            ),
            child: Slider(
              value: _cardioLevel,
              onChanged: (value) => setState(() => _cardioLevel = value),
              min: 0.0,
              max: 1.0,
              divisions: 10,
            ),
          ),
          const SizedBox(height: 24),
          
          // Weightlifting Level
          _buildSectionTitle('Weightlifting'),
          const SizedBox(height: 8),
          Text(
            _getWeightliftingLevelDescription(_weightliftingLevel),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            ),
            child: Slider(
              value: _weightliftingLevel,
              onChanged: (value) => setState(() => _weightliftingLevel = value),
              min: 0.0,
              max: 1.0,
              divisions: 10,
            ),
          ),
          const SizedBox(height: 32),
          
          // Exercises to Avoid
          _buildSectionTitle('Is there any particular exercises you would like to avoid?'),
          const SizedBox(height: 8),
          TextField(
            controller: _exercisesToAvoidController,
            maxLines: 2,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => FocusScope.of(context).unfocus(),
            decoration: _getInputDecoration('e.g., Running due to knee issues, overhead movements...'),
          ),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  // Step 4: Workout Environment
  Widget _buildWorkoutEnvironmentStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Environment',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select all that apply',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          ...WorkoutEnvironment.values.map((environment) {
            final isSelected = _selectedEnvironments.contains(environment);
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedEnvironments.remove(environment);
                      } else {
                        _selectedEnvironments.add(environment);
                      }
                    });
                    HapticFeedback.lightImpact();
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                          : Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _getEnvironmentIcon(environment),
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _getEnvironmentLabel(environment),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  // Step 5: Workout Schedule
  Widget _buildWorkoutScheduleStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Schedule',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Let\'s plan your workout schedule',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),
          
          // Workouts per week
          _buildSectionTitle('What should your number of workouts per week goal be?'),
          const SizedBox(height: 8),
          Text(
            'Science says between at least 2 and optimally 4 days a week',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  '$_workoutsPerWeek days per week',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 12),
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Theme.of(context).colorScheme.primary,
                    inactiveTrackColor: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    thumbColor: Theme.of(context).colorScheme.primary,
                    overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  ),
                  child: Slider(
                    value: _workoutsPerWeek.toDouble(),
                    onChanged: (value) => setState(() => _workoutsPerWeek = value.round()),
                    min: 2,
                    max: 7,
                    divisions: 5,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          
          // Workout duration
          _buildSectionTitle('How long would you like your workout to be?'),
          const SizedBox(height: 16),
          
          // Optimize time checkbox
          CheckboxListTile(
            title: Text(
              'No preference, optimize the time for me',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            value: _optimizeTimeForMe,
            onChanged: (value) {
              setState(() {
                _optimizeTimeForMe = value ?? false;
                if (_optimizeTimeForMe) {
                  _workoutDurationMinutes = null;
                }
              });
            },
            activeColor: Theme.of(context).colorScheme.primary,
            contentPadding: EdgeInsets.zero,
          ),
          
          if (!_optimizeTimeForMe) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    '${_workoutDurationMinutes ?? 45} minutes',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Theme.of(context).colorScheme.primary,
                      inactiveTrackColor: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                      thumbColor: Theme.of(context).colorScheme.primary,
                      overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    ),
                    child: Slider(
                      value: (_workoutDurationMinutes ?? 45).toDouble(),
                      onChanged: (value) => setState(() => _workoutDurationMinutes = value.round()),
                      min: 15,
                      max: 120,
                      divisions: 21,
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  // Step 6: Anything Else
  Widget _buildAnythingElseStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Anything Else',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please share anything else you would like me to know?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          TextField(
            controller: _additionalNotesController,
            maxLines: 6,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => FocusScope.of(context).unfocus(),
            decoration: _getInputDecoration(
              'Share any additional information, preferences, limitations, or goals that would help us create the perfect fitness plan for you...',
            ),
          ),
          const SizedBox(height: 32),
          
          // Summary card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Ready to Start Your Journey!',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'We\'ll create a personalized fitness plan based on your profile, goals, and preferences. You can always update these settings later in your profile.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Theme.of(context).colorScheme.outline),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text(
                  'Previous',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : _currentStep == _stepTitles.length - 1
                      ? _saveOnboarding
                      : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _currentStep == _stepTitles.length - 1
                          ? (widget.isFromProfile ? 'Update Profile' : 'Complete Setup')
                          : 'Next',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for fitness goals
  void _toggleGoal(FitnessGoalInfo goalInfo) {
    FocusScope.of(context).unfocus();

    setState(() {
      final existingIndex = _selectedGoals.indexWhere((g) => g.type == goalInfo.type);
      if (existingIndex != -1) {
        print('🗑️ Removing goal: ${goalInfo.type}');
        _selectedGoals.removeAt(existingIndex);
        // Clear sport activity if removing sport-specific goal
        if (goalInfo.type == FitnessGoalType.sportSpecific) {
          _sportActivity = null;
          _sportController.clear();
        }
      } else {
        print('➕ Adding goal: ${goalInfo.type}');
        
        // For sport-specific goals, initialize with current controller text if available
        String? initialSportActivity;
        if (goalInfo.type == FitnessGoalType.sportSpecific) {
          initialSportActivity = _sportController.text.trim().isNotEmpty 
              ? _sportController.text.trim() 
              : _sportActivity;
          // Update _sportActivity to match controller if needed
          if (_sportController.text.trim().isNotEmpty && _sportActivity != _sportController.text.trim()) {
            _sportActivity = _sportController.text.trim();
          }
        }
        
        _selectedGoals.add(FitnessGoal(
          type: goalInfo.type,
          priority: _selectedGoals.length + 1,
          sportActivity: goalInfo.type == FitnessGoalType.sportSpecific ? initialSportActivity : null,
          selectedAt: DateTime.now(),
        ));
      }
      _updatePriorities();
      print('📋 Total goals now: ${_selectedGoals.length}');
    });

    HapticFeedback.lightImpact();
  }

  void _updatePriorities() {
    for (int i = 0; i < _selectedGoals.length; i++) {
      // For sport-specific goals, always use the current sport activity
      String? sportActivity = _selectedGoals[i].sportActivity;
      if (_selectedGoals[i].type == FitnessGoalType.sportSpecific) {
        sportActivity = _sportActivity?.trim().isNotEmpty == true 
            ? _sportActivity 
            : _sportController.text.trim().isNotEmpty 
                ? _sportController.text.trim() 
                : _selectedGoals[i].sportActivity;
      }
      
      _selectedGoals[i] = FitnessGoal(
        type: _selectedGoals[i].type,
        priority: i + 1,
        sportActivity: sportActivity,
        selectedAt: _selectedGoals[i].selectedAt,
      );
    }
  }

  void _reorderGoals(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final goal = _selectedGoals.removeAt(oldIndex);
      _selectedGoals.insert(newIndex, goal);
      _updatePriorities();
    });
    HapticFeedback.mediumImpact();
  }

  Widget _buildSelectedGoalsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.drag_indicator,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Your Goals (Drag to reorder)',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: ReorderableListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _selectedGoals.length,
            onReorder: _reorderGoals,
            itemBuilder: (context, index) {
              final goal = _selectedGoals[index];
              final goalInfo = FitnessGoalInfo.allGoals.firstWhere(
                (g) => g.type == goal.type,
              );

              return Container(
                key: ValueKey(goal.type),
                margin: EdgeInsets.only(
                  top: index == 0 ? 8 : 4,
                  bottom: index == _selectedGoals.length - 1 ? 8 : 4,
                  left: 8,
                  right: 8,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: ListTile(
                  leading: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    goalInfo.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  trailing: Icon(
                    Icons.drag_handle,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsList() {
    return Column(
      children: FitnessGoalInfo.allGoals.map((goalInfo) {
        final isSelected = _selectedGoals.any((g) => g.type == goalInfo.type);
        final priority = isSelected
            ? _selectedGoals.indexWhere((g) => g.type == goalInfo.type) + 1
            : null;

        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildGoalCard(
            goalInfo: goalInfo,
            isSelected: isSelected,
            priority: priority,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGoalCard({
    required FitnessGoalInfo goalInfo,
    required bool isSelected,
    int? priority,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _toggleGoal(goalInfo),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline.withOpacity(0.2),
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (isSelected && priority != null)
                    Container(
                      width: 28,
                      height: 28,
                      margin: const EdgeInsets.only(right: 12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '$priority',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  Expanded(
                    child: Text(
                      goalInfo.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            size: 16,
                            color: Theme.of(context).colorScheme.onPrimary,
                          )
                        : null,
                  ),
                ],
              ),
              if (goalInfo.recommendation != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Recommended for: ${goalInfo.recommendation}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Text(
                goalInfo.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  height: 1.5,
                ),
              ),
              if (goalInfo.requiresInput && isSelected) ...[
                const SizedBox(height: 12),
                TextField(
                  controller: _sportController,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {
                    setState(() {
                      _sportActivity = value.trim();
                      // Update the sport-specific goal with the new sport activity
                      final sportGoalIndex = _selectedGoals.indexWhere(
                        (g) => g.type == FitnessGoalType.sportSpecific
                      );
                      if (sportGoalIndex != -1) {
                        _selectedGoals[sportGoalIndex] = FitnessGoal(
                          type: FitnessGoalType.sportSpecific,
                          priority: _selectedGoals[sportGoalIndex].priority,
                          sportActivity: _sportActivity,
                          selectedAt: _selectedGoals[sportGoalIndex].selectedAt,
                        );
                      }
                    });
                  },
                  onSubmitted: (_) => FocusScope.of(context).unfocus(),
                  decoration: _getInputDecoration('Example: Marathon, Volleyball, Tactical Readiness'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          textInputAction: TextInputAction.next,
          onEditingComplete: () => FocusScope.of(context).nextFocus(),
          decoration: _getInputDecoration(hint),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  InputDecoration _getInputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
      isDense: true,
    );
  }

  String _getGenderLabel(Gender gender) {
    switch (gender) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.other:
        return 'Other';
      case Gender.preferNotToSay:
        return 'Prefer not to say';
    }
  }

  String _getCardioLevelDescription(double level) {
    if (level <= 0.2) return 'I cannot walk more than half a mile and need frequent breaks';
    if (level <= 0.4) return 'I can walk a mile but get winded easily';
    if (level <= 0.6) return 'I can jog short distances and do light cardio';
    if (level <= 0.8) return 'I can run several miles and do moderate cardio';
    return 'I regularly run long distance (6 miles/10km or more)';
  }

  String _getWeightliftingLevelDescription(double level) {
    if (level <= 0.2) return 'I am a beginner and have not regularly been to the gym';
    if (level <= 0.4) return 'I have some experience with basic exercises';
    if (level <= 0.6) return 'I regularly lift weights and know proper form';
    if (level <= 0.8) return 'I have advanced lifting experience';
    return 'I\'m training for competition or have mastered advanced lifting';
  }

  String _getEnvironmentLabel(WorkoutEnvironment environment) {
    switch (environment) {
      case WorkoutEnvironment.largeGym:
        return 'Large gym';
      case WorkoutEnvironment.smallGym:
        return 'Small gym';
      case WorkoutEnvironment.homeBasic:
        return 'Home (basic equipment)';
      case WorkoutEnvironment.homeNoEquipment:
        return 'Home (no equipment)';
    }
  }

  IconData _getEnvironmentIcon(WorkoutEnvironment environment) {
    switch (environment) {
      case WorkoutEnvironment.largeGym:
        return Icons.fitness_center;
      case WorkoutEnvironment.smallGym:
        return Icons.sports_gymnastics;
      case WorkoutEnvironment.homeBasic:
        return Icons.home_filled;
      case WorkoutEnvironment.homeNoEquipment:
        return Icons.home_outlined;
    }
  }
} 