import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/auth/providers/auth_providers.dart';
import '../features/profile/providers/user_providers.dart';
import '../providers/settings_providers.dart';
import 'auth_page.dart';
import 'comprehensive_onboarding_screen.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(userProfileProvider);
    final settings = ref.watch(appSettingsProvider);
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: userAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('Error: $error')),
        data: (user) {
          if (user == null) return const Center(child: Text('No user data'));
          
          return ListView(
            physics: const BouncingScrollPhysics(),
            children: [
              // Profile Section
              _buildSection(
                title: 'Profile',
                children: [
                  _buildProfileTile(user),
                  _buildSettingTile(
                    icon: Icons.edit,
                    title: 'Edit Profile',
                    subtitle: 'Update your personal information',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ComprehensiveOnboardingScreen(
                            isFromProfile: true,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
              
              // Preferences Section
              _buildSection(
                title: 'Preferences',
                children: [
                  _buildSwitchTile(
                    icon: Icons.fitness_center,
                    title: 'Imperial Units',
                    subtitle: 'Use lbs and inches',
                    value: user.personalInfo.preferredUnits == 'imperial',
                    onChanged: (value) async {
                      await ref.read(userProfileNotifierProvider.notifier)
                          .updatePreferredUnits(value ? 'imperial' : 'metric');
                    },
                  ),
                  _buildThemeTile(context),
                  _buildNotificationTile(
                    enabled: user.preferences.notifications,
                    onChanged: (value) async {
                      await ref.read(userProfileNotifierProvider.notifier)
                          .updateNotificationPreferences(value);
                    },
                  ),
                  _buildLanguageTile(
                    currentLanguage: user.preferences.language,
                  ),
                ],
              ),
              
              // Workout Settings Section
              _buildSection(
                title: 'Workout Settings',
                children: [
                  _buildSliderTile(
                    icon: Icons.timer,
                    title: 'Default Workout Duration',
                    subtitle: '${user.preferences.workoutPreferences.durationMinutes} minutes',
                    value: user.preferences.workoutPreferences.durationMinutes.toDouble(),
                    min: 15,
                    max: 120,
                    divisions: 21,
                    onChanged: (value) async {
                      await ref.read(userProfileNotifierProvider.notifier)
                          .updateWorkoutDuration(value.toInt());
                    },
                  ),
                  _buildSliderTile(
                    icon: Icons.calendar_today,
                    title: 'Weekly Workout Goal',
                    subtitle: '${user.preferences.workoutPreferences.workoutsPerWeek} workouts per week',
                    value: user.preferences.workoutPreferences.workoutsPerWeek.toDouble(),
                    min: 1,
                    max: 7,
                    divisions: 6,
                    onChanged: (value) async {
                      await ref.read(userProfileNotifierProvider.notifier)
                          .updateWorkoutsPerWeek(value.toInt());
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.home,
                    title: 'Workout Environment',
                    subtitle: _getEnvironmentText(user.preferences.workoutPreferences.environments),
                    onTap: () {
                      _showEnvironmentPicker(context, user);
                    },
                  ),
                ],
              ),
              
              // Privacy & Security Section
              _buildSection(
                title: 'Privacy & Security',
                children: [
                  _buildSettingTile(
                    icon: Icons.lock,
                    title: 'Change Password',
                    subtitle: 'Update your account password',
                    onTap: () {
                      // TODO: Implement password change
                    },
                  ),
                  _buildSwitchTile(
                    icon: Icons.analytics,
                    title: 'Share Analytics',
                    subtitle: 'Help improve the app with usage data',
                    value: settings.shareAnalytics,
                    onChanged: (value) {
                      ref.read(appSettingsProvider.notifier)
                          .updateShareAnalytics(value);
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.download,
                    title: 'Export Data',
                    subtitle: 'Download all your workout data',
                    onTap: () {
                      // TODO: Implement data export
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.delete_forever,
                    title: 'Delete Account',
                    subtitle: 'Permanently delete your account',
                    onTap: () => _showDeleteAccountDialog(context),
                    textColor: Theme.of(context).colorScheme.error,
                  ),
                ],
              ),
              
              // Support Section
              _buildSection(
                title: 'Support',
                children: [
                  _buildSettingTile(
                    icon: Icons.help,
                    title: 'Help Center',
                    subtitle: 'Get help and support',
                    onTap: () {
                      // TODO: Navigate to help center
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.feedback,
                    title: 'Send Feedback',
                    subtitle: 'Share your thoughts with us',
                    onTap: () {
                      // TODO: Open feedback form
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.star,
                    title: 'Rate App',
                    subtitle: 'Rate us on the App Store',
                    onTap: () {
                      // TODO: Open app store
                    },
                  ),
                ],
              ),
              
              // About Section
              _buildSection(
                title: 'About',
                children: [
                  _buildSettingTile(
                    icon: Icons.info,
                    title: 'App Version',
                    subtitle: 'v1.0.0',
                    showArrow: false,
                  ),
                  _buildSettingTile(
                    icon: Icons.article,
                    title: 'Terms of Service',
                    subtitle: 'View our terms',
                    onTap: () {
                      // TODO: Open terms
                    },
                  ),
                  _buildSettingTile(
                    icon: Icons.privacy_tip,
                    title: 'Privacy Policy',
                    subtitle: 'View our privacy policy',
                    onTap: () {
                      // TODO: Open privacy policy
                    },
                  ),
                ],
              ),
              
              // Sign Out Button
              Padding(
                padding: const EdgeInsets.all(20),
                child: ElevatedButton.icon(
                  onPressed: () => _showSignOutDialog(context),
                  icon: const Icon(Icons.logout),
                  label: const Text('Sign Out'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    foregroundColor: Theme.of(context).colorScheme.onError,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 24, 20, 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
              letterSpacing: 0.5,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileTile(dynamic user) {
    return ListTile(
      contentPadding: const EdgeInsets.all(16),
      leading: CircleAvatar(
        radius: 30,
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: Text(
          (user.personalInfo.name?.isNotEmpty == true 
              ? user.personalInfo.name![0] 
              : user.email[0]).toUpperCase(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ),
      title: Text(
        user.personalInfo.name ?? 'User',
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        user.email,
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
    );
  }

  Widget _buildSettingTile({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Color? textColor,
    bool showArrow = true,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (textColor ?? Theme.of(context).colorScheme.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: textColor ?? Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: showArrow
          ? Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            )
          : null,
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: CupertinoSwitch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildSliderTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 22,
              ),
            ),
            title: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: value.round().toString(),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildThemeTile(BuildContext context) {
    final currentTheme = ref.watch(appSettingsProvider).themeMode;
    
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.palette,
          color: Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: const Text(
        'Theme',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        _getThemeText(currentTheme),
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
      ),
      onTap: () => _showThemePicker(context),
    );
  }

  Widget _buildNotificationTile({
    required bool enabled,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.notifications,
          color: Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: const Text(
        'Notifications',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        enabled ? 'Enabled' : 'Disabled',
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: CupertinoSwitch(
        value: enabled,
        onChanged: onChanged,
        activeColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildLanguageTile({required String currentLanguage}) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.language,
          color: Theme.of(context).colorScheme.primary,
          size: 22,
        ),
      ),
      title: const Text(
        'Language',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        _getLanguageText(currentLanguage),
        style: TextStyle(
          fontSize: 12,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
      ),
      onTap: () => _showLanguagePicker(context),
    );
  }

  void _showThemePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Theme',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.light_mode),
                title: const Text('Light'),
                onTap: () {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(ThemeMode.light);
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.dark_mode),
                title: const Text('Dark'),
                onTap: () {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.phone_android),
                title: const Text('System'),
                onTap: () {
                  ref.read(appSettingsProvider.notifier).updateThemeMode(ThemeMode.system);
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLanguagePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Language',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Text('🇺🇸', style: TextStyle(fontSize: 24)),
                title: const Text('English'),
                onTap: () {
                  ref.read(userProfileNotifierProvider.notifier).updateLanguage('en');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Text('🇪🇸', style: TextStyle(fontSize: 24)),
                title: const Text('Spanish'),
                onTap: () {
                  ref.read(userProfileNotifierProvider.notifier).updateLanguage('es');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Text('🇫🇷', style: TextStyle(fontSize: 24)),
                title: const Text('French'),
                onTap: () {
                  ref.read(userProfileNotifierProvider.notifier).updateLanguage('fr');
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showEnvironmentPicker(BuildContext context, dynamic user) {
    final environments = ['gym', 'homeWithEquipment', 'homeNoEquipment', 'outdoor'];
    final currentEnvironments = user.preferences.workoutPreferences.environments;
    
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Workout Environment',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...environments.map((env) {
                    final isSelected = currentEnvironments.contains(env);
                    return CheckboxListTile(
                      title: Text(_getEnvironmentName(env)),
                      value: isSelected,
                      onChanged: (value) async {
                        List<String> newEnvironments = List.from(currentEnvironments);
                        if (value == true) {
                          newEnvironments.add(env);
                        } else {
                          newEnvironments.remove(env);
                        }
                        await ref.read(userProfileNotifierProvider.notifier)
                            .updateWorkoutEnvironments(newEnvironments);
                        Navigator.pop(context);
                      },
                    );
                  }).toList(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'Sign Out',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final authService = ref.read(authServiceProvider);
              await authService.signOut();
              if (mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const AuthPage()),
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'Delete Account',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        content: const Text(
          'This action cannot be undone. All your data will be permanently deleted.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement account deletion
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  String _getThemeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  String _getLanguageText(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'es':
        return 'Spanish';
      case 'fr':
        return 'French';
      default:
        return 'English';
    }
  }

  String _getEnvironmentText(List<String> environments) {
    if (environments.isEmpty) return 'None selected';
    if (environments.length == 1) return _getEnvironmentName(environments.first);
    return '${environments.length} selected';
  }

  String _getEnvironmentName(String env) {
    switch (env) {
      case 'gym':
        return 'Gym';
      case 'homeWithEquipment':
        return 'Home (with equipment)';
      case 'homeNoEquipment':
        return 'Home (no equipment)';
      case 'outdoor':
        return 'Outdoor';
      default:
        return env;
    }
  }
}