import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:ui';
import 'package:video_player/video_player.dart';
import 'package:vimeo_video_player/vimeo_video_player.dart';
import '../models/workout_model.dart';
import '../models/exercise_model.dart';
import '../services/firestore_service.dart';
import '../services/auth_service.dart';

class WorkoutSessionPage extends StatefulWidget {
  final WorkoutPlanModel workoutPlan;
  final List<ExerciseModel> exercises;
  final List<WorkoutExercise> workoutExercises;

  const WorkoutSessionPage({
    super.key,
    required this.workoutPlan,
    required this.exercises,
    required this.workoutExercises,
  });

  @override
  State<WorkoutSessionPage> createState() => _WorkoutSessionPageState();
}

class _WorkoutSessionPageState extends State<WorkoutSessionPage>
    with TickerProviderStateMixin {
  final FirestoreService _firestoreService = FirestoreService();
  final AuthService _authService = AuthService();
  
  // Video controller
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  String? _currentVimeoId;
  
  // Session state
  String? _sessionId;
  int _currentExerciseIndex = 0;
  int _currentSet = 1;
  bool _isResting = false;
  bool _isCountingDown = false;
  bool _isExercising = false;
  bool _isPaused = false;
  
  // Weight tracking
  Map<String, double> _exerciseWeights = {};
  Map<String, int> _exerciseReps = {};
  
  // Timers
  Timer? _exerciseTimer;
  Timer? _restTimer;
  Timer? _countdownTimer;
  int _exerciseTimeRemaining = 0;
  int _restTimeRemaining = 0;
  int _countdownRemaining = 5;
  
  // Workout tracking
  Duration _totalWorkoutDuration = Duration.zero;
  Timer? _workoutTimer;
  List<CompletedExercise> _completedExercises = [];
  
  // Animation controllers
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Lock to portrait mode
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    _progressController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _startWorkoutSession();
    _initializeVideo();
  }
  
  Future<void> _initializeVideo() async {
    if (_currentExerciseIndex >= widget.exercises.length) return;
    
    final exercise = widget.exercises[_currentExerciseIndex];
    if (exercise.videoUrl != null && exercise.videoUrl!.isNotEmpty) {
      _videoController?.dispose();
      
      // Handle different video URL formats
      String videoUrl = exercise.videoUrl!;
      
      // Extract Vimeo ID once and store it
      if (videoUrl.contains('vimeo.com')) {
        _currentVimeoId = _extractVimeoId(videoUrl);
      } else {
        _currentVimeoId = null;
      }
      
      // For non-Vimeo URLs, use the regular video player
      if (!videoUrl.contains('vimeo.com')) {
        // Only try to play direct video URLs (mp4, m3u8, etc.)
        if (videoUrl.endsWith('.mp4') || 
            videoUrl.endsWith('.m3u8') || 
            videoUrl.contains('youtube.com') ||
            videoUrl.contains('youtu.be')) {
          
          _videoController = VideoPlayerController.network(videoUrl)
            ..initialize().then((_) {
              if (mounted) {
                setState(() {
                  _isVideoInitialized = true;
                });
                _videoController!.setLooping(true);
                _videoController!.play();
              }
            }).catchError((error) {
              print('❌ Error initializing video: $error');
              print('📹 Video URL: $videoUrl');
              print('🔍 Error type: ${error.runtimeType}');
              // Fallback to image if video fails
              setState(() {
                _isVideoInitialized = false;
              });
            });
        } else {
          print('Unsupported video URL format: $videoUrl');
          setState(() {
            _isVideoInitialized = false;
          });
        }
      }
    }
  }
  
  @override
  void dispose() {
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _countdownTimer?.cancel();
    _workoutTimer?.cancel();
    _progressController.dispose();
    _pulseController.dispose();
    _videoController?.dispose();
    
    // Restore orientation settings
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }
  
  Future<void> _startWorkoutSession() async {
    if (_authService.currentUser == null) return;
    
    final sessionId = await _firestoreService.startWorkoutSession(
      _authService.currentUser!.uid,
      widget.workoutPlan.id,
    );
    
    if (sessionId != null) {
      setState(() {
        _sessionId = sessionId;
      });
      
      _startWorkoutTimer();
      _prepareForExercise();
    }
  }
  
  void _startWorkoutTimer() {
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused && mounted) {
        setState(() {
          _totalWorkoutDuration = Duration(seconds: _totalWorkoutDuration.inSeconds + 1);
        });
      }
    });
  }
  
  void _prepareForExercise() {
    if (_currentExerciseIndex >= widget.exercises.length) {
      _completeWorkout();
      return;
    }
    
    final exercise = widget.exercises[_currentExerciseIndex];
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    
    if (exercise.isTimerBased || workoutExercise.duration > 0) {
      // Start countdown for timer-based exercises
      _startCountdown();
    } else {
      // Go directly to exercise for rep-based
      setState(() {
        _isExercising = true;
      });
    }
  }
  
  void _startCountdown() {
    setState(() {
      _isCountingDown = true;
      _countdownRemaining = 5;
    });
    
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdownRemaining > 1) {
        setState(() {
          _countdownRemaining--;
        });
      } else {
        timer.cancel();
        _startExercise();
      }
    });
  }
  
  void _startExercise() {
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    
    setState(() {
      _isCountingDown = false;
      _isExercising = true;
      _exerciseTimeRemaining = workoutExercise.duration;
    });
    
    if (workoutExercise.duration > 0) {
      _exerciseTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!_isPaused && _exerciseTimeRemaining > 0) {
          setState(() {
            _exerciseTimeRemaining--;
          });
          
          if (_exerciseTimeRemaining == 0) {
            timer.cancel();
            _completeSet();
          }
        }
      });
    }
  }
  
  void _completeSet() {
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    
    if (_currentSet < workoutExercise.sets) {
      // Start rest period
      _startRest();
    } else {
      // Move to next exercise
      _completeExercise();
    }
  }
  
  void _startRest() {
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    
    setState(() {
      _isExercising = false;
      _isResting = true;
      _restTimeRemaining = workoutExercise.restTime;
      _currentSet++;
    });
    
    _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused && _restTimeRemaining > 0) {
        setState(() {
          _restTimeRemaining--;
        });
        
        if (_restTimeRemaining == 0) {
          timer.cancel();
          setState(() {
            _isResting = false;
          });
          _prepareForExercise();
        }
      }
    });
  }
  
  void _completeExercise() {
    final exercise = widget.exercises[_currentExerciseIndex];
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    
    // Record completed exercise
    final completedExercise = CompletedExercise(
      exerciseId: exercise.id,
      completedSets: workoutExercise.sets,
      sets: List.generate(
        workoutExercise.sets,
        (index) => ExerciseSet(
          reps: _exerciseReps[exercise.id] ?? workoutExercise.reps,
          weight: _exerciseWeights[exercise.id] ?? 0.0,
          duration: workoutExercise.duration,
        ),
      ),
      completedAt: DateTime.now(),
    );
    
    setState(() {
      _completedExercises.add(completedExercise);
      _currentExerciseIndex++;
      _currentSet = 1;
      _isExercising = false;
    });
    
    // Initialize video for next exercise
    _initializeVideo();
    print('🎬 Moving to exercise ${_currentExerciseIndex + 1}: ${widget.exercises[_currentExerciseIndex].name}');
    
    // Check if there's a rest period between exercises
    if (_currentExerciseIndex < widget.exercises.length) {
      setState(() {
        _isResting = true;
        _restTimeRemaining = 30; // 30 seconds between exercises
      });
      
      _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!_isPaused && _restTimeRemaining > 0) {
          setState(() {
            _restTimeRemaining--;
          });
          
          if (_restTimeRemaining == 0) {
            timer.cancel();
            setState(() {
              _isResting = false;
            });
            _prepareForExercise();
          }
        }
      });
    } else {
      _completeWorkout();
    }
  }
  
  Future<void> _completeWorkout() async {
    _workoutTimer?.cancel();

    int totalCaloriesBurned = 0;
    if (_authService.currentUser != null) {
      List<String> exerciseIds = _completedExercises.map((ce) => ce.exerciseId).toSet().toList();
      List<ExerciseModel?> exerciseModels = await Future.wait(
        exerciseIds.map((id) => _firestoreService.getExerciseById(id))
      );
      
      Map<String, ExerciseModel> exerciseDataMap = {};
      for (var model in exerciseModels) {
        if (model != null) {
          exerciseDataMap[model.id] = model;
        }
      }

      for (var completedEx in _completedExercises) {
        final exerciseDetail = exerciseDataMap[completedEx.exerciseId];
        if (exerciseDetail != null) {
          for (var set in completedEx.sets) {
            double exerciseMinutes;
            if (set.duration > 0) { 
              exerciseMinutes = set.duration / 60.0;
            } else { 
              exerciseMinutes = (set.reps * 2) / 60.0; 
            }
            totalCaloriesBurned += (exerciseDetail.caloriesPerMinute * exerciseMinutes).round();
          }
        }
      }
    }
    
    if (_sessionId != null) {
      await _firestoreService.completeWorkoutSession(
        _sessionId!,
        _completedExercises,
        'Great workout!',
        totalCaloriesBurned, 
      );
      
      await _authService.updateWorkoutStats(_totalWorkoutDuration.inMinutes, totalCaloriesBurned);
    }
    
    if (mounted) {
      _showCompletionScreen(totalCaloriesBurned);
    }
  }
  
  void _showCompletionScreen(int totalCaloriesBurned) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => WorkoutCompletionPage(
          workoutName: widget.workoutPlan.name,
          duration: _totalWorkoutDuration,
          exercisesCompleted: _completedExercises.length,
          totalExercises: widget.exercises.length,
          caloriesBurned: totalCaloriesBurned, 
        ),
      ),
    );
  }
  
  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });
  }
  
  void _skipExercise() {
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _countdownTimer?.cancel();
    
    setState(() {
      _currentExerciseIndex++;
      _currentSet = 1;
      _isExercising = false;
      _isResting = false;
      _isCountingDown = false;
    });
    
    _initializeVideo(); // Initialize video for next exercise
    print('⏭️ Skipped to exercise ${_currentExerciseIndex + 1}');
    _prepareForExercise();
  }
  
  void _previousExercise() {
    if (_currentExerciseIndex <= 0) return;
    
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _countdownTimer?.cancel();
    
    setState(() {
      _currentExerciseIndex--;
      _currentSet = 1;
      _isExercising = false;
      _isResting = false;
      _isCountingDown = false;
    });
    
    _initializeVideo(); // Initialize video for previous exercise
    print('⏮️ Went back to exercise ${_currentExerciseIndex + 1}');
    _prepareForExercise();
  }
  
  @override
  Widget build(BuildContext context) {
    if (_currentExerciseIndex >= widget.exercises.length) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }
    
    final exercise = widget.exercises[_currentExerciseIndex];
    final workoutExercise = widget.workoutExercises[_currentExerciseIndex];
    final isTimerBased = exercise.isTimerBased || workoutExercise.duration > 0;
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Background video or image
          if (exercise.videoUrl != null && 
              exercise.videoUrl!.isNotEmpty && 
              exercise.videoUrl!.contains('vimeo.com'))
            // Use VimeoVideoPlayer for Vimeo videos
            Positioned.fill(
              child: VimeoVideoPlayer(
                key: ValueKey('vimeo_${exercise.id}_${_currentExerciseIndex}'),
                videoId: _currentVimeoId ?? '',
              ),
            )
          else if (_videoController != null && _isVideoInitialized)
            // Use regular video player for direct video URLs
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _videoController!.value.size.width,
                  height: _videoController!.value.size.height,
                  child: VideoPlayer(_videoController!),
                ),
              ),
            )
          else if (exercise.imageUrl.isNotEmpty)
            // Fallback to image
            Positioned.fill(
              child: Image.network(
                exercise.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(color: Colors.grey[900]);
                },
              ),
            )
          else
            Container(color: Colors.grey[900]),
          
          // Video loading indicator (only for non-Vimeo videos)
          if (_videoController != null && 
              !_isVideoInitialized && 
              (exercise.videoUrl == null || !exercise.videoUrl!.contains('vimeo.com')))
            Positioned.fill(
              child: Container(
                color: Colors.black54,
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          
          // Dark overlay for better text visibility
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.8),
                ],
              ),
            ),
          ),
          
          // Main content
          SafeArea(
            child: Column(
              children: [
                // Top section with set indicator
                _buildTopSection(exercise, workoutExercise),
                
                // Middle section - exercise display
                Expanded(
                  child: Center(
                    child: _isCountingDown
                        ? _buildCountdown()
                        : _isResting
                            ? _buildRestScreen()
                            : _buildExerciseDisplay(exercise, workoutExercise, isTimerBased),
                  ),
                ),
                
                // Bottom section with controls
                _buildBottomSection(exercise, workoutExercise, isTimerBased),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTopSection(ExerciseModel exercise, WorkoutExercise workoutExercise) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Progress bar
          Row(
            children: [
              // Timer
              Text(
                '${_totalWorkoutDuration.inMinutes}:${(_totalWorkoutDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 16),
              // Progress bar
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LinearProgressIndicator(
                      value: (_currentExerciseIndex + (_currentSet / workoutExercise.sets)) / widget.exercises.length,
                      backgroundColor: Colors.white.withOpacity(0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                      minHeight: 6,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${((_currentExerciseIndex / widget.exercises.length) * 100).toInt()}%',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Exercise name
          Text(
            exercise.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          // Set indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
              ),
            ),
            child: Text(
              'Set $_currentSet / ${workoutExercise.sets}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildExerciseDisplay(ExerciseModel exercise, WorkoutExercise workoutExercise, bool isTimerBased) {
    if (isTimerBased && _isExercising) {
      return _buildTimerDisplay();
    }
    // For rep-based exercises, we don't show anything in the middle
    // The reps are now displayed at the bottom
    return const SizedBox();
  }
  
  Widget _buildTimerDisplay() {
    final totalDuration = widget.workoutExercises[_currentExerciseIndex].duration;
    final progress = (_exerciseTimeRemaining / totalDuration).clamp(0.0, 1.0);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            // Circular progress
            SizedBox(
              width: 250,
              height: 250,
              child: CircularProgressIndicator(
                value: 1 - progress,
                strokeWidth: 12,
                backgroundColor: Colors.white.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            // Timer text
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${(_exerciseTimeRemaining ~/ 60).toString().padLeft(1, '0')}:${(_exerciseTimeRemaining % 60).toString().padLeft(2, '0')}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 64,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildCountdown() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Get Ready!',
          style: TextStyle(
            color: Colors.white,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 40),
        ScaleTransition(
          scale: _pulseAnimation,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
            ),
            child: Center(
              child: Text(
                '$_countdownRemaining',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 64,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildRestScreen() {
    final nextExercise = _currentExerciseIndex < widget.exercises.length 
        ? widget.exercises[_currentExerciseIndex] 
        : null;
    final nextWorkoutExercise = _currentExerciseIndex < widget.workoutExercises.length
        ? widget.workoutExercises[_currentExerciseIndex]
        : null;
        
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Rest timer
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 200,
              height: 200,
              child: CircularProgressIndicator(
                value: 1 - (_restTimeRemaining / 30),
                strokeWidth: 8,
                backgroundColor: Colors.blue.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.blue.withOpacity(0.8),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.timer,
                  color: Colors.blue,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  '${_restTimeRemaining}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 64,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Text(
                  'seconds',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 48),
        
        // Next exercise preview
        if (nextExercise != null && nextWorkoutExercise != null) ...[
          Container(
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: Column(
              children: [
                const Text(
                  'Next Exercise',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  nextExercise.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${nextWorkoutExercise.sets} sets',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${nextWorkoutExercise.reps} reps',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          TextButton(
            onPressed: () {
              _restTimer?.cancel();
              setState(() {
                _isResting = false;
              });
              _prepareForExercise();
            },
            child: const Text(
              'Skip Rest',
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ],
    );
  }
  
  Widget _buildBottomSection(ExerciseModel exercise, WorkoutExercise workoutExercise, bool isTimerBased) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // For rep-based exercises, show the reps and weight at the bottom
          if (!isTimerBased && _isExercising) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Reps display with adjustment
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              final currentReps = _exerciseReps[exercise.id] ?? workoutExercise.reps;
                              if (currentReps > 1) {
                                _exerciseReps[exercise.id] = currentReps - 1;
                              }
                            });
                          },
                          icon: const Icon(Icons.remove_circle_outline),
                          color: Colors.white54,
                          iconSize: 28,
                        ),
                        Column(
                          children: [
                            Text(
                              '${_exerciseReps[exercise.id] ?? workoutExercise.reps}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 72,
                                fontWeight: FontWeight.bold,
                                height: 1,
                              ),
                            ),
                            const Text(
                              'reps',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              final currentReps = _exerciseReps[exercise.id] ?? workoutExercise.reps;
                              _exerciseReps[exercise.id] = currentReps + 1;
                            });
                          },
                          icon: const Icon(Icons.add_circle_outline),
                          color: Colors.white54,
                          iconSize: 28,
                        ),
                      ],
                    ),
                  ],
                ),
                
                // Weight display (if not bodyweight)
                if (!exercise.isBodyweight)
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            onPressed: () {
                              setState(() {
                                final currentWeight = _exerciseWeights[exercise.id] ?? 25.0;
                                if (currentWeight >= 5) {
                                  _exerciseWeights[exercise.id] = currentWeight - 5;
                                }
                              });
                            },
                            icon: const Icon(Icons.remove_circle_outline),
                            color: Colors.white54,
                            iconSize: 28,
                          ),
                          Column(
                            children: [
                              Text(
                                '${(_exerciseWeights[exercise.id] ?? 25.0).toInt()}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 72,
                                  fontWeight: FontWeight.bold,
                                  height: 1,
                                ),
                              ),
                              const Text(
                                'lbs',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 18,
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                final currentWeight = _exerciseWeights[exercise.id] ?? 25.0;
                                _exerciseWeights[exercise.id] = currentWeight + 5;
                              });
                            },
                            icon: const Icon(Icons.add_circle_outline),
                            color: Colors.white54,
                            iconSize: 28,
                          ),
                        ],
                      ),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 40),
          ],
          
          // Action buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous exercise button
              IconButton(
                onPressed: _previousExercise,
                icon: const Icon(Icons.skip_previous),
                color: Colors.white54,
                iconSize: 32,
              ),
              
              // Main action button
              if (!isTimerBased && _isExercising)
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: ElevatedButton(
                      onPressed: _completeSet,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        'Done',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              
              // Next exercise button
              IconButton(
                onPressed: _skipExercise,
                icon: const Icon(Icons.skip_next),
                color: Colors.white54,
                iconSize: 32,
              ),
            ],
          ),
          
          // Next exercise preview
          if (_currentExerciseIndex < widget.exercises.length - 1) ...[
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.arrow_forward,
                  color: Colors.white.withOpacity(0.5),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Next → ${widget.exercises[_currentExerciseIndex + 1].name}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  
  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.skip_next, color: Colors.white),
              title: const Text('Skip Exercise', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _skipExercise();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline, color: Colors.white),
              title: const Text('Exercise Info', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showExerciseInfo();
              },
            ),
            ListTile(
              leading: const Icon(Icons.close, color: Colors.red),
              title: const Text('End Workout', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showEndWorkoutDialog();
              },
            ),
          ],
        ),
      ),
    );
  }
  
  void _showExerciseInfo() {
    final exercise = widget.exercises[_currentExerciseIndex];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          exercise.name,
          style: const TextStyle(color: Colors.white),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (exercise.description.isNotEmpty) ...[
                Text(
                  exercise.description,
                  style: const TextStyle(color: Colors.white70),
                ),
                const SizedBox(height: 16),
              ],
              if (exercise.instructions.isNotEmpty) ...[
                const Text(
                  'Instructions:',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...exercise.instructions.map((instruction) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• $instruction',
                    style: const TextStyle(color: Colors.white70),
                  ),
                )),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
  
  void _showEndWorkoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'End Workout?',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Are you sure you want to end this workout?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _completeWorkout();
            },
            child: const Text('End Workout', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String? _extractVimeoId(String videoUrl) {
    // Extract video ID from various Vimeo URL formats
    // Example: https://vimeo.com/121852966 -> 121852966
    final patterns = [
      RegExp(r'vimeo\.com/(\d+)'),
      RegExp(r'player\.vimeo\.com/video/(\d+)'),
      RegExp(r'vimeo\.com/channels/[^/]+/(\d+)'),
      RegExp(r'vimeo\.com/groups/[^/]+/videos/(\d+)'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(videoUrl);
      if (match != null && match.groupCount >= 1) {
        final videoId = match.group(1);
        print('Extracted Vimeo ID: $videoId from URL: $videoUrl');
        return videoId;
      }
    }
    
    print('Could not extract Vimeo ID from URL: $videoUrl');
    return null;
  }
}

// Workout completion page
class WorkoutCompletionPage extends StatelessWidget {
  final String workoutName;
  final Duration duration;
  final int exercisesCompleted;
  final int totalExercises;
  final int caloriesBurned;

  const WorkoutCompletionPage({
    super.key,
    required this.workoutName,
    required this.duration,
    required this.exercisesCompleted,
    required this.totalExercises,
    required this.caloriesBurned,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.celebration,
                color: Colors.amber,
                size: 80,
              ),
              const SizedBox(height: 32),
              const Text(
                'Workout Complete!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                workoutName,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              
              // Stats
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStat(
                    '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}',
                    'Duration',
                    Icons.timer,
                  ),
                  _buildStat(
                    '$exercisesCompleted/$totalExercises',
                    'Exercises',
                    Icons.fitness_center,
                  ),
                ],
              ),
              
              const SizedBox(height: 64),
              
              // Calories burned
              _buildStat(
                '$caloriesBurned calories',
                'Calories Burned',
                Icons.local_fire_department,
              ),
              
              const SizedBox(height: 64),
              
              // Done button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: const Text(
                    'Done',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildStat(String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white54, size: 32),
        const SizedBox(height: 12),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 16,
          ),
        ),
      ],
    );
  }
} 