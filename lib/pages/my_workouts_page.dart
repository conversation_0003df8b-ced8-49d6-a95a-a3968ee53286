import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/workout_model.dart';
import '../features/workout/providers/workout_providers.dart';
import '../widgets/custom_workout_card.dart';
import '../shared/widgets/state_widgets.dart';
import 'create_workout_page.dart';
import 'workout_detail_page.dart';

// Provider for view mode (grid vs list)
final isGridViewProvider = StateProvider<bool>((ref) => true);

// Provider for search query
final workoutSearchQueryProvider = StateProvider<String>((ref) => '');

// Provider for filtered workouts based on search
final filteredCustomWorkoutsProvider = Provider<List<WorkoutPlanModel>>((ref) {
  final workouts = ref.watch(customWorkoutsProvider).asData?.value ?? [];
  final searchQuery = ref.watch(workoutSearchQueryProvider);
  
  if (searchQuery.isEmpty) {
    return workouts;
  }
  
  return workouts.where((workout) =>
    workout.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
    workout.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
    workout.category.toLowerCase().contains(searchQuery.toLowerCase())
  ).toList();
});

class MyWorkoutsPage extends ConsumerStatefulWidget {
  const MyWorkoutsPage({super.key});

  @override
  ConsumerState<MyWorkoutsPage> createState() => _MyWorkoutsPageState();
}

class _MyWorkoutsPageState extends ConsumerState<MyWorkoutsPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));
    
    _searchController.addListener(() {
      ref.read(workoutSearchQueryProvider.notifier).state = _searchController.text;
    });
    
    // Start animation when data is loaded
    Future.microtask(() {
      final workoutsAsync = ref.read(customWorkoutsProvider);
      if (workoutsAsync.hasValue) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _deleteWorkout(WorkoutPlanModel workout) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('Delete Workout'),
        content: Text('Are you sure you want to delete "${workout.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final customWorkoutService = ref.read(customWorkoutServiceProvider);
      await customWorkoutService.deleteCustomWorkout(workout.id);
      ref.invalidate(customWorkoutsProvider);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${workout.name} deleted'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Undo',
              onPressed: () {
                // TODO: Implement undo functionality
              },
            ),
          ),
        );
      }
    }
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search workouts...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref.read(workoutSearchQueryProvider.notifier).state = '';
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }

  Widget _buildWorkoutGrid(List<WorkoutPlanModel> workouts) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return CustomWorkoutCard(
          workout: workout,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WorkoutDetailPage(workoutPlan: workout),
              ),
            );
          },
          onDelete: () => _deleteWorkout(workout),
        );
      },
    );
  }

  Widget _buildWorkoutList(List<WorkoutPlanModel> workouts) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: CustomWorkoutCard(
            workout: workout,
            isListView: true,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WorkoutDetailPage(workoutPlan: workout),
                ),
              );
            },
            onDelete: () => _deleteWorkout(workout),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final workoutsAsync = ref.watch(customWorkoutsProvider);
    final filteredWorkouts = ref.watch(filteredCustomWorkoutsProvider);
    final isGridView = ref.watch(isGridViewProvider);
    
    // Trigger animation when data loads
    ref.listen(customWorkoutsProvider, (previous, next) {
      if (next.hasValue && !previous!.hasValue) {
        _animationController.forward();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Workouts'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              ref.read(isGridViewProvider.notifier).state = !isGridView;
            },
            tooltip: isGridView ? 'List View' : 'Grid View',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(customWorkoutsProvider);
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: AsyncValueWidget(
              value: workoutsAsync,
              onRetry: () {
                ref.invalidate(customWorkoutsProvider);
              },
              data: (workouts) {
                if (workouts.isEmpty) {
                  return EmptyState(
                    icon: Icons.fitness_center,
                    title: 'No Custom Workouts',
                    message: 'Create your first custom workout to get started',
                    onAction: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateWorkoutPage(),
                        ),
                      ).then((_) {
                        ref.invalidate(customWorkoutsProvider);
                      });
                    },
                    actionLabel: 'Create Workout',
                  );
                }
                
                if (filteredWorkouts.isEmpty) {
                  return const EmptyState(
                    icon: Icons.search_off,
                    title: 'No Results',
                    message: 'No workouts match your search',
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(customWorkoutsProvider);
                  },
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: isGridView
                          ? _buildWorkoutGrid(filteredWorkouts)
                          : _buildWorkoutList(filteredWorkouts),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'my_workouts_page_create_fab',
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateWorkoutPage(),
            ),
          ).then((_) {
            ref.invalidate(customWorkoutsProvider);
          });
        },
        icon: const Icon(Icons.add),
        label: const Text('Create Workout'),
      ),
    );
  }
}