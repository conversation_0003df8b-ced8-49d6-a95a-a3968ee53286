import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/workout_model.dart';
import '../features/workout/providers/workout_providers.dart';
import '../widgets/custom_workout_card.dart';
import '../shared/widgets/state_widgets.dart';
import 'workout_detail_page.dart';
import 'my_workouts_page.dart';
import 'create_workout_page.dart';

// Provider for view mode (grid vs list)
final workoutsViewModeProvider = StateProvider<bool>((ref) => true);

// Provider for search query
final workoutsSearchQueryProvider = StateProvider<String>((ref) => '');

// Provider for filtered workouts based on search
final filteredWorkoutPlansProvider = Provider<List<WorkoutPlanModel>>((ref) {
  final workouts = ref.watch(workoutPlansProvider).asData?.value ?? [];
  final searchQuery = ref.watch(workoutsSearchQueryProvider);

  if (searchQuery.isEmpty) {
    return workouts;
  }

  return workouts.where((workout) =>
    workout.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
    workout.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
    workout.category.toLowerCase().contains(searchQuery.toLowerCase())
  ).toList();
});

class WorkoutsPage extends ConsumerStatefulWidget {
  const WorkoutsPage({super.key});

  @override
  ConsumerState<WorkoutsPage> createState() => _WorkoutsPageState();
}

class _WorkoutsPageState extends ConsumerState<WorkoutsPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _searchController.addListener(() {
      ref.read(workoutsSearchQueryProvider.notifier).state = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search workouts...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref.read(workoutsSearchQueryProvider.notifier).state = '';
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }

  Widget _buildWorkoutGrid(List<WorkoutPlanModel> workouts) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return CustomWorkoutCard(
          workout: workout,
          showActions: false, // Don't show edit/delete for pre-built workouts
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WorkoutDetailPage(workoutPlan: workout),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildWorkoutList(List<WorkoutPlanModel> workouts) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: CustomWorkoutCard(
            workout: workout,
            isListView: true,
            showActions: false, // Don't show edit/delete for pre-built workouts
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WorkoutDetailPage(workoutPlan: workout),
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final workoutsAsync = ref.watch(workoutPlansProvider);
    final filteredWorkouts = ref.watch(filteredWorkoutPlansProvider);
    final isGridView = ref.watch(workoutsViewModeProvider);

    // Trigger animation when data loads
    ref.listen(workoutPlansProvider, (previous, next) {
      if (next.hasValue && !previous!.hasValue) {
        _animationController.forward();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Workouts'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              ref.read(workoutsViewModeProvider.notifier).state = !isGridView;
            },
            tooltip: isGridView ? 'List View' : 'Grid View',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.refresh(workoutPlansProvider),
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyWorkoutsPage(),
                ),
              );
            },
            tooltip: 'My Workouts',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: AsyncValueWidget(
              value: workoutsAsync,
              onRetry: () => ref.refresh(workoutPlansProvider),
              data: (workouts) {
                if (workouts.isEmpty) {
                  return const EmptyState(
                    icon: Icons.fitness_center,
                    title: 'No Workouts Available',
                    message: 'Check back later for new workout plans',
                  );
                }

                if (filteredWorkouts.isEmpty) {
                  return const EmptyState(
                    icon: Icons.search_off,
                    title: 'No Results',
                    message: 'No workouts match your search',
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async => ref.refresh(workoutPlansProvider),
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: isGridView
                          ? _buildWorkoutGrid(filteredWorkouts)
                          : _buildWorkoutList(filteredWorkouts),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateWorkoutPage(),
            ),
          );
        },
        icon: const Icon(Icons.add),
        label: const Text('Create Workout'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
    );
  }
}
