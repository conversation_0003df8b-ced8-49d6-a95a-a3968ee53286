import 'package:flutter/material.dart';
import '../models/exercise_model.dart';
import '../models/workout_model.dart';
import '../services/custom_workout_service.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../widgets/exercise_configuration_widget.dart';
import '../widgets/enhanced_progress_indicator.dart';
import '../widgets/fullscreen_exercise_search.dart';


class CreateWorkoutPage extends StatefulWidget {
  final WorkoutPlanModel? existingWorkout;

  const CreateWorkoutPage({super.key, this.existingWorkout});

  @override
  State<CreateWorkoutPage> createState() => _CreateWorkoutPageState();
}

class _CreateWorkoutPageState extends State<CreateWorkoutPage>
    with TickerProviderStateMixin {
  final CustomWorkoutService _customWorkoutService = CustomWorkoutService();
  final AuthService _authService = AuthService();
  final FirestoreService _firestoreService = FirestoreService();
  final PageController _pageController = PageController();
  
  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  // Workout data
  List<ExerciseModel> _selectedExercises = [];
  Map<String, WorkoutExercise> _exerciseConfigurations = {};
  String _selectedDifficulty = 'beginner';
  String _selectedCategory = 'Strength';
  
  int _currentStep = 0;
  bool _isLoading = false;
  bool _isDraftSaved = false;
  Map<String, String> _validationErrors = {};
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<String> _categories = [
    'Strength', 'Cardio', 'Flexibility', 'Sports', 'Rehabilitation'
  ];
  
  final List<String> _difficulties = [
    'beginner', 'intermediate', 'advanced'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));
    
    if (widget.existingWorkout != null) {
      _loadExistingWorkout();
    }
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }



  void _loadExistingWorkout() {
    final workout = widget.existingWorkout!;
    _nameController.text = workout.name;
    _descriptionController.text = workout.description;
    _selectedDifficulty = workout.difficulty;
    _selectedCategory = workout.category;
    
    // Note: For existing workout editing, we would need to load exercises
    // This is simplified for MVP
  }



  void _nextStep() {
    // Dismiss keyboard before navigating
    FocusScope.of(context).unfocus();
    
    if (!_canProceedFromStep(_currentStep)) {
      // Show validation errors
      String errorMessage = _validationErrors.values.first;
      _showSnackBar(errorMessage, isError: true);
      return;
    }
    
    if (_currentStep < 3) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    // Dismiss keyboard before navigating
    FocusScope.of(context).unfocus();
    
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceedFromStep(int step) {
    _validationErrors.clear();
    
    switch (step) {
      case 0:
        bool isValid = true;
        if (_nameController.text.trim().isEmpty) {
          _validationErrors['name'] = 'Workout name is required';
          isValid = false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _validationErrors['description'] = 'Description is required';
          isValid = false;
        }
        if (_nameController.text.trim().length < 3) {
          _validationErrors['name'] = 'Name must be at least 3 characters';
          isValid = false;
        }
        return isValid;
      case 1:
        if (_selectedExercises.isEmpty) {
          _validationErrors['exercises'] = 'Please select at least one exercise';
          return false;
        }
        if (_selectedExercises.length > 15) {
          _validationErrors['exercises'] = 'Maximum 15 exercises allowed';
          return false;
        }
        return true;
      case 2:
        if (_exerciseConfigurations.length != _selectedExercises.length) {
          _validationErrors['configuration'] = 'Please configure all exercises';
          return false;
        }
        // Validate each configuration
        for (var config in _exerciseConfigurations.values) {
          if (config.sets < 1 || config.sets > 10) {
            _validationErrors['configuration'] = 'Sets must be between 1 and 10';
            return false;
          }
          if (config.reps < 1 || config.reps > 100) {
            _validationErrors['configuration'] = 'Reps must be between 1 and 100';
            return false;
          }
        }
        return true;
      default:
        return true;
    }
  }

  Future<void> _saveWorkout() async {
    if (_authService.currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Calculate total duration
      List<WorkoutExercise> workoutExercises = _selectedExercises
          .map((exercise) => _exerciseConfigurations[exercise.id]!)
          .toList();
      
      int totalDuration = _customWorkoutService.calculateWorkoutDuration(workoutExercises);

      WorkoutPlanModel newWorkout = WorkoutPlanModel(
        id: widget.existingWorkout?.id ?? '',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        exercises: workoutExercises,
        duration: totalDuration,
        difficulty: _selectedDifficulty,
        category: _selectedCategory,
        imageUrl: "https://images.unsplash.com/photo-1607962837359-5e7e89f86776?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NTYyMDF8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NDgxMzc5MDN8&ixlib=rb-4.1.0&q=80&w=1080",
        isCustom: true,
        createdBy: _authService.currentUser!.uid,
        createdAt: DateTime.now(),
        isPublic: false,
      );

      // Validate workout
      Map<String, String> errors = _customWorkoutService.validateWorkout(newWorkout);
      if (errors.isNotEmpty) {
        _showSnackBar('Please fix the following: ${errors.values.join(', ')}', isError: true);
        setState(() {
          _isLoading = false;
        });
        return;
      }

      String? workoutId;
      if (widget.existingWorkout != null) {
        bool success = await _customWorkoutService.updateCustomWorkout(newWorkout);
        if (success) workoutId = newWorkout.id;
      } else {
        workoutId = await _customWorkoutService.createCustomWorkout(newWorkout);
      }

      setState(() {
        _isLoading = false;
      });

      if (workoutId != null) {
        _showSnackBar(
          widget.existingWorkout != null 
              ? 'Workout updated successfully! 🎉'
              : 'Workout created successfully! 🎉',
          isSuccess: true,
        );
        
        // Navigate back with result
        Navigator.of(context).pop(true);
      } else {
        _showSnackBar('Failed to save workout. Please try again.', isError: true);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('An error occurred. Please try again.', isError: true);
    }
  }

  Future<void> _saveDraft() async {
    // Save current progress as draft
    setState(() {
      _isDraftSaved = true;
    });
    
    _showSnackBar(
      'Draft saved! You can continue later 💾',
      isSuccess: true,
    );
    
    // Auto-hide the draft button after saving
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isDraftSaved = false;
        });
      }
    });
  }

  void _showSnackBar(String message, {bool isError = false, bool isSuccess = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError 
            ? Theme.of(context).colorScheme.error
            : isSuccess 
                ? Theme.of(context).colorScheme.secondary
                : Theme.of(context).colorScheme.surface,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back_ios,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          title: Text(
            widget.existingWorkout != null ? 'Edit Workout' : 'Create Workout',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          centerTitle: true,
          actions: [
            if (_currentStep > 0 && !_isDraftSaved)
              TextButton.icon(
                onPressed: _saveDraft,
                icon: Icon(
                  Icons.save_outlined,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                ),
                label: Text(
                  'Draft',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        body: SafeArea(
          child: Column(
            children: [
              EnhancedProgressIndicator(
                currentStep: _currentStep,
                totalSteps: 4,
                steps: WorkoutCreationSteps.steps,
                onStepTap: (step) {
                  if (step <= _currentStep) {
                    // Dismiss keyboard before navigating
                    FocusScope.of(context).unfocus();
                    setState(() {
                      _currentStep = step;
                    });
                    _pageController.animateToPage(
                      step,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                },
              ),
              Expanded(
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildBasicInfoStep(),
                    _buildExerciseSelectionStep(),
                    _buildConfigurationStep(),
                    _buildReviewStep(),
                  ],
                ),
              ),
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildBasicInfoStep() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Workout Details',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Let\'s start with the basics of your workout',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 20),
              
              // Workout Name
              _buildTextField(
                controller: _nameController,
                label: 'Workout Name',
                hint: 'e.g., Morning Power Session',
                icon: Icons.fitness_center,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              
              // Description
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                hint: 'Describe your workout goals and focus...',
                icon: Icons.description,
                maxLines: 3,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: 20),
              
              // Category Selection
              _buildSectionTitle('Category'),
              const SizedBox(height: 10),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: _categories.map((category) {
                  bool isSelected = category == _selectedCategory;
                  return ChoiceChip(
                    label: Text(
                      category,
                      style: TextStyle(
                        color: isSelected 
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 13,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (_) => setState(() => _selectedCategory = category),
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    selectedColor: Theme.of(context).colorScheme.primary,
                    side: BorderSide(
                      color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  );
                }).toList(),
              ),
              const SizedBox(height: 20),
              
              // Difficulty Selection
              _buildSectionTitle('Difficulty'),
              const SizedBox(height: 10),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: _difficulties.map((difficulty) {
                  bool isSelected = difficulty == _selectedDifficulty;
                  Color difficultyColor = _getDifficultyColor(difficulty);
                  return ChoiceChip(
                    label: Text(
                      difficulty.toUpperCase(),
                      style: TextStyle(
                        color: isSelected 
                            ? Theme.of(context).colorScheme.onPrimary
                            : difficultyColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (_) => setState(() => _selectedDifficulty = difficulty),
                    backgroundColor: difficultyColor.withOpacity(0.1),
                    selectedColor: difficultyColor,
                    side: BorderSide(color: difficultyColor),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  );
                }).toList(),
              ),
              const SizedBox(height: 20), // Add some bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExerciseSelectionStep() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Select Exercises',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Choose exercises for your workout (${_selectedExercises.length} selected)',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Search Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => FullscreenExerciseSearch(
                              selectedExercises: _selectedExercises,
                              onExerciseToggle: _toggleExercise,
                            ),
                          ),
                        );
                        // Ensure UI updates after returning from search
                        if (mounted) {
                          setState(() {});
                        }
                      },
                      icon: const Icon(Icons.search, size: 20),
                      label: const Text('Search & Add Exercises'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Selected Exercises List
            Expanded(
              child: _selectedExercises.isEmpty
                  ? _buildEmptyExerciseState()
                  : _buildSelectedExercisesList(),
            ),
          ],
        );
      },
    );
  }

  void _addExercise(ExerciseModel exercise) {
    if (!_selectedExercises.any((e) => e.id == exercise.id)) {
      setState(() {
        _selectedExercises.add(exercise);
        _exerciseConfigurations[exercise.id] = WorkoutExercise(
          exerciseId: exercise.id,
          sets: 3,
          reps: 12,
          duration: 0,
          restTime: 60,
        );
      });
    }
  }

  void _toggleExercise(ExerciseModel exercise) {
    setState(() {
      if (_selectedExercises.any((e) => e.id == exercise.id)) {
        _selectedExercises.removeWhere((e) => e.id == exercise.id);
        _exerciseConfigurations.remove(exercise.id);
      } else {
        _selectedExercises.add(exercise);
        // Add default configuration
        _exerciseConfigurations[exercise.id] = WorkoutExercise(
          exerciseId: exercise.id,
          sets: 3,
          reps: 12,
          duration: 0,
          restTime: 60,
        );
      }
    });
  }

  Widget _buildEmptyExerciseState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fitness_center,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 12),
            Text(
              'No exercises selected',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Tap the search button above to add exercises',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedExercisesList() {
    return ListView.builder(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8, top: 8),
      itemCount: _selectedExercises.length,
      itemBuilder: (context, index) {
        final exercise = _selectedExercises[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: exercise.imageUrl.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          exercise.imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.fitness_center,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.fitness_center,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      exercise.primaryMuscleGroup,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => _toggleExercise(exercise),
                icon: Icon(
                  Icons.remove_circle_outline,
                  color: Theme.of(context).colorScheme.error,
                  size: 22,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error.withOpacity(0.1),
                  padding: const EdgeInsets.all(6),
                  minimumSize: const Size(32, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConfigurationStep() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Configure Exercises',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Set up sets, reps, and timing for each exercise',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            itemCount: _selectedExercises.length,
            itemBuilder: (context, index) {
              final exercise = _selectedExercises[index];
              return ExerciseConfigurationWidget(
                exercise: exercise,
                initialConfiguration: _exerciseConfigurations[exercise.id],
                onConfigurationChanged: (config) {
                  setState(() {
                    _exerciseConfigurations[exercise.id] = config;
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }



  Widget _buildReviewStep() {
    int totalDuration = _customWorkoutService.calculateWorkoutDuration(
      _exerciseConfigurations.values.toList(),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review Workout',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Review your workout before saving',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 20),
          
          // Workout Summary Card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _nameController.text,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _descriptionController.text,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildSummaryTag('$totalDuration min', Icons.access_time, Theme.of(context).colorScheme.primary),
                    _buildSummaryTag(_selectedDifficulty, Icons.speed, _getDifficultyColor(_selectedDifficulty)),
                    _buildSummaryTag('${_selectedExercises.length} exercises', Icons.list, Theme.of(context).colorScheme.secondary),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          
          Text(
            'Exercises (${_selectedExercises.length})',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          
          // Exercise List
          ...(_selectedExercises.asMap().entries.map((entry) {
            int index = entry.key;
            ExerciseModel exercise = entry.value;
            WorkoutExercise? config = _exerciseConfigurations[exercise.id];
            
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exercise.name,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        if (config != null)
                          Text(
                            config.duration > 0 
                                ? '${config.sets} sets × ${config.duration}s'
                                : '${config.sets} sets × ${config.reps} reps',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          })),
          const SizedBox(height: 16), // Add bottom padding
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Theme.of(context).colorScheme.outline),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text(
                  'Previous',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: _isLoading 
                  ? null
                  : _currentStep == 3 
                      ? _saveWorkout
                      : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _currentStep == 3 
                          ? (widget.existingWorkout != null ? 'Update Workout' : 'Create Workout')
                          : 'Next',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    TextInputAction? textInputAction,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        TextField(
          controller: controller,
          maxLines: maxLines,
          textInputAction: textInputAction ?? (maxLines > 1 ? TextInputAction.newline : TextInputAction.next),
          onEditingComplete: () {
            if (textInputAction == TextInputAction.next) {
              FocusScope.of(context).nextFocus();
            } else if (textInputAction == TextInputAction.done) {
              FocusScope.of(context).unfocus();
            }
          },
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
            isDense: true,
          ),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
        fontSize: 14,
      ),
    );
  }

  Widget _buildSummaryTag(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Theme.of(context).colorScheme.secondary;
      case 'intermediate':
        return Theme.of(context).colorScheme.tertiary;
      case 'advanced':
        return Theme.of(context).colorScheme.error;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}