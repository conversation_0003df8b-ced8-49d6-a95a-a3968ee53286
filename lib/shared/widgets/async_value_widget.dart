import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'loading_state.dart';
import 'error_state.dart';

/// A widget that handles AsyncValue states from Riverpod
class AsyncValueWidget<T> extends StatelessWidget {
  final AsyncValue<T> value;
  final Widget Function(T data) data;
  final Widget? loading;
  final Widget Function(Object error, StackTrace stack)? error;
  final VoidCallback? onRetry;
  final bool skipLoadingOnRefresh;
  final bool skipLoadingOnReload;
  final bool skipError;

  const AsyncValueWidget({
    super.key,
    required this.value,
    required this.data,
    this.loading,
    this.error,
    this.onRetry,
    this.skipLoadingOnRefresh = true,
    this.skipLoadingOnReload = true,
    this.skipError = false,
  });

  @override
  Widget build(BuildContext context) {
    return value.when(
      skipLoadingOnRefresh: skipLoadingOnRefresh,
      skipLoadingOnReload: skipLoadingOnReload,
      skipError: skipError,
      data: data,
      loading: () => loading ?? const LoadingState(),
      error: (err, stack) => error?.call(err, stack) ?? 
          ErrorState(
            title: 'Something went wrong',
            message: err.toString(),
            onRetry: onRetry,
          ),
    );
  }
}

/// A sliver version of AsyncValueWidget for use in CustomScrollView
class AsyncValueSliverWidget<T> extends StatelessWidget {
  final AsyncValue<T> value;
  final Widget Function(T data) data;
  final Widget? loading;
  final Widget Function(Object error, StackTrace stack)? error;
  final VoidCallback? onRetry;

  const AsyncValueSliverWidget({
    super.key,
    required this.value,
    required this.data,
    this.loading,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return value.when(
      data: data,
      loading: () => SliverFillRemaining(
        child: loading ?? const LoadingState(),
      ),
      error: (err, stack) => SliverFillRemaining(
        child: error?.call(err, stack) ?? 
            ErrorState(
              title: 'Something went wrong',
              message: err.toString(),
              onRetry: onRetry,
            ),
      ),
    );
  }
}

/// Extension methods for AsyncValue
extension AsyncValueX<T> on AsyncValue<T> {
  /// Returns true if the value is currently loading (not including refresh/reload)
  bool get isLoading => this is AsyncLoading<T>;
  
  /// Returns true if the value has an error
  bool get hasError => this is AsyncError<T>;
  
  /// Returns true if the value has data
  bool get hasValue => this is AsyncData<T>;
  
  /// Returns the data if available, otherwise null
  T? get valueOrNull => this is AsyncData<T> ? (this as AsyncData<T>).value : null;
  
  /// Guard against null values in AsyncData
  AsyncValue<T> guard() {
    return maybeWhen(
      data: (value) => value != null 
          ? AsyncData<T>(value) 
          : AsyncLoading<T>() as AsyncValue<T>,
      orElse: () => this,
    );
  }
}

/// A widget that shows different content based on AsyncValue state
class AsyncValueBuilder<T> extends StatelessWidget {
  final AsyncValue<T> value;
  final Widget Function(BuildContext context, T data) builder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, Object error, StackTrace stack)? errorBuilder;
  final VoidCallback? onRetry;

  const AsyncValueBuilder({
    super.key,
    required this.value,
    required this.builder,
    this.loadingBuilder,
    this.errorBuilder,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return value.when(
      data: (data) => builder(context, data),
      loading: () => loadingBuilder?.call(context) ?? const LoadingState(),
      error: (error, stack) => errorBuilder?.call(context, error, stack) ??
          ErrorState(
            title: 'Error',
            message: error.toString(),
            onRetry: onRetry,
          ),
    );
  }
}