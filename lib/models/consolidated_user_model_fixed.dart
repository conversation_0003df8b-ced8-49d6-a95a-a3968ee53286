import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

// Type conversion helper utilities
class TypeConverters {
  // Safe integer parsing with default value
  static int parseInt(dynamic value, [int defaultValue = 0]) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final parsed = int.tryParse(value);
      if (parsed != null) return parsed;
      // Try parsing as double first, then convert to int
      final doubleValue = double.tryParse(value);
      if (doubleValue != null) return doubleValue.toInt();
    }
    return defaultValue;
  }

  // Safe double parsing with default value
  static double parseDouble(dynamic value, [double defaultValue = 0.0]) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed;
    }
    return defaultValue;
  }

  // Safe nullable double parsing
  static double? parseNullableDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed;
    }
    return null;
  }

  // Safe nullable integer parsing
  static int? parseNullableInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final parsed = int.tryParse(value);
      if (parsed != null) return parsed;
      // Try parsing as double first, then convert to int
      final doubleValue = double.tryParse(value);
      if (doubleValue != null) return doubleValue.toInt();
    }
    return null;
  }

  // Safe boolean parsing with default value
  static bool parseBool(dynamic value, [bool defaultValue = false]) {
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes') return true;
      if (lowerValue == 'false' || lowerValue == '0' || lowerValue == 'no') return false;
    }
    if (value is int) return value != 0;
    if (value is double) return value != 0.0;
    return defaultValue;
  }

  // Safe string parsing
  static String parseString(dynamic value, [String defaultValue = '']) {
    if (value == null) return defaultValue;
    return value.toString();
  }

  // Safe nullable string parsing
  static String? parseNullableString(dynamic value) {
    if (value == null) return null;
    if (value is String && value.isEmpty) return null;
    return value.toString();
  }

  // Safe DateTime parsing
  static DateTime parseDateTime(dynamic value, [DateTime? defaultValue]) {
    defaultValue ??= DateTime.now();
    if (value == null) return defaultValue;
    
    // Handle Firestore Timestamp
    if (value is Timestamp) return value.toDate();
    
    // Handle milliseconds since epoch
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    
    // Handle DateTime object
    if (value is DateTime) return value;
    
    // Handle string representation
    if (value is String) {
      // Try parsing as milliseconds
      final milliseconds = int.tryParse(value);
      if (milliseconds != null) {
        return DateTime.fromMillisecondsSinceEpoch(milliseconds);
      }
      
      // Try parsing as ISO8601 or other date formats
      final dateTime = DateTime.tryParse(value);
      if (dateTime != null) return dateTime;
    }
    
    return defaultValue;
  }

  // Safe nullable DateTime parsing
  static DateTime? parseNullableDateTime(dynamic value) {
    if (value == null) return null;
    
    // Handle Firestore Timestamp
    if (value is Timestamp) return value.toDate();
    
    // Handle milliseconds since epoch
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    
    // Handle DateTime object
    if (value is DateTime) return value;
    
    // Handle string representation
    if (value is String) {
      // Try parsing as milliseconds
      final milliseconds = int.tryParse(value);
      if (milliseconds != null) {
        return DateTime.fromMillisecondsSinceEpoch(milliseconds);
      }
      
      // Try parsing as ISO8601 or other date formats
      return DateTime.tryParse(value);
    }
    
    return null;
  }

  // Safe list parsing
  static List<T> parseList<T>(dynamic value, T Function(dynamic) itemParser, [List<T>? defaultValue]) {
    if (value == null) return defaultValue ?? [];
    if (value is! List) return defaultValue ?? [];
    
    try {
      return value.map((item) {
        try {
          return itemParser(item);
        } catch (e) {
          // Skip invalid items
          return null;
        }
      }).whereType<T>().toList();
    } catch (e) {
      return defaultValue ?? [];
    }
  }

  // Safe map parsing
  static Map<String, dynamic> parseMap(dynamic value, [Map<String, dynamic>? defaultValue]) {
    if (value == null) return defaultValue ?? {};
    if (value is Map<String, dynamic>) return value;
    if (value is Map) {
      try {
        return Map<String, dynamic>.from(value);
      } catch (e) {
        return defaultValue ?? {};
      }
    }
    return defaultValue ?? {};
  }
}

// Enums
enum Gender { male, female, other, preferNotToSay }
enum WorkoutEnvironment { largeGym, smallGym, homeBasic, homeNoEquipment }
enum FitnessGoalType {
  notReady,
  sportSpecific,
  buildMuscle,
  calisthenics,
  weightLoss,
  healthOptimization,
  cardioBunny,
  increaseStrength,
  increaseStamina,
}

// Fitness Goal class
class FitnessGoal {
  final FitnessGoalType type;
  final int priority;
  final String? sportActivity;
  final DateTime selectedAt;

  FitnessGoal({
    required this.type,
    required this.priority,
    this.sportActivity,
    required this.selectedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'priority': priority,
      'sportActivity': sportActivity,
      'selectedAt': selectedAt.millisecondsSinceEpoch,
    };
  }

  factory FitnessGoal.fromJson(Map<String, dynamic> json) {
    return FitnessGoal(
      type: FitnessGoalType.values.firstWhere(
        (e) => e.toString().split('.').last == TypeConverters.parseString(json['type']),
        orElse: () => FitnessGoalType.notReady,
      ),
      priority: TypeConverters.parseInt(json['priority'], 0),
      sportActivity: TypeConverters.parseNullableString(json['sportActivity']),
      selectedAt: TypeConverters.parseDateTime(json['selectedAt']),
    );
  }
}

// Personal Information
class PersonalInfo {
  final String name;
  final Gender? gender;
  final DateTime? dateOfBirth;
  final double? height; // in cm for metric, inches for imperial
  final double? weight; // in kg for metric, lbs for imperial
  final String preferredUnits; // 'metric' or 'imperial'

  PersonalInfo({
    required this.name,
    this.gender,
    this.dateOfBirth,
    this.height,
    this.weight,
    this.preferredUnits = 'metric',
  });

  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'gender': gender?.toString().split('.').last,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'height': height,
      'weight': weight,
      'preferredUnits': preferredUnits,
    };
  }

  factory PersonalInfo.fromJson(Map<String, dynamic> json) {
    // Handle age field if dateOfBirth is missing
    DateTime? dateOfBirth = TypeConverters.parseNullableDateTime(json['dateOfBirth']);
    if (dateOfBirth == null && json['age'] != null) {
      final age = TypeConverters.parseNullableInt(json['age']);
      if (age != null) {
        dateOfBirth = DateTime.now().subtract(Duration(days: age * 365));
      }
    }

    return PersonalInfo(
      name: TypeConverters.parseString(json['name'], ''),
      gender: json['gender'] != null 
          ? Gender.values.firstWhere(
              (e) => e.toString().split('.').last == TypeConverters.parseString(json['gender']),
              orElse: () => Gender.preferNotToSay,
            )
          : null,
      dateOfBirth: dateOfBirth,
      height: TypeConverters.parseNullableDouble(json['height']),
      weight: TypeConverters.parseNullableDouble(json['weight']),
      preferredUnits: TypeConverters.parseString(json['preferredUnits'], 'metric'),
    );
  }

  PersonalInfo copyWith({
    String? name,
    Gender? gender,
    DateTime? dateOfBirth,
    double? height,
    double? weight,
    String? preferredUnits,
  }) {
    return PersonalInfo(
      name: name ?? this.name,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      preferredUnits: preferredUnits ?? this.preferredUnits,
    );
  }
}

// Fitness Profile
class FitnessProfile {
  final List<FitnessGoal> goals;
  final double cardioLevel; // 0.0 to 1.0
  final double weightliftingLevel; // 0.0 to 1.0
  final String? exercisesToAvoid;
  final String? personalCoachNotes;

  FitnessProfile({
    this.goals = const [],
    this.cardioLevel = 0.0,
    this.weightliftingLevel = 0.0,
    this.exercisesToAvoid,
    this.personalCoachNotes,
  });

  Map<String, dynamic> toJson() {
    return {
      'goals': goals.map((g) => g.toJson()).toList(),
      'cardioLevel': cardioLevel,
      'weightliftingLevel': weightliftingLevel,
      'exercisesToAvoid': exercisesToAvoid,
      'personalCoachNotes': personalCoachNotes,
    };
  }

  factory FitnessProfile.fromJson(Map<String, dynamic> json) {
    return FitnessProfile(
      goals: TypeConverters.parseList<FitnessGoal>(
        json['goals'],
        (item) => FitnessGoal.fromJson(TypeConverters.parseMap(item)),
        [],
      ),
      cardioLevel: TypeConverters.parseDouble(json['cardioLevel'], 0.0).clamp(0.0, 1.0),
      weightliftingLevel: TypeConverters.parseDouble(json['weightliftingLevel'], 0.0).clamp(0.0, 1.0),
      exercisesToAvoid: TypeConverters.parseNullableString(json['exercisesToAvoid']),
      personalCoachNotes: TypeConverters.parseNullableString(json['personalCoachNotes']),
    );
  }

  FitnessProfile copyWith({
    List<FitnessGoal>? goals,
    double? cardioLevel,
    double? weightliftingLevel,
    String? exercisesToAvoid,
    String? personalCoachNotes,
  }) {
    return FitnessProfile(
      goals: goals ?? this.goals,
      cardioLevel: cardioLevel ?? this.cardioLevel,
      weightliftingLevel: weightliftingLevel ?? this.weightliftingLevel,
      exercisesToAvoid: exercisesToAvoid ?? this.exercisesToAvoid,
      personalCoachNotes: personalCoachNotes ?? this.personalCoachNotes,
    );
  }
}

// Workout Preferences
class WorkoutPreferences {
  final List<WorkoutEnvironment> environments;
  final int workoutsPerWeek;
  final int? workoutDurationMinutes;
  final String? additionalNotes;

  WorkoutPreferences({
    this.environments = const [WorkoutEnvironment.homeNoEquipment],
    this.workoutsPerWeek = 3,
    this.workoutDurationMinutes,
    this.additionalNotes,
  });

  Map<String, dynamic> toJson() {
    return {
      'environments': environments.map((e) => e.toString().split('.').last).toList(),
      'workoutsPerWeek': workoutsPerWeek,
      'workoutDurationMinutes': workoutDurationMinutes,
      'additionalNotes': additionalNotes,
    };
  }

  factory WorkoutPreferences.fromJson(Map<String, dynamic> json) {
    return WorkoutPreferences(
      environments: TypeConverters.parseList<WorkoutEnvironment>(
        json['environments'],
        (item) {
          final envString = TypeConverters.parseString(item);
          return WorkoutEnvironment.values.firstWhere(
            (env) => env.toString().split('.').last == envString,
            orElse: () => WorkoutEnvironment.homeNoEquipment,
          );
        },
        [WorkoutEnvironment.homeNoEquipment],
      ),
      workoutsPerWeek: TypeConverters.parseInt(json['workoutsPerWeek'], 3),
      workoutDurationMinutes: TypeConverters.parseNullableInt(json['workoutDurationMinutes']),
      additionalNotes: TypeConverters.parseNullableString(json['additionalNotes']),
    );
  }

  WorkoutPreferences copyWith({
    List<WorkoutEnvironment>? environments,
    int? workoutsPerWeek,
    int? workoutDurationMinutes,
    String? additionalNotes,
  }) {
    return WorkoutPreferences(
      environments: environments ?? this.environments,
      workoutsPerWeek: workoutsPerWeek ?? this.workoutsPerWeek,
      workoutDurationMinutes: workoutDurationMinutes ?? this.workoutDurationMinutes,
      additionalNotes: additionalNotes ?? this.additionalNotes,
    );
  }
}

// User Statistics
class UserStats {
  final int totalWorkouts;
  final int totalMinutes;
  final int weeklyGoal; // minutes per week
  final int currentStreak;
  final double? totalCaloriesBurned;
  final DateTime? lastWorkoutDate;
  final Map<String, dynamic> monthlyStats; // For tracking monthly progress

  UserStats({
    this.totalWorkouts = 0,
    this.totalMinutes = 0,
    this.weeklyGoal = 150,
    this.currentStreak = 0,
    this.totalCaloriesBurned,
    this.lastWorkoutDate,
    this.monthlyStats = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'totalWorkouts': totalWorkouts,
      'totalMinutes': totalMinutes,
      'weeklyGoal': weeklyGoal,
      'currentStreak': currentStreak,
      'totalCaloriesBurned': totalCaloriesBurned,
      'lastWorkoutDate': lastWorkoutDate?.toIso8601String(),
      'monthlyStats': monthlyStats,
    };
  }

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalWorkouts: TypeConverters.parseInt(json['totalWorkouts'], 0),
      totalMinutes: TypeConverters.parseInt(json['totalMinutes'], 0),
      weeklyGoal: TypeConverters.parseInt(json['weeklyGoal'], 150),
      currentStreak: TypeConverters.parseInt(json['currentStreak'], 0),
      // Handle both 'totalCaloriesBurned' and 'totalCalories' field names
      totalCaloriesBurned: TypeConverters.parseNullableDouble(json['totalCaloriesBurned']) ?? 
                          TypeConverters.parseNullableDouble(json['totalCalories']),
      lastWorkoutDate: TypeConverters.parseNullableDateTime(json['lastWorkoutDate']),
      monthlyStats: TypeConverters.parseMap(json['monthlyStats'], {}),
    );
  }

  UserStats copyWith({
    int? totalWorkouts,
    int? totalMinutes,
    int? weeklyGoal,
    int? currentStreak,
    double? totalCaloriesBurned,
    DateTime? lastWorkoutDate,
    Map<String, dynamic>? monthlyStats,
  }) {
    return UserStats(
      totalWorkouts: totalWorkouts ?? this.totalWorkouts,
      totalMinutes: totalMinutes ?? this.totalMinutes,
      weeklyGoal: weeklyGoal ?? this.weeklyGoal,
      currentStreak: currentStreak ?? this.currentStreak,
      totalCaloriesBurned: totalCaloriesBurned ?? this.totalCaloriesBurned,
      lastWorkoutDate: lastWorkoutDate ?? this.lastWorkoutDate,
      monthlyStats: monthlyStats ?? this.monthlyStats,
    );
  }
}

// User Preferences
class UserPreferences {
  final String units; // 'metric' or 'imperial'
  final bool notifications;
  final String theme; // 'light', 'dark', 'system'
  final bool onboardingComplete;
  final bool comprehensiveOnboardingComplete;
  final bool fitnessGoalsSet;
  final String? language;
  final Map<String, dynamic> appSettings;

  UserPreferences({
    this.units = 'metric',
    this.notifications = true,
    this.theme = 'system',
    this.onboardingComplete = false,
    this.comprehensiveOnboardingComplete = false,
    this.fitnessGoalsSet = false,
    this.language,
    this.appSettings = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'units': units,
      'notifications': notifications,
      'theme': theme,
      'onboardingComplete': onboardingComplete,
      'comprehensiveOnboardingComplete': comprehensiveOnboardingComplete,
      'fitnessGoalsSet': fitnessGoalsSet,
      'language': language,
      'appSettings': appSettings,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      units: TypeConverters.parseString(json['units'], 'metric'),
      notifications: TypeConverters.parseBool(json['notifications'], true),
      theme: TypeConverters.parseString(json['theme'], 'system'),
      onboardingComplete: TypeConverters.parseBool(json['onboardingComplete'], false),
      comprehensiveOnboardingComplete: TypeConverters.parseBool(json['comprehensiveOnboardingComplete'], false),
      fitnessGoalsSet: TypeConverters.parseBool(json['fitnessGoalsSet'], false),
      language: TypeConverters.parseNullableString(json['language']),
      appSettings: TypeConverters.parseMap(json['appSettings'], {}),
    );
  }

  UserPreferences copyWith({
    String? units,
    bool? notifications,
    String? theme,
    bool? onboardingComplete,
    bool? comprehensiveOnboardingComplete,
    bool? fitnessGoalsSet,
    String? language,
    Map<String, dynamic>? appSettings,
  }) {
    return UserPreferences(
      units: units ?? this.units,
      notifications: notifications ?? this.notifications,
      theme: theme ?? this.theme,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      comprehensiveOnboardingComplete: comprehensiveOnboardingComplete ?? this.comprehensiveOnboardingComplete,
      fitnessGoalsSet: fitnessGoalsSet ?? this.fitnessGoalsSet,
      language: language ?? this.language,
      appSettings: appSettings ?? this.appSettings,
    );
  }
}

// Main Consolidated User Model
class ConsolidatedUserModel {
  final String uid;
  final String email;
  final String? displayName;
  final String? photoURL;
  final PersonalInfo personalInfo;
  final FitnessProfile fitnessProfile;
  final WorkoutPreferences workoutPreferences;
  final UserStats stats;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  ConsolidatedUserModel({
    required this.uid,
    required this.email,
    this.displayName,
    this.photoURL,
    required this.personalInfo,
    required this.fitnessProfile,
    required this.workoutPreferences,
    required this.stats,
    required this.preferences,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'personalInfo': personalInfo.toJson(),
      'fitnessProfile': fitnessProfile.toJson(),
      'workoutPreferences': workoutPreferences.toJson(),
      'stats': stats.toJson(),
      'preferences': preferences.toJson(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory ConsolidatedUserModel.fromJson(Map<String, dynamic> json) {
    // Handle mixed structure where some fields might be at root level
    Map<String, dynamic> personalInfoData = TypeConverters.parseMap(json['personalInfo'], {});
    
    // If personalInfo fields are at root level, merge them
    if (json['name'] != null) personalInfoData['name'] = json['name'];
    if (json['gender'] != null) personalInfoData['gender'] = json['gender'];
    if (json['dateOfBirth'] != null) personalInfoData['dateOfBirth'] = json['dateOfBirth'];
    if (json['height'] != null) personalInfoData['height'] = json['height'];
    if (json['weight'] != null) personalInfoData['weight'] = json['weight'];
    if (json['age'] != null && personalInfoData['dateOfBirth'] == null) {
      personalInfoData['age'] = json['age'];
    }
    if (json['preferredUnits'] != null) personalInfoData['preferredUnits'] = json['preferredUnits'];
    
    return ConsolidatedUserModel(
      uid: TypeConverters.parseString(json['uid'], ''),
      email: TypeConverters.parseString(json['email'], ''),
      displayName: TypeConverters.parseNullableString(json['displayName']),
      photoURL: TypeConverters.parseNullableString(json['photoURL']),
      personalInfo: PersonalInfo.fromJson(personalInfoData),
      fitnessProfile: FitnessProfile.fromJson(TypeConverters.parseMap(json['fitnessProfile'], {})),
      workoutPreferences: WorkoutPreferences.fromJson(TypeConverters.parseMap(json['workoutPreferences'], {})),
      stats: UserStats.fromJson(TypeConverters.parseMap(json['stats'], {})),
      preferences: UserPreferences.fromJson(TypeConverters.parseMap(json['preferences'], {})),
      createdAt: TypeConverters.parseDateTime(json['createdAt']),
      updatedAt: TypeConverters.parseDateTime(json['updatedAt']),
    );
  }

  // Convert to JSON string for caching
  String toJsonString() {
    return jsonEncode(toJson());
  }

  // Create from JSON string
  factory ConsolidatedUserModel.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return ConsolidatedUserModel.fromJson(json);
  }

  ConsolidatedUserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    PersonalInfo? personalInfo,
    FitnessProfile? fitnessProfile,
    WorkoutPreferences? workoutPreferences,
    UserStats? stats,
    UserPreferences? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ConsolidatedUserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      personalInfo: personalInfo ?? this.personalInfo,
      fitnessProfile: fitnessProfile ?? this.fitnessProfile,
      workoutPreferences: workoutPreferences ?? this.workoutPreferences,
      stats: stats ?? this.stats,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Helper methods for backward compatibility
  String get name => personalInfo.name;
  int? get age => personalInfo.age;
  Gender? get gender => personalInfo.gender;
  double? get height => personalInfo.height;
  double? get weight => personalInfo.weight;
  bool get onboardingCompleted => preferences.onboardingComplete;
  bool get comprehensiveOnboardingCompleted => preferences.comprehensiveOnboardingComplete;

  // Migration helper - create from old UserModel
  factory ConsolidatedUserModel.fromLegacyUserModel(Map<String, dynamic> userData) {
    final now = DateTime.now();
    
    // Build personalInfo data from various sources
    Map<String, dynamic> personalInfoData = {};
    if (userData['personalInfo'] is Map) {
      personalInfoData = Map<String, dynamic>.from(userData['personalInfo']);
    }
    
    // Merge root-level fields into personalInfo
    personalInfoData['name'] = personalInfoData['name'] ?? 
                              userData['name'] ?? 
                              userData['displayName'] ?? '';
    personalInfoData['gender'] = personalInfoData['gender'] ?? userData['gender'];
    personalInfoData['dateOfBirth'] = personalInfoData['dateOfBirth'] ?? userData['dateOfBirth'];
    personalInfoData['height'] = personalInfoData['height'] ?? userData['height'];
    personalInfoData['weight'] = personalInfoData['weight'] ?? userData['weight'];
    personalInfoData['preferredUnits'] = personalInfoData['preferredUnits'] ?? 
                                        userData['preferredUnits'] ?? 
                                        userData['units'] ?? 
                                        'metric';
    
    return ConsolidatedUserModel(
      uid: TypeConverters.parseString(userData['uid'], ''),
      email: TypeConverters.parseString(userData['email'], ''),
      displayName: TypeConverters.parseNullableString(userData['displayName']) ?? 
                   TypeConverters.parseNullableString(userData['name']),
      photoURL: TypeConverters.parseNullableString(userData['photoURL']),
      personalInfo: PersonalInfo.fromJson(personalInfoData),
      fitnessProfile: FitnessProfile.fromJson(TypeConverters.parseMap(userData['fitnessProfile'], {})),
      workoutPreferences: WorkoutPreferences.fromJson(TypeConverters.parseMap(userData['workoutPreferences'], {})),
      stats: UserStats.fromJson(TypeConverters.parseMap(userData['stats'], {})),
      preferences: UserPreferences.fromJson(TypeConverters.parseMap(userData['preferences'], {})),
      createdAt: TypeConverters.parseDateTime(userData['createdAt'], now),
      updatedAt: TypeConverters.parseDateTime(userData['updatedAt'], now),
    );
  }

  // Create from Firestore DocumentSnapshot
  factory ConsolidatedUserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    data['uid'] = doc.id; // Ensure UID is set from document ID
    return ConsolidatedUserModel.fromJson(data);
  }

  // Create default user model for new users
  factory ConsolidatedUserModel.createDefault({
    required String uid,
    required String email,
    String? displayName,
    String? photoURL,
  }) {
    final now = DateTime.now();
    return ConsolidatedUserModel(
      uid: uid,
      email: email,
      displayName: displayName,
      photoURL: photoURL,
      personalInfo: PersonalInfo(
        name: displayName ?? email.split('@').first,
        preferredUnits: 'metric',
      ),
      fitnessProfile: FitnessProfile(),
      workoutPreferences: WorkoutPreferences(),
      stats: UserStats(),
      preferences: UserPreferences(),
      createdAt: now,
      updatedAt: now,
    );
  }
}