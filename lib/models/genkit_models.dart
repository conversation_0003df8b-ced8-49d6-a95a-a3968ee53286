// Data models for Genkit integration

class GenkitExercise {
  final String id;
  final String name;
  final String description;
  final String primaryMuscleGroup;
  final List<String> secondaryMuscleGroups;
  final String difficulty;
  final int? reps;
  final int? sets;
  final int? duration;
  
  GenkitExercise({
    required this.id,
    required this.name,
    required this.description,
    required this.primaryMuscleGroup,
    required this.secondaryMuscleGroups,
    required this.difficulty,
    this.reps,
    this.sets,
    this.duration,
  });
  
  factory GenkitExercise.fromJson(Map<String, dynamic> json) {
    return GenkitExercise(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      primaryMuscleGroup: json['primaryMuscleGroup'] ?? '',
      secondaryMuscleGroups: List<String>.from(json['secondaryMuscleGroups'] ?? []),
      difficulty: json['difficulty'] ?? 'medium',
      reps: json['reps'],
      sets: json['sets'],
      duration: json['duration'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'primaryMuscleGroup': primaryMuscleGroup,
      'secondaryMuscleGroups': secondaryMuscleGroups,
      'difficulty': difficulty,
      'reps': reps,
      'sets': sets,
      'duration': duration,
    };
  }
}

class ExerciseRecommendation {
  final GenkitExercise exercise;
  final String reason;
  final List<GenkitExercise> alternatives;
  final List<String> musclesWorked;
  
  ExerciseRecommendation({
    required this.exercise,
    required this.reason,
    required this.alternatives,
    required this.musclesWorked,
  });
}

class WorkoutAnalysis {
  final String overallPerformance;
  final List<String> strengths;
  final List<String> areasForImprovement;
  final String recoveryStatus;
  final String nextWorkoutRecommendation;
  
  WorkoutAnalysis({
    required this.overallPerformance,
    required this.strengths,
    required this.areasForImprovement,
    required this.recoveryStatus,
    required this.nextWorkoutRecommendation,
  });
}

class ChatResponse {
  final String response;
  final String conversationId;
  final List<String>? suggestions;
  
  ChatResponse({
    required this.response,
    required this.conversationId,
    this.suggestions,
  });
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final List<String>? suggestions;
  
  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.suggestions,
  });
}