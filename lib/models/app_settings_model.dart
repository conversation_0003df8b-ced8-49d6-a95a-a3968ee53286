enum ThemeMode { system, light, dark }
enum Units { metric, imperial }
enum Language { english, spanish, french, german }
enum NotificationFrequency { none, daily, weekly, beforeWorkout }

class AppSettings {
  final ThemeMode themeMode;
  final Units units;
  final Language language;
  final bool notificationsEnabled;
  final NotificationFrequency notificationFrequency;
  final bool workoutReminders;
  final bool progressUpdates;
  final bool socialSharing;
  final bool analyticsEnabled;
  final bool crashReporting;
  final bool autoPlayVideos;
  final bool soundEffects;
  final int restTimerDuration; // seconds
  final bool showCalories;
  final bool showHeartRate;
  final DateTime updatedAt;

  AppSettings({
    this.themeMode = ThemeMode.system,
    this.units = Units.imperial,
    this.language = Language.english,
    this.notificationsEnabled = true,
    this.notificationFrequency = NotificationFrequency.beforeWorkout,
    this.workoutReminders = true,
    this.progressUpdates = true,
    this.socialSharing = false,
    this.analyticsEnabled = true,
    this.crashReporting = true,
    this.autoPlayVideos = true,
    this.soundEffects = true,
    this.restTimerDuration = 60,
    this.showCalories = true,
    this.showHeartRate = false,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.toString().split('.').last,
      'units': units.toString().split('.').last,
      'language': language.toString().split('.').last,
      'notificationsEnabled': notificationsEnabled,
      'notificationFrequency': notificationFrequency.toString().split('.').last,
      'workoutReminders': workoutReminders,
      'progressUpdates': progressUpdates,
      'socialSharing': socialSharing,
      'analyticsEnabled': analyticsEnabled,
      'crashReporting': crashReporting,
      'autoPlayVideos': autoPlayVideos,
      'soundEffects': soundEffects,
      'restTimerDuration': restTimerDuration,
      'showCalories': showCalories,
      'showHeartRate': showHeartRate,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      themeMode: ThemeMode.values.firstWhere(
        (e) => e.toString().split('.').last == json['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      units: Units.values.firstWhere(
        (e) => e.toString().split('.').last == json['units'],
        orElse: () => Units.imperial,
      ),
      language: Language.values.firstWhere(
        (e) => e.toString().split('.').last == json['language'],
        orElse: () => Language.english,
      ),
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      notificationFrequency: NotificationFrequency.values.firstWhere(
        (e) => e.toString().split('.').last == json['notificationFrequency'],
        orElse: () => NotificationFrequency.beforeWorkout,
      ),
      workoutReminders: json['workoutReminders'] ?? true,
      progressUpdates: json['progressUpdates'] ?? true,
      socialSharing: json['socialSharing'] ?? false,
      analyticsEnabled: json['analyticsEnabled'] ?? true,
      crashReporting: json['crashReporting'] ?? true,
      autoPlayVideos: json['autoPlayVideos'] ?? true,
      soundEffects: json['soundEffects'] ?? true,
      restTimerDuration: json['restTimerDuration'] ?? 60,
      showCalories: json['showCalories'] ?? true,
      showHeartRate: json['showHeartRate'] ?? false,
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : DateTime.now(),
    );
  }

  AppSettings copyWith({
    ThemeMode? themeMode,
    Units? units,
    Language? language,
    bool? notificationsEnabled,
    NotificationFrequency? notificationFrequency,
    bool? workoutReminders,
    bool? progressUpdates,
    bool? socialSharing,
    bool? analyticsEnabled,
    bool? crashReporting,
    bool? autoPlayVideos,
    bool? soundEffects,
    int? restTimerDuration,
    bool? showCalories,
    bool? showHeartRate,
    DateTime? updatedAt,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      units: units ?? this.units,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      notificationFrequency: notificationFrequency ?? this.notificationFrequency,
      workoutReminders: workoutReminders ?? this.workoutReminders,
      progressUpdates: progressUpdates ?? this.progressUpdates,
      socialSharing: socialSharing ?? this.socialSharing,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      crashReporting: crashReporting ?? this.crashReporting,
      autoPlayVideos: autoPlayVideos ?? this.autoPlayVideos,
      soundEffects: soundEffects ?? this.soundEffects,
      restTimerDuration: restTimerDuration ?? this.restTimerDuration,
      showCalories: showCalories ?? this.showCalories,
      showHeartRate: showHeartRate ?? this.showHeartRate,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
} 