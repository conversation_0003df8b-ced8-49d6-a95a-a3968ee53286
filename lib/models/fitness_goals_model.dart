

enum FitnessGoalType {
  notReady,
  sportSpecific,
  buildMuscle,
  calisthenics,
  weightLoss,
  healthOptimization,
  cardioBunny,
  increaseStrength,
}

class FitnessGoal {
  final FitnessGoalType type;
  final int priority;
  final String? sportActivity; // For sport-specific goals
  final DateTime selectedAt;

  FitnessGoal({
    required this.type,
    required this.priority,
    this.sportActivity,
    required this.selectedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'priority': priority,
      'sportActivity': sportActivity,
      'selectedAt': selectedAt.millisecondsSinceEpoch,
    };
  }

  factory FitnessGoal.fromJson(Map<String, dynamic> json) {
    return FitnessGoal(
      type: FitnessGoalType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => FitnessGoalType.notReady,
      ),
      priority: json['priority'] ?? 0,
      sportActivity: json['sportActivity'],
      selectedAt: json['selectedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['selectedAt'])
          : DateTime.now(),
    );
  }
}

class FitnessGoalsModel {
  final String userId;
  final List<FitnessGoal> goals;
  final String? additionalNotes;
  final DateTime completedAt;
  final bool isOnboardingComplete;

  FitnessGoalsModel({
    required this.userId,
    required this.goals,
    this.additionalNotes,
    required this.completedAt,
    this.isOnboardingComplete = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'goals': goals.map((g) => g.toJson()).toList(),
      'additionalNotes': additionalNotes,
      'completedAt': completedAt.millisecondsSinceEpoch,
      'isOnboardingComplete': isOnboardingComplete,
    };
  }

  factory FitnessGoalsModel.fromJson(Map<String, dynamic> json) {
    return FitnessGoalsModel(
      userId: json['userId'] ?? '',
      goals: (json['goals'] as List<dynamic>?)
              ?.map((g) => FitnessGoal.fromJson(g))
              .toList() ??
          [],
      additionalNotes: json['additionalNotes'],
      completedAt: json['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
          : DateTime.now(),
      isOnboardingComplete: json['isOnboardingComplete'] ?? false,
    );
  }

  FitnessGoalsModel copyWith({
    String? userId,
    List<FitnessGoal>? goals,
    String? additionalNotes,
    DateTime? completedAt,
    bool? isOnboardingComplete,
  }) {
    return FitnessGoalsModel(
      userId: userId ?? this.userId,
      goals: goals ?? this.goals,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      completedAt: completedAt ?? this.completedAt,
      isOnboardingComplete: isOnboardingComplete ?? this.isOnboardingComplete,
    );
  }
}

// Helper class for goal information
class FitnessGoalInfo {
  final FitnessGoalType type;
  final String title;
  final String description;
  final String? recommendation;
  final bool requiresInput;

  const FitnessGoalInfo({
    required this.type,
    required this.title,
    required this.description,
    this.recommendation,
    this.requiresInput = false,
  });

  static const List<FitnessGoalInfo> allGoals = [
    FitnessGoalInfo(
      type: FitnessGoalType.notReady,
      title: "I'm Not Ready to Plan My Fitness Yet",
      description: "Selecting this option allows you to skip all fitness-related questions for now and revisit them later under the Fitness tab. All other options on this screen will be disabled.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.sportSpecific,
      title: "Training for a Specific Sport or Activity",
      description: "Specify your sport or activity in the input box below.",
      requiresInput: true,
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.buildMuscle,
      title: "Build Muscle Mass and Size",
      description: "You'll train with a mix of volume and intensity, focusing on both compound movements and isolated exercises when necessary. This plan is designed to maximize muscle growth and help you build size like never before.",
      recommendation: "Individuals aiming to prioritize muscle mass and size above all else.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.calisthenics,
      title: "Calisthenics",
      description: "You'll focus on mastering bodyweight exercises that improve strength, flexibility, and balance. This plan emphasizes full-body control and endurance, making it perfect for calisthenics enthusiasts.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.weightLoss,
      title: "Weight Loss and Management",
      description: "You'll start with resistance training to build muscle mass and gradually incorporate endurance exercises to support weight management and cardiovascular health. Over time, this plan will transition into a health optimization routine tailored to your needs.",
      recommendation: "Individuals with minimal movement and/or experience with being overweight or obese.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.healthOptimization,
      title: "Optimize my Health and Fitness",
      description: "You'll follow a balanced routine that includes exercises to increase and maintain a healthy muscle mass, improve cardiovascular fitness, and support weight maintenance with short at home flexibility/Stretch during rest days. This plan is perfect for promoting longevity and overall well-being and fitness.",
      recommendation: "Everyday Users",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.cardioBunny,
      title: "I Am a Cardio Bunny",
      description: "Designed for those who love endurance exercises like running, cycling, or swimming, this plan focuses on building stamina and improving overall cardiovascular fitness.",
      recommendation: "Endurance Focus",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.increaseStrength,
      title: "Increase Strength and Lift Heavier",
      description: "Perfect for individuals focused on maximizing strength and powerlifting, utilizing low-rep, high-weight training with longer rest periods to build power and lift heavier weights.",
    ),
  ];
} 