import 'package:cloud_firestore/cloud_firestore.dart';

class ExerciseModel {
  final String id;
  final String name;
  final String category;
  final String description;
  final List<String> instructions;
  final List<String> muscleGroups;
  final String primaryMuscleGroup;
  final List<String> secondaryMuscleGroups;
  final String difficulty;
  final String imageUrl;
  final double caloriesPerMinute;
  final bool isBodyweight;
  final List<String> equipment;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isTimerBased;
  final String? videoUrl;

  ExerciseModel({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.instructions,
    required this.muscleGroups,
    required this.primaryMuscleGroup,
    required this.secondaryMuscleGroups,
    required this.difficulty,
    required this.imageUrl,
    required this.caloriesPerMinute,
    required this.isBodyweight,
    required this.equipment,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
    this.isTimerBased = false,
    this.videoUrl,
  });

  factory ExerciseModel.fromJson(Map<String, dynamic> json) {
    bool isTimerBased = false;
    String name = (json['name'] ?? '').toString().toLowerCase();
    List<String> timerBasedKeywords = ['plank', 'hold', 'wall sit', 'dead hang', 'hollow body', 'superman'];
    for (String keyword in timerBasedKeywords) {
      if (name.contains(keyword)) {
        isTimerBased = true;
        break;
      }
    }

    return ExerciseModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      category: json['category'] ?? '',
      description: json['description'] ?? '',
      instructions: List<String>.from(json['instructions'] ?? []),
      muscleGroups: List<String>.from(json['muscleGroups'] ?? []),
      primaryMuscleGroup: json['primaryMuscleGroup'] ?? '',
      secondaryMuscleGroups: List<String>.from(json['secondaryMuscleGroups'] ?? []),
      difficulty: json['difficulty'] ?? 'beginner',
      imageUrl: json['imageUrl'] ?? '',
      caloriesPerMinute: (json['caloriesPerMinute'] ?? 8.0).toDouble(),
      isBodyweight: json['isBodyweight'] ?? false,
      equipment: List<String>.from(json['equipment'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is Timestamp
              ? (json['createdAt'] as Timestamp).toDate()
              : (json['createdAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                  : DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()))
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] is Timestamp
              ? (json['updatedAt'] as Timestamp).toDate()
              : (json['updatedAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
                  : DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()))
          : DateTime.now(),
      isTimerBased: json['isTimerBased'] ?? isTimerBased,
      videoUrl: json['videoUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'instructions': instructions,
      'muscleGroups': muscleGroups,
      'primaryMuscleGroup': primaryMuscleGroup,
      'secondaryMuscleGroups': secondaryMuscleGroups,
      'difficulty': difficulty,
      'imageUrl': imageUrl,
      'caloriesPerMinute': caloriesPerMinute,
      'isBodyweight': isBodyweight,
      'equipment': equipment,
      'tags': tags,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'isTimerBased': isTimerBased,
      'videoUrl': videoUrl,
    };
  }
}

class WorkoutExercise {
  final String exerciseId;
  final int sets;
  final int reps;
  final int duration; // in seconds
  final int restTime; // in seconds
  final Map<String, dynamic> notes;

  WorkoutExercise({
    required this.exerciseId,
    this.sets = 1,
    this.reps = 10,
    this.duration = 0,
    this.restTime = 60,
    this.notes = const {},
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      exerciseId: json['exerciseId'] ?? '',
      sets: json['sets'] ?? 1,
      reps: json['reps'] ?? 10,
      duration: json['duration'] ?? 0,
      restTime: json['restTime'] ?? 60,
      notes: Map<String, dynamic>.from(json['notes'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exerciseId': exerciseId,
      'sets': sets,
      'reps': reps,
      'duration': duration,
      'restTime': restTime,
      'notes': notes,
    };
  }
}