import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String name;
  final String email;
  final DateTime createdAt;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> stats;

  UserModel({
    required this.uid,
    required this.name,
    required this.email,
    required this.createdAt,
    this.preferences = const {},
    this.stats = const {},
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is Timestamp
              ? (json['createdAt'] as Timestamp).toDate()
              : (json['createdAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                  : DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()))
          : DateTime.now(),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
      stats: Map<String, dynamic>.from(json['stats'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'preferences': preferences,
      'stats': stats,
    };
  }

  // Convert to JSON string for caching
  String toJsonString() {
    return jsonEncode(toJson());
  }

  // Create from JSON string
  factory UserModel.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return UserModel.fromJson(json);
  }

  UserModel copyWith({
    String? uid,
    String? name,
    String? email,
    DateTime? createdAt,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? stats,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
    );
  }
}