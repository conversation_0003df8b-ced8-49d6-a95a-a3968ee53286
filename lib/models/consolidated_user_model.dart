import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

// Enums
enum Gender { male, female, other, preferNotToSay }
enum WorkoutEnvironment { largeGym, smallGym, homeBasic, homeNoEquipment }
enum FitnessGoalType {
  notReady,
  sportSpecific,
  buildMuscle,
  calisthenics,
  weightLoss,
  healthOptimization,
  cardioBunny,
  increaseStrength,
  increaseStamina,
}

// Fitness Goal class
class FitnessGoal {
  final FitnessGoalType type;
  final int priority;
  final String? sportActivity;
  final DateTime selectedAt;

  FitnessGoal({
    required this.type,
    required this.priority,
    this.sportActivity,
    required this.selectedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'priority': priority,
      'sportActivity': sportActivity,
      'selectedAt': selectedAt.millisecondsSinceEpoch,
    };
  }

  factory FitnessGoal.fromJson(Map<String, dynamic> json) {
    // Handle priority as either int or String
    int parsePriority(dynamic value) {
      if (value == null) return 0;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? 0;
      return 0;
    }
    
    // Handle selectedAt as either int or String
    DateTime parseSelectedAt(dynamic value) {
      if (value == null) return DateTime.now();
      if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
      if (value is String) {
        final parsed = int.tryParse(value);
        if (parsed != null) return DateTime.fromMillisecondsSinceEpoch(parsed);
        final dateTime = DateTime.tryParse(value);
        if (dateTime != null) return dateTime;
      }
      return DateTime.now();
    }
    
    return FitnessGoal(
      type: FitnessGoalType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type']?.toString(),
        orElse: () => FitnessGoalType.notReady,
      ),
      priority: parsePriority(json['priority']),
      sportActivity: json['sportActivity']?.toString(),
      selectedAt: parseSelectedAt(json['selectedAt']),
    );
  }
}

// Personal Information
class PersonalInfo {
  final String name;
  final Gender? gender;
  final DateTime? dateOfBirth;
  final double? height; // in cm for metric, inches for imperial
  final double? weight; // in kg for metric, lbs for imperial
  final String preferredUnits; // 'metric' or 'imperial'

  PersonalInfo({
    required this.name,
    this.gender,
    this.dateOfBirth,
    this.height,
    this.weight,
    this.preferredUnits = 'metric',
  });

  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'gender': gender?.toString().split('.').last,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'height': height,
      'weight': weight,
      'preferredUnits': preferredUnits,
    };
  }

  factory PersonalInfo.fromJson(Map<String, dynamic> json) {
    return PersonalInfo(
      name: json['name']?.toString() ?? '',
      gender: json['gender'] != null 
          ? Gender.values.firstWhere(
              (e) => e.toString().split('.').last == json['gender']?.toString(),
              orElse: () => Gender.preferNotToSay,
            )
          : null,
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.tryParse(json['dateOfBirth']?.toString() ?? '') 
          : null,
      height: json['height']?.toDouble(),
      weight: json['weight']?.toDouble(),
      preferredUnits: json['preferredUnits']?.toString() ?? 'metric',
    );
  }

  PersonalInfo copyWith({
    String? name,
    Gender? gender,
    DateTime? dateOfBirth,
    double? height,
    double? weight,
    String? preferredUnits,
  }) {
    return PersonalInfo(
      name: name ?? this.name,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      preferredUnits: preferredUnits ?? this.preferredUnits,
    );
  }
}

// Fitness Profile
class FitnessProfile {
  final List<FitnessGoal> goals;
  final double cardioLevel; // 0.0 to 1.0
  final double weightliftingLevel; // 0.0 to 1.0
  final String? exercisesToAvoid;
  final String? personalCoachNotes;

  FitnessProfile({
    this.goals = const [],
    this.cardioLevel = 0.0,
    this.weightliftingLevel = 0.0,
    this.exercisesToAvoid,
    this.personalCoachNotes,
  });

  Map<String, dynamic> toJson() {
    return {
      'goals': goals.map((g) => g.toJson()).toList(),
      'cardioLevel': cardioLevel,
      'weightliftingLevel': weightliftingLevel,
      'exercisesToAvoid': exercisesToAvoid,
      'personalCoachNotes': personalCoachNotes,
    };
  }

  factory FitnessProfile.fromJson(Map<String, dynamic> json) {
    return FitnessProfile(
      goals: (json['goals'] as List<dynamic>?)
              ?.map((g) => FitnessGoal.fromJson(g))
              .toList() ??
          [],
      cardioLevel: (json['cardioLevel'] ?? 0.0).toDouble(),
      weightliftingLevel: (json['weightliftingLevel'] ?? 0.0).toDouble(),
      exercisesToAvoid: json['exercisesToAvoid']?.toString(),
      personalCoachNotes: json['personalCoachNotes']?.toString(),
    );
  }

  FitnessProfile copyWith({
    List<FitnessGoal>? goals,
    double? cardioLevel,
    double? weightliftingLevel,
    String? exercisesToAvoid,
    String? personalCoachNotes,
  }) {
    return FitnessProfile(
      goals: goals ?? this.goals,
      cardioLevel: cardioLevel ?? this.cardioLevel,
      weightliftingLevel: weightliftingLevel ?? this.weightliftingLevel,
      exercisesToAvoid: exercisesToAvoid ?? this.exercisesToAvoid,
      personalCoachNotes: personalCoachNotes ?? this.personalCoachNotes,
    );
  }
}

// Workout Preferences
class WorkoutPreferences {
  final List<WorkoutEnvironment> environments;
  final int workoutsPerWeek;
  final int? workoutDurationMinutes;
  final String? additionalNotes;

  WorkoutPreferences({
    this.environments = const [WorkoutEnvironment.homeNoEquipment],
    this.workoutsPerWeek = 3,
    this.workoutDurationMinutes,
    this.additionalNotes,
  });

  Map<String, dynamic> toJson() {
    return {
      'environments': environments.map((e) => e.toString().split('.').last).toList(),
      'workoutsPerWeek': workoutsPerWeek,
      'workoutDurationMinutes': workoutDurationMinutes,
      'additionalNotes': additionalNotes,
    };
  }

  factory WorkoutPreferences.fromJson(Map<String, dynamic> json) {
    return WorkoutPreferences(
      environments: (json['environments'] as List<dynamic>?)
              ?.map((e) => WorkoutEnvironment.values.firstWhere(
                    (env) => env.toString().split('.').last == e?.toString(),
                    orElse: () => WorkoutEnvironment.homeNoEquipment,
                  ))
              .toList() ??
          [WorkoutEnvironment.homeNoEquipment],
      workoutsPerWeek: json['workoutsPerWeek'] is int ? json['workoutsPerWeek'] : (int.tryParse(json['workoutsPerWeek']?.toString() ?? '') ?? 3),
      workoutDurationMinutes: json['workoutDurationMinutes'] is int ? json['workoutDurationMinutes'] : (json['workoutDurationMinutes'] != null ? int.tryParse(json['workoutDurationMinutes'].toString()) : null),
      additionalNotes: json['additionalNotes']?.toString(),
    );
  }

  WorkoutPreferences copyWith({
    List<WorkoutEnvironment>? environments,
    int? workoutsPerWeek,
    int? workoutDurationMinutes,
    String? additionalNotes,
  }) {
    return WorkoutPreferences(
      environments: environments ?? this.environments,
      workoutsPerWeek: workoutsPerWeek ?? this.workoutsPerWeek,
      workoutDurationMinutes: workoutDurationMinutes ?? this.workoutDurationMinutes,
      additionalNotes: additionalNotes ?? this.additionalNotes,
    );
  }
}

// User Statistics
class UserStats {
  final int totalWorkouts;
  final int totalMinutes;
  final int weeklyGoal; // minutes per week
  final int currentStreak;
  final double? totalCaloriesBurned;
  final DateTime? lastWorkoutDate;
  final Map<String, dynamic> monthlyStats; // For tracking monthly progress

  UserStats({
    this.totalWorkouts = 0,
    this.totalMinutes = 0,
    this.weeklyGoal = 150,
    this.currentStreak = 0,
    this.totalCaloriesBurned,
    this.lastWorkoutDate,
    this.monthlyStats = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'totalWorkouts': totalWorkouts,
      'totalMinutes': totalMinutes,
      'weeklyGoal': weeklyGoal,
      'currentStreak': currentStreak,
      'totalCaloriesBurned': totalCaloriesBurned,
      'lastWorkoutDate': lastWorkoutDate?.toIso8601String(),
      'monthlyStats': monthlyStats,
    };
  }

  factory UserStats.fromJson(Map<String, dynamic> json) {
    // Helper to parse int from either int or String
    int parseIntField(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? defaultValue;
      if (value is double) return value.toInt();
      return defaultValue;
    }
    
    // Helper to parse double from either double, int or String
    double? parseDoubleField(dynamic value) {
      if (value == null) return null;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value);
      return null;
    }
    
    return UserStats(
      totalWorkouts: parseIntField(json['totalWorkouts'], 0),
      totalMinutes: parseIntField(json['totalMinutes'], 0),
      weeklyGoal: parseIntField(json['weeklyGoal'], 150),
      currentStreak: parseIntField(json['currentStreak'], 0),
      totalCaloriesBurned: parseDoubleField(json['totalCaloriesBurned'] ?? json['totalCalories']),
      lastWorkoutDate: json['lastWorkoutDate'] != null 
          ? (json['lastWorkoutDate'] is int 
              ? DateTime.fromMillisecondsSinceEpoch(json['lastWorkoutDate'])
              : (json['lastWorkoutDate'] is String
                  ? DateTime.tryParse(json['lastWorkoutDate'])
                  : null))
          : null,
      monthlyStats: Map<String, dynamic>.from(json['monthlyStats'] ?? {}),
    );
  }

  UserStats copyWith({
    int? totalWorkouts,
    int? totalMinutes,
    int? weeklyGoal,
    int? currentStreak,
    double? totalCaloriesBurned,
    DateTime? lastWorkoutDate,
    Map<String, dynamic>? monthlyStats,
  }) {
    return UserStats(
      totalWorkouts: totalWorkouts ?? this.totalWorkouts,
      totalMinutes: totalMinutes ?? this.totalMinutes,
      weeklyGoal: weeklyGoal ?? this.weeklyGoal,
      currentStreak: currentStreak ?? this.currentStreak,
      totalCaloriesBurned: totalCaloriesBurned ?? this.totalCaloriesBurned,
      lastWorkoutDate: lastWorkoutDate ?? this.lastWorkoutDate,
      monthlyStats: monthlyStats ?? this.monthlyStats,
    );
  }
}

// User Preferences
class UserPreferences {
  final String units; // 'metric' or 'imperial'
  final bool notifications;
  final String theme; // 'light', 'dark', 'system'
  final bool onboardingComplete;
  final bool comprehensiveOnboardingComplete;
  final bool fitnessGoalsSet;
  final String? language;
  final Map<String, dynamic> appSettings;

  UserPreferences({
    this.units = 'metric',
    this.notifications = true,
    this.theme = 'system',
    this.onboardingComplete = false,
    this.comprehensiveOnboardingComplete = false,
    this.fitnessGoalsSet = false,
    this.language,
    this.appSettings = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'units': units,
      'notifications': notifications,
      'theme': theme,
      'onboardingComplete': onboardingComplete,
      'comprehensiveOnboardingComplete': comprehensiveOnboardingComplete,
      'fitnessGoalsSet': fitnessGoalsSet,
      'language': language,
      'appSettings': appSettings,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      units: json['units']?.toString() ?? 'metric',
      notifications: json['notifications'] ?? true,
      theme: json['theme']?.toString() ?? 'system',
      onboardingComplete: json['onboardingComplete'] ?? false,
      comprehensiveOnboardingComplete: json['comprehensiveOnboardingComplete'] ?? false,
      fitnessGoalsSet: json['fitnessGoalsSet'] ?? false,
      language: json['language']?.toString(),
      appSettings: Map<String, dynamic>.from(json['appSettings'] ?? {}),
    );
  }

  UserPreferences copyWith({
    String? units,
    bool? notifications,
    String? theme,
    bool? onboardingComplete,
    bool? comprehensiveOnboardingComplete,
    bool? fitnessGoalsSet,
    String? language,
    Map<String, dynamic>? appSettings,
  }) {
    return UserPreferences(
      units: units ?? this.units,
      notifications: notifications ?? this.notifications,
      theme: theme ?? this.theme,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      comprehensiveOnboardingComplete: comprehensiveOnboardingComplete ?? this.comprehensiveOnboardingComplete,
      fitnessGoalsSet: fitnessGoalsSet ?? this.fitnessGoalsSet,
      language: language ?? this.language,
      appSettings: appSettings ?? this.appSettings,
    );
  }
}

// Main Consolidated User Model
class ConsolidatedUserModel {
  final String uid;
  final String email;
  final String? displayName;
  final String? photoURL;
  final PersonalInfo personalInfo;
  final FitnessProfile fitnessProfile;
  final WorkoutPreferences workoutPreferences;
  final UserStats stats;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  ConsolidatedUserModel({
    required this.uid,
    required this.email,
    this.displayName,
    this.photoURL,
    required this.personalInfo,
    required this.fitnessProfile,
    required this.workoutPreferences,
    required this.stats,
    required this.preferences,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'personalInfo': personalInfo.toJson(),
      'fitnessProfile': fitnessProfile.toJson(),
      'workoutPreferences': workoutPreferences.toJson(),
      'stats': stats.toJson(),
      'preferences': preferences.toJson(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory ConsolidatedUserModel.fromJson(Map<String, dynamic> json) {
    // Handle mixed structure where some fields might be at root level
    Map<String, dynamic> personalInfoData = {};
    if (json['personalInfo'] != null && json['personalInfo'] is Map) {
      personalInfoData = Map<String, dynamic>.from(json['personalInfo']);
    }
    
    // If personalInfo fields are at root level, merge them
    if (json['name'] != null) personalInfoData['name'] = json['name'];
    if (json['gender'] != null) personalInfoData['gender'] = json['gender'];
    if (json['dateOfBirth'] != null) personalInfoData['dateOfBirth'] = json['dateOfBirth'];
    if (json['height'] != null) personalInfoData['height'] = json['height'];
    if (json['weight'] != null) personalInfoData['weight'] = json['weight'];
    if (json['age'] != null && personalInfoData['dateOfBirth'] == null) {
      // Convert age to approximate date of birth if needed
      int? age;
      if (json['age'] is int) {
        age = json['age'];
      } else if (json['age'] is String) {
        age = int.tryParse(json['age']);
      }
      if (age != null) {
        personalInfoData['dateOfBirth'] = DateTime.now().subtract(Duration(days: age * 365)).toIso8601String();
      }
    }
    
    return ConsolidatedUserModel(
      uid: json['uid']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      displayName: json['displayName']?.toString(),
      photoURL: json['photoURL']?.toString(),
      personalInfo: PersonalInfo.fromJson(personalInfoData),
      fitnessProfile: FitnessProfile.fromJson(json['fitnessProfile'] ?? {}),
      workoutPreferences: WorkoutPreferences.fromJson(json['workoutPreferences'] ?? {}),
      stats: UserStats.fromJson(json['stats'] ?? {}),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] is Timestamp
              ? (json['createdAt'] as Timestamp).toDate()
              : (json['createdAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                  : DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()))
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] is Timestamp
              ? (json['updatedAt'] as Timestamp).toDate()
              : (json['updatedAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
                  : DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()))
          : DateTime.now(),
    );
  }

  // Convert to JSON string for caching
  String toJsonString() {
    return jsonEncode(toJson());
  }

  // Create from JSON string
  factory ConsolidatedUserModel.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return ConsolidatedUserModel.fromJson(json);
  }

  ConsolidatedUserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    PersonalInfo? personalInfo,
    FitnessProfile? fitnessProfile,
    WorkoutPreferences? workoutPreferences,
    UserStats? stats,
    UserPreferences? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ConsolidatedUserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      personalInfo: personalInfo ?? this.personalInfo,
      fitnessProfile: fitnessProfile ?? this.fitnessProfile,
      workoutPreferences: workoutPreferences ?? this.workoutPreferences,
      stats: stats ?? this.stats,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Helper methods for backward compatibility
  String get name => personalInfo.name;
  int? get age => personalInfo.age;
  Gender? get gender => personalInfo.gender;
  double? get height => personalInfo.height;
  double? get weight => personalInfo.weight;
  bool get onboardingCompleted => preferences.onboardingComplete;
  bool get comprehensiveOnboardingCompleted => preferences.comprehensiveOnboardingComplete;

  // Migration helper - create from old UserModel
  factory ConsolidatedUserModel.fromLegacyUserModel(Map<String, dynamic> userData) {
    final now = DateTime.now();
    
    return ConsolidatedUserModel(
      uid: userData['uid']?.toString() ?? '',
      email: userData['email']?.toString() ?? '',
      displayName: userData['displayName']?.toString() ?? userData['name']?.toString(),
      photoURL: userData['photoURL']?.toString(),
      personalInfo: PersonalInfo(
        name: userData['name']?.toString() ?? userData['displayName']?.toString() ?? '',
        gender: userData['gender'] != null 
            ? Gender.values.firstWhere(
                (e) => e.toString().split('.').last == userData['gender']?.toString(),
                orElse: () => Gender.preferNotToSay,
              )
            : null,
        dateOfBirth: userData['dateOfBirth'] != null 
            ? DateTime.tryParse(userData['dateOfBirth']?.toString() ?? '') 
            : null,
        height: userData['height']?.toDouble(),
        weight: userData['weight']?.toDouble(),
        preferredUnits: userData['preferredUnits']?.toString() ?? 'metric',
      ),
      fitnessProfile: FitnessProfile.fromJson(userData['fitnessProfile'] ?? {}),
      workoutPreferences: WorkoutPreferences.fromJson(userData['workoutPreferences'] ?? {}),
      stats: UserStats.fromJson(userData['stats'] ?? {}),
      preferences: UserPreferences.fromJson(userData['preferences'] ?? {}),
      createdAt: userData['createdAt'] != null
          ? (userData['createdAt'] is int
              ? DateTime.fromMillisecondsSinceEpoch(userData['createdAt'])
              : DateTime.tryParse(userData['createdAt'].toString()) ?? now)
          : now,
      updatedAt: userData['updatedAt'] != null
          ? (userData['updatedAt'] is int
              ? DateTime.fromMillisecondsSinceEpoch(userData['updatedAt'])
              : DateTime.tryParse(userData['updatedAt'].toString()) ?? now)
          : now,
    );
  }
} 