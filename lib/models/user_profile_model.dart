

enum Gender { male, female, other, preferNotToSay }

enum WorkoutEnvironment { largeGym, smallGym, homeBasic, homeNoEquipment }

class UserProfileModel {
  final String userId;
  final String name;
  final Gender? gender;
  final int? age;
  final double? heightFeet; // in feet (e.g., 5.5 for 5'6")
  final double? weightLbs; // in pounds
  final DateTime createdAt;
  final DateTime? updatedAt;

  UserProfileModel({
    required this.userId,
    required this.name,
    this.gender,
    this.age,
    this.heightFeet,
    this.weightLbs,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'name': name,
      'gender': gender?.toString().split('.').last,
      'age': age,
      'heightFeet': heightFeet,
      'weightLbs': weightLbs,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      userId: json['userId'] ?? '',
      name: json['name'] ?? '',
      gender: json['gender'] != null 
          ? Gender.values.firstWhere(
              (e) => e.toString().split('.').last == json['gender'],
              orElse: () => Gender.preferNotToSay,
            )
          : null,
      age: json['age'],
      heightFeet: json['heightFeet']?.toDouble(),
      weightLbs: json['weightLbs']?.toDouble(),
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
    );
  }

  UserProfileModel copyWith({
    String? userId,
    String? name,
    Gender? gender,
    int? age,
    double? heightFeet,
    double? weightLbs,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfileModel(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      heightFeet: heightFeet ?? this.heightFeet,
      weightLbs: weightLbs ?? this.weightLbs,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class FitnessLevelModel {
  final String userId;
  final double cardioLevel; // 0.0 to 1.0
  final double weightliftingLevel; // 0.0 to 1.0
  final String? exercisesToAvoid;
  final DateTime createdAt;

  FitnessLevelModel({
    required this.userId,
    required this.cardioLevel,
    required this.weightliftingLevel,
    this.exercisesToAvoid,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'cardioLevel': cardioLevel,
      'weightliftingLevel': weightliftingLevel,
      'exercisesToAvoid': exercisesToAvoid,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory FitnessLevelModel.fromJson(Map<String, dynamic> json) {
    return FitnessLevelModel(
      userId: json['userId'] ?? '',
      cardioLevel: (json['cardioLevel'] ?? 0.0).toDouble(),
      weightliftingLevel: (json['weightliftingLevel'] ?? 0.0).toDouble(),
      exercisesToAvoid: json['exercisesToAvoid'],
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : DateTime.now(),
    );
  }
}

class WorkoutPreferencesModel {
  final String userId;
  final List<WorkoutEnvironment> environments;
  final int workoutsPerWeek;
  final int? workoutDurationMinutes; // null means "optimize for me"
  final String? additionalNotes;
  final DateTime createdAt;

  WorkoutPreferencesModel({
    required this.userId,
    required this.environments,
    required this.workoutsPerWeek,
    this.workoutDurationMinutes,
    this.additionalNotes,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'environments': environments.map((e) => e.toString().split('.').last).toList(),
      'workoutsPerWeek': workoutsPerWeek,
      'workoutDurationMinutes': workoutDurationMinutes,
      'additionalNotes': additionalNotes,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory WorkoutPreferencesModel.fromJson(Map<String, dynamic> json) {
    return WorkoutPreferencesModel(
      userId: json['userId'] ?? '',
      environments: (json['environments'] as List<dynamic>?)
              ?.map((e) => WorkoutEnvironment.values.firstWhere(
                    (env) => env.toString().split('.').last == e,
                    orElse: () => WorkoutEnvironment.homeNoEquipment,
                  ))
              .toList() ??
          [],
      workoutsPerWeek: json['workoutsPerWeek'] ?? 3,
      workoutDurationMinutes: json['workoutDurationMinutes'],
      additionalNotes: json['additionalNotes'],
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : DateTime.now(),
    );
  }
}

// Updated fitness goals model
enum FitnessGoalType {
  healthOptimization,
  sportSpecific,
  buildMuscle,
  weightLoss,
  increaseStamina,
  increaseStrength,
}

class FitnessGoal {
  final FitnessGoalType type;
  final int priority;
  final String? sportActivity; // For sport-specific goals
  final DateTime selectedAt;

  FitnessGoal({
    required this.type,
    required this.priority,
    this.sportActivity,
    required this.selectedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'priority': priority,
      'sportActivity': sportActivity,
      'selectedAt': selectedAt.millisecondsSinceEpoch,
    };
  }

  factory FitnessGoal.fromJson(Map<String, dynamic> json) {
    return FitnessGoal(
      type: FitnessGoalType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => FitnessGoalType.healthOptimization,
      ),
      priority: json['priority'] ?? 0,
      sportActivity: json['sportActivity'],
      selectedAt: json['selectedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['selectedAt'])
          : DateTime.now(),
    );
  }
}

class ComprehensiveOnboardingModel {
  final UserProfileModel profile;
  final List<FitnessGoal> fitnessGoals;
  final String? personalCoachNotes;
  final FitnessLevelModel fitnessLevel;
  final WorkoutPreferencesModel workoutPreferences;
  final bool isComplete;
  final DateTime completedAt;

  ComprehensiveOnboardingModel({
    required this.profile,
    required this.fitnessGoals,
    this.personalCoachNotes,
    required this.fitnessLevel,
    required this.workoutPreferences,
    this.isComplete = false,
    required this.completedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'profile': profile.toJson(),
      'fitnessGoals': fitnessGoals.map((g) => g.toJson()).toList(),
      'personalCoachNotes': personalCoachNotes,
      'fitnessLevel': fitnessLevel.toJson(),
      'workoutPreferences': workoutPreferences.toJson(),
      'isComplete': isComplete,
      'completedAt': completedAt.millisecondsSinceEpoch,
    };
  }

  factory ComprehensiveOnboardingModel.fromJson(Map<String, dynamic> json) {
    return ComprehensiveOnboardingModel(
      profile: UserProfileModel.fromJson(json['profile']),
      fitnessGoals: (json['fitnessGoals'] as List<dynamic>?)
              ?.map((g) => FitnessGoal.fromJson(g))
              .toList() ??
          [],
      personalCoachNotes: json['personalCoachNotes'],
      fitnessLevel: FitnessLevelModel.fromJson(json['fitnessLevel']),
      workoutPreferences: WorkoutPreferencesModel.fromJson(json['workoutPreferences']),
      isComplete: json['isComplete'] ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
          : DateTime.now(),
    );
  }
}

// Helper class for goal information
class FitnessGoalInfo {
  final FitnessGoalType type;
  final String title;
  final String description;
  final String? recommendation;
  final bool requiresInput;

  const FitnessGoalInfo({
    required this.type,
    required this.title,
    required this.description,
    this.recommendation,
    this.requiresInput = false,
  });

  static const List<FitnessGoalInfo> allGoals = [
    FitnessGoalInfo(
      type: FitnessGoalType.healthOptimization,
      title: "Optimize my Health and Fitness",
      description: "You'll follow a balanced routine that includes exercises to increase and maintain a healthy muscle mass, improve cardiovascular fitness, and support weight maintenance with short at home flexibility/Stretch during rest days. This plan is perfect for promoting longevity and overall well-being and fitness.",
      recommendation: "Everyday Users",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.sportSpecific,
      title: "Training for a Specific Sport or Activity",
      description: "What is your sport or activity of choice? Example: Marathon, Volleyball, Tactical Readiness.",
      requiresInput: true,
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.buildMuscle,
      title: "Build Muscle Mass and Size",
      description: "You'll train with a mix of volume and intensity, focusing on both compound movements and isolated exercises when necessary. This plan is designed to maximize muscle growth and help you build size like never before.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.weightLoss,
      title: "Weight Loss and Management",
      description: "You'll start with resistance training to build muscle mass and gradually incorporate endurance exercises to support weight management and cardiovascular health. Over time, this plan will transition into a health optimization routine tailored to your needs.",
      recommendation: "Individuals with minimal movement and/or experience with being overweight or obese.",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.increaseStamina,
      title: "Increase Stamina",
      description: "Designed for those who love endurance exercises like running, cycling, or swimming, this plan focuses on building stamina and improving overall cardiovascular fitness.",
      recommendation: "Endurance Focus",
    ),
    FitnessGoalInfo(
      type: FitnessGoalType.increaseStrength,
      title: "Increase Strength",
      description: "Perfect for individuals focused on maximizing strength and powerlifting, utilizing low-rep, high-weight training with longer rest periods to build power and lift heavier weights.",
    ),
  ];
} 