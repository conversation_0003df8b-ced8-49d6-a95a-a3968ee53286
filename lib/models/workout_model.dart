import 'exercise_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class WorkoutPlanModel {
  final String id;
  final String name;
  final String description;
  final List<WorkoutExercise> exercises;
  final int duration; // estimated duration in minutes
  final String difficulty;
  final String category;
  final String imageUrl;
  final bool isCustom;
  final String? createdBy;
  final DateTime? createdAt;
  final bool isPublic;

  WorkoutPlanModel({
    required this.id,
    required this.name,
    required this.description,
    required this.exercises,
    required this.duration,
    required this.difficulty,
    required this.category,
    required this.imageUrl,
    this.isCustom = false,
    this.createdBy,
    this.createdAt,
    this.isPublic = false,
  });

  factory WorkoutPlanModel.fromJson(Map<String, dynamic> json) {
    return WorkoutPlanModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      exercises: (json['exercises'] as List<dynamic>?)
              ?.map((e) => WorkoutExercise.fromJson(e))
              .toList() ??
          [],
      duration: json['duration'] ?? 30,
      difficulty: json['difficulty'] ?? 'beginner',
      category: json['category'] ?? 'general',
      imageUrl: json['imageUrl'] ?? '',
      isCustom: json['isCustom'] ?? false,
      createdBy: json['createdBy'],
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] is Timestamp
              ? (json['createdAt'] as Timestamp).toDate()
              : (json['createdAt'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
                  : DateTime.tryParse(json['createdAt'].toString())))
          : null,
      isPublic: json['isPublic'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'name': name,
      'description': description,
      'exercises': exercises.map((e) => e.toJson()).toList(),
      'duration': duration,
      'difficulty': difficulty,
      'category': category,
      'imageUrl': imageUrl,
      'isCustom': isCustom,
      'createdBy': createdBy,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'isPublic': isPublic,
    };
    
    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }
    
    return json;
  }
}

class WorkoutSessionModel {
  final String id;
  final String userId;
  final String workoutPlanId;
  final DateTime startTime;
  final DateTime? endTime;
  final List<CompletedExercise> completedExercises;
  final String notes;
  final bool isCompleted;
  final int? totalCaloriesBurned;

  WorkoutSessionModel({
    required this.id,
    required this.userId,
    required this.workoutPlanId,
    required this.startTime,
    this.endTime,
    this.completedExercises = const [],
    this.notes = '',
    this.isCompleted = false,
    this.totalCaloriesBurned,
  });

  factory WorkoutSessionModel.fromJson(Map<String, dynamic> json) {
    return WorkoutSessionModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      workoutPlanId: json['workoutPlanId'] ?? '',
      startTime: DateTime.fromMillisecondsSinceEpoch(json['startTime'] ?? 0),
      endTime: json['endTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['endTime'])
          : null,
      completedExercises: (json['completedExercises'] as List<dynamic>?)
              ?.map((e) => CompletedExercise.fromJson(e))
              .toList() ??
          [],
      notes: json['notes'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      totalCaloriesBurned: json['totalCaloriesBurned'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'userId': userId,
      'workoutPlanId': workoutPlanId,
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime?.millisecondsSinceEpoch,
      'completedExercises': completedExercises.map((e) => e.toJson()).toList(),
      'notes': notes,
      'isCompleted': isCompleted,
      'totalCaloriesBurned': totalCaloriesBurned,
    };
    
    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }
    
    return json;
  }

  Duration get totalDuration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return Duration.zero;
  }
}

class CompletedExercise {
  final String exerciseId;
  final int completedSets;
  final List<ExerciseSet> sets;
  final DateTime completedAt;

  CompletedExercise({
    required this.exerciseId,
    required this.completedSets,
    required this.sets,
    required this.completedAt,
  });

  factory CompletedExercise.fromJson(Map<String, dynamic> json) {
    return CompletedExercise(
      exerciseId: json['exerciseId'] ?? '',
      completedSets: json['completedSets'] ?? 0,
      sets: (json['sets'] as List<dynamic>?)
              ?.map((e) => ExerciseSet.fromJson(e))
              .toList() ??
          [],
      completedAt: DateTime.fromMillisecondsSinceEpoch(json['completedAt'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exerciseId': exerciseId,
      'completedSets': completedSets,
      'sets': sets.map((e) => e.toJson()).toList(),
      'completedAt': completedAt.millisecondsSinceEpoch,
    };
  }
}

class ExerciseSet {
  final int reps;
  final double weight;
  final int duration; // in seconds

  ExerciseSet({
    required this.reps,
    required this.weight,
    required this.duration,
  });

  factory ExerciseSet.fromJson(Map<String, dynamic> json) {
    return ExerciseSet(
      reps: json['reps'] ?? 0,
      weight: (json['weight'] ?? 0).toDouble(),
      duration: json['duration'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reps': reps,
      'weight': weight,
      'duration': duration,
    };
  }
}