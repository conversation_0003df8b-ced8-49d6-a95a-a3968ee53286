// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for android - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDa1rzUXjOAA9Eq-K3jz7n4X6bKpzGBHUc',
    appId: '1:745521622245:web:effc5edd6209b8a99c780c',
    messagingSenderId: '745521622245',
    projectId: 'po2vf2ae7tal9invaj7jkf4a06hsac',
    authDomain: 'po2vf2ae7tal9invaj7jkf4a06hsac.firebaseapp.com',
    storageBucket: 'po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCWq1nvA-BWpCIBm-NwRYXHetnOK4n1_as',
    appId: '1:745521622245:ios:cd22bc3d02ab2db59c780c',
    messagingSenderId: '745521622245',
    projectId: 'po2vf2ae7tal9invaj7jkf4a06hsac',
    storageBucket: 'po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app',
    iosBundleId: 'com.abenezernuro.agenticfit',
  );
}