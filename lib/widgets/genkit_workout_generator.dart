import 'package:flutter/material.dart';
import '../services/genkit_workout_service.dart';
import '../models/workout_model.dart';

class GenKitWorkoutGenerator extends StatefulWidget {
  const GenKitWorkoutGenerator({super.key});

  @override
  State<GenKitWorkoutGenerator> createState() => _GenKitWorkoutGeneratorState();
}

class _GenKitWorkoutGeneratorState extends State<GenKitWorkoutGenerator> {
  final GenKitWorkoutService _genKitService = GenKitWorkoutService();
  bool _isGenerating = false;
  Map<String, dynamic>? _generatedWorkout;
  String? _error;

  String _selectedWorkoutType = 'full_body';
  int _selectedDuration = 30;
  String _selectedFitnessLevel = 'intermediate';

  final List<String> _workoutTypes = [
    'full_body',
    'upper_body',
    'lower_body',
    'cardio',
    'strength',
    'push',
    'pull',
    'legs',
    'abs',
  ];

  final List<int> _durations = [15, 20, 30, 45, 60, 90];
  final List<String> _fitnessLevels = ['beginner', 'intermediate', 'advanced'];

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.psychology, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'AI Workout Generator',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Generate personalized workouts using our advanced AI',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            
            // Workout Type Selection
            _buildDropdown(
              'Workout Type',
              _selectedWorkoutType,
              _workoutTypes.map((type) => DropdownMenuItem(
                value: type,
                child: Text(type.replaceAll('_', ' ').toUpperCase()),
              )).toList(),
              (value) => setState(() => _selectedWorkoutType = value!),
            ),
            
            const SizedBox(height: 16),
            
            // Duration Selection
            _buildDropdown(
              'Duration (minutes)',
              _selectedDuration,
              _durations.map((duration) => DropdownMenuItem(
                value: duration,
                child: Text('$duration minutes'),
              )).toList(),
              (value) => setState(() => _selectedDuration = value!),
            ),
            
            const SizedBox(height: 16),
            
            // Fitness Level Selection
            _buildDropdown(
              'Fitness Level',
              _selectedFitnessLevel,
              _fitnessLevels.map((level) => DropdownMenuItem(
                value: level,
                child: Text(level.toUpperCase()),
              )).toList(),
              (value) => setState(() => _selectedFitnessLevel = value!),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _generateWorkout,
                    icon: _isGenerating 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.fitness_center),
                    label: Text(_isGenerating ? 'Generating...' : 'Generate Workout'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _isGenerating ? null : _generatePersonalizedWorkout,
                  icon: const Icon(Icons.person),
                  label: const Text('Use My Profile'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Test Connection Button
            TextButton.icon(
              onPressed: _testConnection,
              icon: const Icon(Icons.network_check),
              label: const Text('Test GenKit Connection'),
            ),
            
            // Error Display
            if (_error != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Generated Workout Display
            if (_generatedWorkout != null) ...[
              const SizedBox(height: 24),
              const Divider(),
              const SizedBox(height: 16),
              Text(
                'Generated Workout',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Workout Details:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_formatWorkoutResponse(_generatedWorkout!)),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDropdown<T>(
    String label,
    T value,
    List<DropdownMenuItem<T>> items,
    void Function(T?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Future<void> _generateWorkout() async {
    setState(() {
      _isGenerating = true;
      _error = null;
      _generatedWorkout = null;
    });

    try {
      final result = await _genKitService.generateWorkout(
        workoutType: _selectedWorkoutType,
        durationMinutes: _selectedDuration,
        fitnessLevel: _selectedFitnessLevel,
        equipment: ['bodyweight', 'dumbbells'],
        targetMuscleGroups: _getTargetMuscleGroups(_selectedWorkoutType),
      );

      if (result != null) {
        setState(() {
          _generatedWorkout = result;
        });
        _showSuccessSnackBar('Workout generated successfully!');
      } else {
        setState(() {
          _error = 'Failed to generate workout. Please try again.';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<void> _generatePersonalizedWorkout() async {
    setState(() {
      _isGenerating = true;
      _error = null;
      _generatedWorkout = null;
    });

    try {
      final result = await _genKitService.generatePersonalizedWorkout();

      if (result != null) {
        setState(() {
          _generatedWorkout = result;
        });
        _showSuccessSnackBar('Personalized workout generated!');
      } else {
        setState(() {
          _error = 'Failed to generate personalized workout. Make sure your profile is complete.';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<void> _testConnection() async {
    final isConnected = await _genKitService.testConnection();
    
    if (isConnected) {
      _showSuccessSnackBar('✅ GenKit function is accessible!');
    } else {
      _showErrorSnackBar('❌ Cannot connect to GenKit function');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  List<String> _getTargetMuscleGroups(String workoutType) {
    switch (workoutType.toLowerCase()) {
      case 'upper_body':
        return ['chest', 'back', 'shoulders', 'arms'];
      case 'lower_body':
        return ['quads', 'hamstrings', 'glutes', 'calves'];
      case 'push':
        return ['chest', 'shoulders', 'triceps'];
      case 'pull':
        return ['back', 'biceps'];
      case 'legs':
        return ['quads', 'hamstrings', 'glutes', 'calves'];
      case 'abs':
        return ['abs', 'core'];
      case 'cardio':
        return ['cardiovascular'];
      default:
        return ['full_body'];
    }
  }

  String _formatWorkoutResponse(Map<String, dynamic> workout) {
    final buffer = StringBuffer();
    
    workout.forEach((key, value) {
      if (value != null) {
        if (value is List) {
          buffer.writeln('$key: ${value.join(', ')}');
        } else if (value is Map) {
          buffer.writeln('$key:');
          value.forEach((subKey, subValue) {
            buffer.writeln('  $subKey: $subValue');
          });
        } else {
          buffer.writeln('$key: $value');
        }
      }
    });
    
    return buffer.toString();
  }
}