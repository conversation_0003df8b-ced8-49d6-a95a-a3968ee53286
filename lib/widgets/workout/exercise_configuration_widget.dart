import 'package:flutter/material.dart';
import '../../models/exercise_model.dart';

class ExerciseConfigurationWidget extends StatefulWidget {
  final ExerciseModel exercise;
  final WorkoutExercise? initialConfiguration;
  final Function(WorkoutExercise) onConfigurationChanged;

  const ExerciseConfigurationWidget({
    super.key,
    required this.exercise,
    this.initialConfiguration,
    required this.onConfigurationChanged,
  });

  @override
  State<ExerciseConfigurationWidget> createState() => _ExerciseConfigurationWidgetState();
}

class _ExerciseConfigurationWidgetState extends State<ExerciseConfigurationWidget>
    with SingleTickerProviderStateMixin {
  late int _sets;
  late int _reps;
  late int _duration;
  late int _restTime;
  late String _notes;
  bool _isTimeBased = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // Initialize with default or existing values
    final config = widget.initialConfiguration ?? WorkoutExercise(
      exerciseId: widget.exercise.id,
      sets: 3,
      reps: 12,
      duration: 0,
      restTime: 60,
    );

    _sets = config.sets;
    _reps = config.reps;
    _duration = config.duration > 0 ? config.duration : 30;
    _restTime = config.restTime;
    _notes = config.notes['workout'] ?? '';
    _isTimeBased = config.duration > 0;
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateConfiguration() {
    final workoutExercise = WorkoutExercise(
      exerciseId: widget.exercise.id,
      sets: _sets,
      reps: _isTimeBased ? 0 : _reps,
      duration: _isTimeBased ? _duration : 0,
      restTime: _restTime,
      notes: {
        'workout': _notes,
      },
    );
    
    widget.onConfigurationChanged(workoutExercise);
  }

  void _updateValue(String field, int delta, int min, int max) {
    setState(() {
      switch (field) {
        case 'sets':
          _sets = (_sets + delta).clamp(min, max);
          break;
        case 'reps':
          _reps = (_reps + delta).clamp(min, max);
          break;
        case 'duration':
          _duration = (_duration + delta).clamp(min, max);
          break;
        case 'rest':
          _restTime = (_restTime + delta).clamp(min, max);
          break;
      }
    });
    _updateConfiguration();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Header Card
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primary.withOpacity(0.2),
                          Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.fitness_center,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.exercise.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.exercise.primaryMuscleGroup,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Configuration Controls
            Row(
              children: [
                // Sets Control
                Expanded(
                  child: _buildCompactControl(
                    label: 'Sets',
                    value: _sets,
                    onDecrease: () => _updateValue('sets', -1, 1, 10),
                    onIncrease: () => _updateValue('sets', 1, 1, 10),
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 16),
                // Reps/Duration Control
                Expanded(
                  child: _isTimeBased
                      ? _buildCompactControl(
                          label: 'Time',
                          value: _duration,
                          unit: 's',
                          onDecrease: () => _updateValue('duration', -5, 10, 300),
                          onIncrease: () => _updateValue('duration', 5, 10, 300),
                          color: Theme.of(context).colorScheme.secondary,
                        )
                      : _buildCompactControl(
                          label: 'Reps',
                          value: _reps,
                          onDecrease: () => _updateValue('reps', -1, 1, 100),
                          onIncrease: () => _updateValue('reps', 1, 1, 100),
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Rest Time Control
            _buildCompactControl(
              label: 'Rest Time',
              value: _restTime,
              unit: 's',
              onDecrease: () => _updateValue('rest', -5, 0, 300),
              onIncrease: () => _updateValue('rest', 5, 0, 300),
              color: Theme.of(context).colorScheme.tertiary,
              fullWidth: true,
            ),
            
            const SizedBox(height: 20),
            
            // Time/Reps Toggle
            Container(
              height: 44,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if (_isTimeBased) {
                          setState(() => _isTimeBased = false);
                          _updateConfiguration();
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: !_isTimeBased 
                              ? Theme.of(context).colorScheme.surface
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: !_isTimeBased ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ] : null,
                        ),
                        child: Center(
                          child: Text(
                            'Reps',
                            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: !_isTimeBased 
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if (!_isTimeBased) {
                          setState(() => _isTimeBased = true);
                          _updateConfiguration();
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: _isTimeBased 
                              ? Theme.of(context).colorScheme.surface
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: _isTimeBased ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ] : null,
                        ),
                        child: Center(
                          child: Text(
                            'Time',
                            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: _isTimeBased 
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactControl({
    required String label,
    required int value,
    String? unit,
    required VoidCallback onDecrease,
    required VoidCallback onIncrease,
    required Color color,
    bool fullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: IconButton(
                  onPressed: onDecrease,
                  icon: const Icon(Icons.remove),
                  style: IconButton.styleFrom(
                    backgroundColor: color.withOpacity(0.1),
                    foregroundColor: color,
                    minimumSize: const Size(40, 40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: SizedBox(
                  width: fullWidth ? 120 : 60,
                  child: Text(
                    '$value${unit ?? ''}',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ),
              Flexible(
                child: IconButton(
                  onPressed: onIncrease,
                  icon: const Icon(Icons.add),
                  style: IconButton.styleFrom(
                    backgroundColor: color.withOpacity(0.1),
                    foregroundColor: color,
                    minimumSize: const Size(40, 40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}