import 'package:flutter/material.dart';

/// A widget that catches and handles Flutter framework errors gracefully
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(FlutterErrorDetails)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  FlutterErrorDetails? _errorDetails;

  @override
  void initState() {
    super.initState();
    
    // Set up error handler for this widget tree
    FlutterError.onError = (FlutterErrorDetails details) {
      // Check if this is a SystemContextMenu error that we want to handle
      if (details.exception.toString().contains('SystemContextMenu') ||
          details.exception.toString().contains('text input connection')) {
        setState(() {
          _errorDetails = details;
        });
        // Log the error but don't crash
        debugPrint('SystemContextMenu error handled: ${details.exception}');
        return;
      }
      
      // For other errors, use the default handler
      FlutterError.presentError(details);
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_errorDetails != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_errorDetails!);
      }
      
      // Default error handling - just show the child widget
      // SystemContextMenu errors are usually non-critical UI errors
      return widget.child;
    }
    
    return widget.child;
  }
}
