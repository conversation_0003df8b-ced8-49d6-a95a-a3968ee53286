import 'package:flutter/material.dart';
import '../models/exercise_model.dart';

class QuickConfigurationPanel extends StatefulWidget {
  final List<ExerciseModel> exercises;
  final Function(Map<String, WorkoutExercise>) onBulkConfiguration;

  const QuickConfigurationPanel({
    super.key,
    required this.exercises,
    required this.onBulkConfiguration,
  });

  @override
  State<QuickConfigurationPanel> createState() => _QuickConfigurationPanelState();
}

class _QuickConfigurationPanelState extends State<QuickConfigurationPanel> {
  int _sets = 3;
  int _reps = 12;
  int _rest = 60;
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isExpanded ? null : 0,
            child: _isExpanded ? _buildConfigurationControls() : null,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.tune,
                color: Theme.of(context).colorScheme.secondary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Configuration',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    'Apply settings to all ${widget.exercises.length} exercises',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.keyboard_arrow_down,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationControls() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildControlField(
                  label: 'Sets',
                  value: _sets,
                  icon: Icons.repeat,
                  color: Theme.of(context).colorScheme.primary,
                  onChanged: (value) => setState(() => _sets = value),
                  min: 1,
                  max: 10,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildControlField(
                  label: 'Reps',
                  value: _reps,
                  icon: Icons.replay,
                  color: Theme.of(context).colorScheme.secondary,
                  onChanged: (value) => setState(() => _reps = value),
                  min: 1,
                  max: 100,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildControlField(
                  label: 'Rest (s)',
                  value: _rest,
                  icon: Icons.pause,
                  color: Theme.of(context).colorScheme.tertiary,
                  onChanged: (value) => setState(() => _rest = value),
                  min: 0,
                  max: 300,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _applyToAll,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
                foregroundColor: Theme.of(context).colorScheme.onSecondary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.done_all,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Apply to All Exercises',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlField({
    required String label,
    required int value,
    required IconData icon,
    required Color color,
    required Function(int) onChanged,
    required int min,
    required int max,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: value > min ? () => onChanged(value - 1) : null,
              child: Container(
                width: 28,
                height: 28,
                decoration: BoxDecoration(
                  color: value > min 
                      ? color.withOpacity(0.1) 
                      : Theme.of(context).colorScheme.outline.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.remove,
                  size: 16,
                  color: value > min 
                      ? color 
                      : Theme.of(context).colorScheme.outline,
                ),
              ),
            ),
            Container(
              width: 40,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                value.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
            GestureDetector(
              onTap: value < max ? () => onChanged(value + 1) : null,
              child: Container(
                width: 28,
                height: 28,
                decoration: BoxDecoration(
                  color: value < max 
                      ? color.withOpacity(0.1) 
                      : Theme.of(context).colorScheme.outline.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.add,
                  size: 16,
                  color: value < max 
                      ? color 
                      : Theme.of(context).colorScheme.outline,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _applyToAll() {
    Map<String, WorkoutExercise> configurations = {};
    
    for (var exercise in widget.exercises) {
      configurations[exercise.id] = WorkoutExercise(
        exerciseId: exercise.id,
        sets: _sets,
        reps: _reps,
        duration: 0,
        restTime: _rest,
      );
    }
    
    widget.onBulkConfiguration(configurations);
    
    setState(() {
      _isExpanded = false;
    });
  }
} 