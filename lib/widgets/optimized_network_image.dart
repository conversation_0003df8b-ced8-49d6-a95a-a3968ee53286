import 'package:flutter/material.dart';

class OptimizedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  
  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  });
  
  @override
  Widget build(BuildContext context) {
    // Calculate cache dimensions based on device pixel ratio
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final cacheWidth = width != null ? (width! * devicePixelRatio).round() : null;
    final cacheHeight = height != null ? (height! * devicePixelRatio).round() : null;
    
    Widget imageWidget = Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return child;
        }
        return placeholder ?? Container(
          width: width,
          height: height,
          color: Theme.of(context).colorScheme.surface,
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                  : null,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary.withOpacity(0.5),
              ),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? Container(
          width: width,
          height: height,
          color: Theme.of(context).colorScheme.surface,
          child: Icon(
            Icons.broken_image,
            size: 32,
            color: Theme.of(context).colorScheme.outline,
          ),
        );
      },
    );
    
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }
    
    return imageWidget;
  }
}

// Convenience widget for workout images
class WorkoutImage extends StatelessWidget {
  final String imageUrl;
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  
  const WorkoutImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    this.borderRadius,
  });
  
  @override
  Widget build(BuildContext context) {
    return OptimizedNetworkImage(
      imageUrl: imageUrl.isNotEmpty ? imageUrl : "https://via.placeholder.com/${width.toInt()}x${height.toInt()}",
      width: width,
      height: height,
      borderRadius: borderRadius,
      errorWidget: Container(
        width: width,
        height: height,
        color: Theme.of(context).colorScheme.surface,
        child: Icon(
          Icons.fitness_center,
          size: width * 0.3,
          color: Theme.of(context).colorScheme.outline,
        ),
      ),
    );
  }
}

// Convenience widget for exercise images
class ExerciseImage extends StatelessWidget {
  final String imageUrl;
  final double size;
  final BorderRadius? borderRadius;
  
  const ExerciseImage({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.borderRadius,
  });
  
  @override
  Widget build(BuildContext context) {
    return OptimizedNetworkImage(
      imageUrl: imageUrl.isNotEmpty ? imageUrl : "https://via.placeholder.com/${size.toInt()}x${size.toInt()}",
      width: size,
      height: size,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      errorWidget: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.fitness_center,
          size: size * 0.4,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
} 