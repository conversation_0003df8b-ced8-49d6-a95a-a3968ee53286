import 'package:flutter/material.dart';
import '../models/exercise_model.dart';

class ExerciseSuggestionCard extends StatelessWidget {
  final List<ExerciseModel> suggestedExercises;
  final Function(ExerciseModel) onExerciseAdd;
  final List<ExerciseModel> selectedExercises;

  const ExerciseSuggestionCard({
    super.key,
    required this.suggestedExercises,
    required this.onExerciseAdd,
    required this.selectedExercises,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestedExercises.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.05),
            Theme.of(context).colorScheme.secondary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Suggested Exercises',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      'Complete your workout with these recommendations',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: suggestedExercises.length,
              itemBuilder: (context, index) {
                final exercise = suggestedExercises[index];
                final isAlreadySelected = selectedExercises.any((e) => e.id == exercise.id);
                
                return Container(
                  width: 200,
                  margin: EdgeInsets.only(right: index < suggestedExercises.length - 1 ? 12 : 0),
                  child: _buildSuggestionItem(context, exercise, isAlreadySelected),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(BuildContext context, ExerciseModel exercise, bool isAlreadySelected) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: exercise.imageUrl.isNotEmpty
                  ? ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: Image.network(
                        exercise.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(context),
                      ),
                    )
                  : _buildFallbackIcon(context),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.name,
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        exercise.primaryMuscleGroup,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: isAlreadySelected ? null : () => onExerciseAdd(exercise),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isAlreadySelected 
                              ? Theme.of(context).colorScheme.outline.withOpacity(0.3)
                              : Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          isAlreadySelected ? Icons.check : Icons.add,
                          size: 16,
                          color: isAlreadySelected 
                              ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
                              : Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFallbackIcon(BuildContext context) {
    return Center(
      child: Icon(
        Icons.fitness_center,
        size: 32,
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      ),
    );
  }
}

class ExerciseSuggestionService {
  static List<ExerciseModel> getSuggestedExercises(
    List<ExerciseModel> selectedExercises,
    List<ExerciseModel> allExercises,
  ) {
    if (selectedExercises.isEmpty) {
      // Return popular exercises for beginners
      return allExercises
          .where((e) => e.difficulty == 'beginner')
          .take(5)
          .toList();
    }

    // Get muscle groups from selected exercises
    Set<String> selectedMuscleGroups = {};
    Set<String> selectedCategories = {};
    
    for (var exercise in selectedExercises) {
      selectedMuscleGroups.add(exercise.primaryMuscleGroup);
      selectedMuscleGroups.addAll(exercise.muscleGroups);
      selectedCategories.add(exercise.category);
    }

    // Find complementary exercises
    List<ExerciseModel> suggestions = [];
    
    // 1. Add exercises that target different muscle groups for balance
    final complementaryMuscleGroups = _getComplementaryMuscleGroups(selectedMuscleGroups);
    for (var muscleGroup in complementaryMuscleGroups) {
      final exercises = allExercises
          .where((e) => 
              e.primaryMuscleGroup == muscleGroup &&
              !selectedExercises.any((selected) => selected.id == e.id))
          .take(2)
          .toList();
      suggestions.addAll(exercises);
    }

    // 2. Add exercises from the same category but different muscle groups
    for (var category in selectedCategories) {
      final exercises = allExercises
          .where((e) => 
              e.category == category &&
              !selectedMuscleGroups.contains(e.primaryMuscleGroup) &&
              !selectedExercises.any((selected) => selected.id == e.id) &&
              !suggestions.any((suggested) => suggested.id == e.id))
          .take(2)
          .toList();
      suggestions.addAll(exercises);
    }

    // 3. Limit to 6 suggestions and prioritize by difficulty match
    final avgDifficulty = _getAverageDifficulty(selectedExercises);
    suggestions.sort((a, b) {
      final aDiffScore = _getDifficultyScore(a.difficulty, avgDifficulty);
      final bDiffScore = _getDifficultyScore(b.difficulty, avgDifficulty);
      return aDiffScore.compareTo(bDiffScore);
    });

    return suggestions.take(6).toList();
  }

  static Set<String> _getComplementaryMuscleGroups(Set<String> selectedMuscleGroups) {
    const muscleGroupPairs = {
      'Chest': ['Back', 'Shoulders'],
      'Back': ['Chest', 'Arms'],
      'Shoulders': ['Chest', 'Arms'],
      'Arms': ['Back', 'Shoulders'],
      'Legs': ['Core', 'Glutes'],
      'Core': ['Legs', 'Back'],
      'Glutes': ['Legs', 'Core'],
    };

    Set<String> complementary = {};
    for (var selected in selectedMuscleGroups) {
      final pairs = muscleGroupPairs[selected];
      if (pairs != null) {
        complementary.addAll(pairs);
      }
    }

    // Remove already selected muscle groups
    complementary.removeAll(selectedMuscleGroups);
    return complementary;
  }

  static String _getAverageDifficulty(List<ExerciseModel> exercises) {
    if (exercises.isEmpty) return 'beginner';
    
    final difficultyScores = exercises.map((e) => _getDifficultyValue(e.difficulty)).toList();
    final avgScore = difficultyScores.reduce((a, b) => a + b) / difficultyScores.length;
    
    if (avgScore <= 1.3) return 'beginner';
    if (avgScore <= 2.3) return 'intermediate';
    return 'advanced';
  }

  static int _getDifficultyValue(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return 1;
      case 'intermediate': return 2;
      case 'advanced': return 3;
      default: return 1;
    }
  }

  static int _getDifficultyScore(String difficulty, String targetDifficulty) {
    return ((_getDifficultyValue(difficulty) - _getDifficultyValue(targetDifficulty)).abs());
  }
} 