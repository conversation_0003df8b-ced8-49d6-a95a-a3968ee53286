import 'package:flutter/material.dart';
import '../models/exercise_model.dart';

class WorkoutPresetSelector extends StatelessWidget {
  final Function(WorkoutPreset) onPresetSelected;
  final String selectedDifficulty;

  const WorkoutPresetSelector({
    super.key,
    required this.onPresetSelected,
    required this.selectedDifficulty,
  });

  @override
  Widget build(BuildContext context) {
    final presets = _getPresetsForDifficulty(selectedDifficulty);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flash_on,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Quick Setup Presets',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Apply common configurations to all exercises',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: presets.map((preset) => _buildPresetCard(context, preset)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetCard(BuildContext context, WorkoutPreset preset) {
    return GestureDetector(
      onTap: () => onPresetSelected(preset),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              preset.color.withOpacity(0.1),
              preset.color.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: preset.color.withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  preset.icon,
                  color: preset.color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  preset.name,
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              preset.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${preset.sets} sets × ${preset.reps} reps • ${preset.rest}s rest',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: preset.color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<WorkoutPreset> _getPresetsForDifficulty(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return [
          WorkoutPreset(
            name: 'Light Start',
            description: 'Perfect for getting started',
            sets: 2,
            reps: 8,
            rest: 90,
            icon: Icons.child_friendly,
            color: Colors.green,
          ),
          WorkoutPreset(
            name: 'Steady Build',
            description: 'Gradual strength building',
            sets: 3,
            reps: 10,
            rest: 75,
            icon: Icons.trending_up,
            color: Colors.blue,
          ),
        ];
      case 'intermediate':
        return [
          WorkoutPreset(
            name: 'Balanced',
            description: 'Well-rounded workout',
            sets: 3,
            reps: 12,
            rest: 60,
            icon: Icons.balance,
            color: Colors.orange,
          ),
          WorkoutPreset(
            name: 'Power Focus',
            description: 'Higher intensity training',
            sets: 4,
            reps: 10,
            rest: 75,
            icon: Icons.bolt,
            color: Colors.purple,
          ),
        ];
      case 'advanced':
        return [
          WorkoutPreset(
            name: 'High Volume',
            description: 'Maximum muscle engagement',
            sets: 4,
            reps: 15,
            rest: 45,
            icon: Icons.fitness_center,
            color: Colors.red,
          ),
          WorkoutPreset(
            name: 'Strength Focus',
            description: 'Heavy lifting protocol',
            sets: 5,
            reps: 6,
            rest: 120,
            icon: Icons.sports_gymnastics,
            color: Colors.indigo,
          ),
        ];
      default:
        return [];
    }
  }
}

class WorkoutPreset {
  final String name;
  final String description;
  final int sets;
  final int reps;
  final int rest;
  final IconData icon;
  final Color color;

  WorkoutPreset({
    required this.name,
    required this.description,
    required this.sets,
    required this.reps,
    required this.rest,
    required this.icon,
    required this.color,
  });
} 