import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/workout_agent_service.dart';
import '../models/genkit_models.dart';

// Provider for WorkoutAgentService
final workoutAgentServiceProvider = Provider<WorkoutAgentService>((ref) {
  return WorkoutAgentService();
});

// Provider for FitnessChatService
final fitnessChatServiceProvider = Provider<FitnessChatService>((ref) {
  return FitnessChatService();
});

// Provider for onboarding completion
final completeOnboardingProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.completeUserOnboarding();
});

// Provider for generating fitness guide
final generateFitnessGuideProvider = FutureProvider.autoDispose<String>((ref) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.generateFitnessGuide();
});

// Provider for creating first workout
final createFirstWorkoutProvider = FutureProvider.autoDispose.family<String, String?>((ref, guideId) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.createFirstWorkout(guideId: guideId);
});

// Provider for exercise recommendations
final exerciseRecommendationProvider = FutureProvider.autoDispose.family<ExerciseRecommendation, bool>((ref, saveWorkout) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.getNextExercise(saveWorkout: saveWorkout);
});

// Provider for workout analysis
final workoutAnalysisProvider = FutureProvider.autoDispose.family<WorkoutAnalysis, String?>((ref, workoutId) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.analyzeLastWorkout(workoutId: workoutId);
});

// Provider for generating next workout
final generateNextWorkoutProvider = FutureProvider.autoDispose.family<String, NextWorkoutParams>((ref, params) async {
  final service = ref.read(workoutAgentServiceProvider);
  return service.generateNextWorkout(
    skipRecoveryCheck: params.skipRecoveryCheck,
    targetMuscles: params.targetMuscles,
    workoutType: params.workoutType,
  );
});

// Parameters class for next workout generation
class NextWorkoutParams {
  final bool skipRecoveryCheck;
  final List<String>? targetMuscles;
  final String? workoutType;
  
  NextWorkoutParams({
    this.skipRecoveryCheck = false,
    this.targetMuscles,
    this.workoutType,
  });
}