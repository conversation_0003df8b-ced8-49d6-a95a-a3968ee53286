import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/user_profile_model.dart';
import '../../../services/comprehensive_onboarding_service.dart';
import '../../../services/consolidated_user_service_fixed.dart';

/// Provider for ComprehensiveOnboardingService
final comprehensiveOnboardingServiceProvider = Provider<ComprehensiveOnboardingService>((ref) {
  return ComprehensiveOnboardingService();
});

/// Provider for ConsolidatedUserService
final consolidatedUserServiceProvider = Provider<ConsolidatedUserServiceFixed>((ref) {
  return ConsolidatedUserServiceFixed();
});

/// Providers for onboarding step management
final currentStepProvider = StateProvider<int>((ref) => 0);
final isLoadingProvider = StateProvider<bool>((ref) => false);

/// Providers for personal info
final nameProvider = StateProvider<String>((ref) => '');
final ageProvider = StateProvider<String>((ref) => '');
final selectedGenderProvider = StateProvider<Gender?>((ref) => null);
final heightFeetProvider = StateProvider<String>((ref) => '');
final heightInchesProvider = StateProvider<String>((ref) => '');
final weightProvider = StateProvider<String>((ref) => '');

/// Providers for fitness goals
final selectedGoalsProvider = StateProvider<List<FitnessGoal>>((ref) => []);
final sportActivityProvider = StateProvider<String?>((ref) => null);
final personalCoachNotesProvider = StateProvider<String>((ref) => '');

/// Providers for fitness level
final cardioLevelProvider = StateProvider<double>((ref) => 0.2);
final weightliftingLevelProvider = StateProvider<double>((ref) => 0.2);
final exercisesToAvoidProvider = StateProvider<String>((ref) => '');

/// Providers for workout preferences
final selectedEnvironmentsProvider = StateProvider<List<WorkoutEnvironment>>((ref) => []);
final workoutsPerWeekProvider = StateProvider<int>((ref) => 3);
final workoutDurationMinutesProvider = StateProvider<int?>((ref) => 45);
final optimizeTimeForMeProvider = StateProvider<bool>((ref) => false);
final additionalNotesProvider = StateProvider<String>((ref) => '');

/// Text editing controllers providers
final nameControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  // Listen to name provider changes and update controller
  ref.listen(nameProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final ageControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(ageProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final heightFeetControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(heightFeetProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final heightInchesControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(heightInchesProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final weightControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(weightProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final sportControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(sportActivityProvider, (previous, next) {
    if (controller.text != (next ?? '')) {
      controller.text = next ?? '';
    }
  });
  
  return controller;
});

final personalCoachNotesControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(personalCoachNotesProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final exercisesToAvoidControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(exercisesToAvoidProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

final additionalNotesControllerProvider = Provider<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() => controller.dispose());
  
  ref.listen(additionalNotesProvider, (previous, next) {
    if (controller.text != next) {
      controller.text = next;
    }
  });
  
  return controller;
});

/// Provider for loading existing data
final existingOnboardingDataProvider = FutureProvider.family<ComprehensiveOnboardingModel?, String>((ref, userId) async {
  final onboardingService = ref.watch(comprehensiveOnboardingServiceProvider);
  return await onboardingService.getComprehensiveOnboarding(userId);
});

/// State notifier for onboarding data management
class OnboardingNotifier extends StateNotifier<AsyncValue<void>> {
  final ComprehensiveOnboardingService _onboardingService;
  final ConsolidatedUserServiceFixed _userService;
  final Ref _ref;
  
  OnboardingNotifier(this._onboardingService, this._userService, this._ref) 
      : super(const AsyncValue.data(null));
  
  Future<void> loadExistingData(String userId) async {
    state = const AsyncValue.loading();
    try {
      final onboarding = await _onboardingService.getComprehensiveOnboarding(userId);
      
      if (onboarding != null) {
        // Update all providers with existing data
        _ref.read(nameProvider.notifier).state = onboarding.profile.name;
        _ref.read(selectedGenderProvider.notifier).state = onboarding.profile.gender;
        
        if (onboarding.profile.age != null) {
          _ref.read(ageProvider.notifier).state = onboarding.profile.age.toString();
        }
        
        if (onboarding.profile.heightFeet != null) {
          final totalFeet = onboarding.profile.heightFeet!;
          final feet = totalFeet.floor();
          final inches = ((totalFeet - feet) * 12).round();
          _ref.read(heightFeetProvider.notifier).state = feet.toString();
          _ref.read(heightInchesProvider.notifier).state = inches.toString();
        }
        
        if (onboarding.profile.weightLbs != null) {
          _ref.read(weightProvider.notifier).state = onboarding.profile.weightLbs.toString();
        }
        
        // Fitness goals
        _ref.read(selectedGoalsProvider.notifier).state = List.from(onboarding.fitnessGoals);
        _ref.read(personalCoachNotesProvider.notifier).state = onboarding.personalCoachNotes ?? '';
        
        // Find sport-specific goal
        final sportGoal = onboarding.fitnessGoals.firstWhere(
          (g) => g.type == FitnessGoalType.sportSpecific,
          orElse: () => FitnessGoal(
            type: FitnessGoalType.sportSpecific,
            priority: 0,
            selectedAt: DateTime.now(),
          ),
        );
        if (sportGoal.sportActivity != null) {
          _ref.read(sportActivityProvider.notifier).state = sportGoal.sportActivity;
        }
        
        // Fitness level
        _ref.read(cardioLevelProvider.notifier).state = onboarding.fitnessLevel.cardioLevel;
        _ref.read(weightliftingLevelProvider.notifier).state = onboarding.fitnessLevel.weightliftingLevel;
        _ref.read(exercisesToAvoidProvider.notifier).state = onboarding.fitnessLevel.exercisesToAvoid ?? '';
        
        // Workout preferences
        _ref.read(selectedEnvironmentsProvider.notifier).state = List.from(onboarding.workoutPreferences.environments);
        _ref.read(workoutsPerWeekProvider.notifier).state = onboarding.workoutPreferences.workoutsPerWeek;
        _ref.read(workoutDurationMinutesProvider.notifier).state = onboarding.workoutPreferences.workoutDurationMinutes;
        _ref.read(optimizeTimeForMeProvider.notifier).state = onboarding.workoutPreferences.workoutDurationMinutes == null;
        _ref.read(additionalNotesProvider.notifier).state = onboarding.workoutPreferences.additionalNotes ?? '';
      }
      
      state = const AsyncValue.data(null);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
  
  Future<void> saveOnboardingData() async {
    state = const AsyncValue.loading();
    try {
      final currentUser = _userService.currentUser;
      if (currentUser == null) throw Exception('No user logged in');
      
      // Collect all data from providers
      final name = _ref.read(nameProvider);
      final age = int.tryParse(_ref.read(ageProvider)) ?? 0;
      final gender = _ref.read(selectedGenderProvider);
      final heightFeet = double.tryParse(_ref.read(heightFeetProvider)) ?? 0.0;
      final heightInches = double.tryParse(_ref.read(heightInchesProvider)) ?? 0.0;
      final weight = double.tryParse(_ref.read(weightProvider)) ?? 0.0;
      
      final selectedGoals = _ref.read(selectedGoalsProvider);
      final personalCoachNotes = _ref.read(personalCoachNotesProvider);
      
      final cardioLevel = _ref.read(cardioLevelProvider);
      final weightliftingLevel = _ref.read(weightliftingLevelProvider);
      final exercisesToAvoid = _ref.read(exercisesToAvoidProvider);
      
      final selectedEnvironments = _ref.read(selectedEnvironmentsProvider);
      final workoutsPerWeek = _ref.read(workoutsPerWeekProvider);
      final workoutDurationMinutes = _ref.read(workoutDurationMinutesProvider);
      final optimizeTimeForMe = _ref.read(optimizeTimeForMeProvider);
      final additionalNotes = _ref.read(additionalNotesProvider);
      
      // Create comprehensive onboarding model
      final now = DateTime.now();
      final onboarding = ComprehensiveOnboardingModel(
        profile: UserProfileModel(
          userId: currentUser.uid,
          name: name,
          gender: gender,
          age: age > 0 ? age : null,
          heightFeet: heightFeet > 0 ? heightFeet + (heightInches / 12) : null,
          weightLbs: weight > 0 ? weight : null,
          createdAt: now,
        ),
        fitnessGoals: selectedGoals,
        fitnessLevel: FitnessLevelModel(
          userId: currentUser.uid,
          cardioLevel: cardioLevel,
          weightliftingLevel: weightliftingLevel,
          exercisesToAvoid: exercisesToAvoid.isNotEmpty ? exercisesToAvoid : null,
          createdAt: now,
        ),
        workoutPreferences: WorkoutPreferencesModel(
          userId: currentUser.uid,
          environments: selectedEnvironments,
          workoutsPerWeek: workoutsPerWeek,
          workoutDurationMinutes: optimizeTimeForMe ? null : workoutDurationMinutes,
          additionalNotes: additionalNotes.isNotEmpty ? additionalNotes : null,
          createdAt: now,
        ),
        personalCoachNotes: personalCoachNotes.isNotEmpty ? personalCoachNotes : null,
        completedAt: now,
      );
      
      // Save the onboarding data
      await _onboardingService.saveComprehensiveOnboarding(onboarding);
      
      state = const AsyncValue.data(null);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
}

/// Provider for onboarding state notifier
final onboardingNotifierProvider = StateNotifierProvider<OnboardingNotifier, AsyncValue<void>>((ref) {
  final onboardingService = ref.watch(comprehensiveOnboardingServiceProvider);
  final userService = ref.watch(consolidatedUserServiceProvider);
  return OnboardingNotifier(onboardingService, userService, ref);
});

/// Helper provider to check if current step can proceed
final canProceedProvider = Provider<bool>((ref) {
  final currentStep = ref.watch(currentStepProvider);
  
  switch (currentStep) {
    case 0: // About You
      final name = ref.watch(nameProvider);
      final age = ref.watch(ageProvider);
      final gender = ref.watch(selectedGenderProvider);
      return name.isNotEmpty && age.isNotEmpty && gender != null;
      
    case 1: // Fitness Goals
      final goals = ref.watch(selectedGoalsProvider);
      return goals.isNotEmpty;
      
    case 2: // Current Fitness Level
      return true; // Always can proceed from fitness level
      
    case 3: // Workout Environment
      final environments = ref.watch(selectedEnvironmentsProvider);
      return environments.isNotEmpty;
      
    case 4: // Workout Schedule
      return true; // Always can proceed from schedule
      
    case 5: // Anything Else
      return true; // Always can proceed from final step
      
    default:
      return false;
  }
});

/// Step titles provider
final stepTitlesProvider = Provider<List<String>>((ref) => [
  'About You',
  'Fitness Goals', 
  'Current Fitness Level',
  'Workout Environment',
  'Workout Schedule',
  'Anything Else',
]);