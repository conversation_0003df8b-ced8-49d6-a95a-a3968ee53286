/// App configuration constants
/// 
/// SECURITY NOTE: Never commit API keys or sensitive data to version control
/// Use environment variables or secure storage for production
class AppConfig {
  // Firebase configuration is handled by firebase_options.dart
  
  // For development only - in production use:
  // 1. Firebase Remote Config
  // 2. Environment variables on your server
  // 3. Secure storage solutions
  
  // Placeholder for Google AI API key
  // TODO: Implement secure key retrieval
  static const String googleAiApiKey = 'YOUR_API_KEY_HERE';
  
  // App metadata
  static const String appName = 'FitTracker';
  static const String appVersion = '1.0.0';
  
  // Cache configuration
  static const Duration cacheValidDuration = Duration(hours: 1);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50 MB
  static const int maxCacheImages = 50;
  
  // API endpoints (if needed in future)
  static const String baseApiUrl = '';
  
  // Feature flags
  static const bool enableAIChat = true;
  static const bool enableAudioChat = true;
  static const bool enableCommunityFeatures = false;
  static const bool enableNutritionTracking = false;
}