import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Secure configuration manager for sensitive data
/// 
/// This class handles secure storage and retrieval of API keys and other
/// sensitive configuration data. It uses flutter_secure_storage for 
/// encrypted storage on the device.
class SecureConfig {
  static const _storage = FlutterSecureStorage();
  
  // Storage keys
  static const String _googleAiApiKeyKey = 'google_ai_api_key';
  
  /// Initialize secure config with environment variables
  /// This should only be used during development
  static Future<void> initializeFromEnv() async {
    final apiKey = dotenv.env['GOOGLE_GENAI_API_KEY'];
    if (apiKey != null && apiKey != 'YOUR_API_KEY_HERE') {
      await setGoogleAiApiKey(apiKey);
    }
  }
  
  /// Get Google AI API key from secure storage
  static Future<String?> getGoogleAiApiKey() async {
    try {
      return await _storage.read(key: _googleAiApiKeyKey);
    } catch (e) {
      print('Error reading API key: $e');
      return null;
    }
  }
  
  /// Set Google AI API key in secure storage
  static Future<void> setGoogleAiApiKey(String apiKey) async {
    try {
      await _storage.write(key: _googleAiApiKeyKey, value: apiKey);
    } catch (e) {
      print('Error storing API key: $e');
    }
  }
  
  /// Clear all secure storage (use with caution)
  static Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      print('Error clearing secure storage: $e');
    }
  }
  
  /// Check if API key is configured
  static Future<bool> isConfigured() async {
    final apiKey = await getGoogleAiApiKey();
    return apiKey != null && apiKey.isNotEmpty;
  }
}