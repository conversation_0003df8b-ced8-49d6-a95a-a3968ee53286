import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/genkit_providers.dart';
import '../models/genkit_models.dart';

class AIWorkoutGeneratorScreen extends ConsumerStatefulWidget {
  const AIWorkoutGeneratorScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<AIWorkoutGeneratorScreen> createState() => _AIWorkoutGeneratorScreenState();
}

class _AIWorkoutGeneratorScreenState extends ConsumerState<AIWorkoutGeneratorScreen> {
  bool _isGenerating = false;
  String? _lastWorkoutId;
  WorkoutAnalysis? _lastAnalysis;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Workout Generator'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildFeatureCard(
              title: 'Generate Next Workout',
              description: 'Get a personalized workout based on your progress and recovery',
              icon: Icons.fitness_center,
              onTap: _generateNextWorkout,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            _buildFeatureCard(
              title: 'Analyze Last Workout',
              description: 'Get insights and recommendations from your previous session',
              icon: Icons.analytics,
              onTap: _analyzeLastWorkout,
              color: Theme.of(context).colorScheme.secondary,
            ),
            const SizedBox(height: 16),
            _buildFeatureCard(
              title: 'Get Exercise Recommendation',
              description: 'Get a single exercise recommendation for your next workout',
              icon: Icons.recommend,
              onTap: _getExerciseRecommendation,
              color: Theme.of(context).colorScheme.tertiary,
            ),
            if (_lastWorkoutId != null) ...[
              const SizedBox(height: 24),
              _buildResultCard(
                title: 'Last Generated Workout',
                content: 'Workout ID: $_lastWorkoutId',
                icon: Icons.check_circle,
              ),
            ],
            if (_lastAnalysis != null) ...[
              const SizedBox(height: 16),
              _buildAnalysisCard(_lastAnalysis!),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureCard({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: _isGenerating ? null : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (_isGenerating)
                const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildResultCard({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Card(
      color: Theme.of(context).colorScheme.primaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(content),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAnalysisCard(WorkoutAnalysis analysis) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Workout Analysis',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildAnalysisSection('Performance', analysis.overallPerformance),
            const SizedBox(height: 12),
            _buildAnalysisSection('Recovery Status', analysis.recoveryStatus),
            const SizedBox(height: 12),
            if (analysis.strengths.isNotEmpty) ...[
              const Text(
                'Strengths:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...analysis.strengths.map((s) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check, size: 16, color: Colors.green),
                    const SizedBox(width: 8),
                    Expanded(child: Text(s)),
                  ],
                ),
              )),
              const SizedBox(height: 8),
            ],
            if (analysis.areasForImprovement.isNotEmpty) ...[
              const Text(
                'Areas for Improvement:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...analysis.areasForImprovement.map((a) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  children: [
                    const Icon(Icons.trending_up, size: 16, color: Colors.orange),
                    const SizedBox(width: 8),
                    Expanded(child: Text(a)),
                  ],
                ),
              )),
              const SizedBox(height: 8),
            ],
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Next Workout Recommendation:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(analysis.nextWorkoutRecommendation),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAnalysisSection(String title, String content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$title: ',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        Expanded(child: Text(content)),
      ],
    );
  }
  
  Future<void> _generateNextWorkout() async {
    setState(() => _isGenerating = true);
    
    try {
      final params = NextWorkoutParams();
      final workoutId = await ref.read(
        generateNextWorkoutProvider(params).future,
      );
      
      setState(() {
        _lastWorkoutId = workoutId;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Workout generated successfully! ID: $workoutId'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isGenerating = false);
    }
  }
  
  Future<void> _analyzeLastWorkout() async {
    setState(() => _isGenerating = true);
    
    try {
      final analysis = await ref.read(
        workoutAnalysisProvider(null).future,
      );
      
      setState(() {
        _lastAnalysis = analysis;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isGenerating = false);
    }
  }
  
  Future<void> _getExerciseRecommendation() async {
    setState(() => _isGenerating = true);
    
    try {
      final recommendation = await ref.read(
        exerciseRecommendationProvider(false).future,
      );
      
      if (mounted) {
        _showExerciseRecommendation(recommendation);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isGenerating = false);
    }
  }
  
  void _showExerciseRecommendation(ExerciseRecommendation recommendation) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const Text(
                'Exercise Recommendation',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Card(
                color: Theme.of(context).colorScheme.primaryContainer,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        recommendation.exercise.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(recommendation.exercise.description),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        children: [
                          Chip(
                            label: Text(recommendation.exercise.primaryMuscleGroup),
                            backgroundColor: Theme.of(context).colorScheme.secondary,
                          ),
                          if (recommendation.exercise.sets != null)
                            Chip(label: Text('${recommendation.exercise.sets} sets')),
                          if (recommendation.exercise.reps != null)
                            Chip(label: Text('${recommendation.exercise.reps} reps')),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Why this exercise?',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(recommendation.reason),
              const SizedBox(height: 16),
              if (recommendation.alternatives.isNotEmpty) ...[
                const Text(
                  'Alternative Exercises:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...recommendation.alternatives.map((alt) => Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(alt.name),
                    subtitle: Text(alt.primaryMuscleGroup),
                    trailing: Text(alt.difficulty),
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
    );
  }
}