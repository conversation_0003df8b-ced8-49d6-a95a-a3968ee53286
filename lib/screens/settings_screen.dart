import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/settings_providers.dart';
import '../../../models/app_settings_model.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: settingsAsync.when(
        data: (settings) => _buildSettingsContent(context, ref, settings),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading settings: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(settingsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsContent(BuildContext context, WidgetRef ref, AppSettings settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Appearance Section
        SettingsSection(
          title: 'Appearance',
          children: [
            SettingsTile.dropdown<ThemeMode>(
              title: 'Theme',
              subtitle: 'Choose your preferred theme',
              icon: Icons.palette,
              value: settings.themeMode,
              items: ThemeMode.values.map((mode) => DropdownMenuItem(
                value: mode,
                child: Text(_getThemeModeLabel(mode)),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('themeMode', value);
                }
              },
            ),
            SettingsTile.dropdown<Units>(
              title: 'Units',
              subtitle: 'Measurement system',
              icon: Icons.straighten,
              value: settings.units,
              items: Units.values.map((unit) => DropdownMenuItem(
                value: unit,
                child: Text(_getUnitsLabel(unit)),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('units', value);
                }
              },
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Notifications Section
        SettingsSection(
          title: 'Notifications',
          children: [
            SettingsTile.switch(
              title: 'Enable Notifications',
              subtitle: 'Receive app notifications',
              icon: Icons.notifications,
              value: settings.notificationsEnabled,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('notificationsEnabled', value);
              },
            ),
            if (settings.notificationsEnabled) ...[
              SettingsTile.switch(
                title: 'Workout Reminders',
                subtitle: 'Get reminded about your workouts',
                icon: Icons.fitness_center,
                value: settings.workoutReminders,
                onChanged: (value) {
                  ref.read(settingsProvider.notifier).updateSetting('workoutReminders', value);
                },
              ),
              SettingsTile.switch(
                title: 'Progress Updates',
                subtitle: 'Weekly progress notifications',
                icon: Icons.trending_up,
                value: settings.progressUpdates,
                onChanged: (value) {
                  ref.read(settingsProvider.notifier).updateSetting('progressUpdates', value);
                },
              ),
            ],
          ],
        ),

        const SizedBox(height: 20),

        // Workout Section
        SettingsSection(
          title: 'Workout',
          children: [
            SettingsTile.switch(
              title: 'Auto-play Videos',
              subtitle: 'Automatically play exercise videos',
              icon: Icons.play_circle,
              value: settings.autoPlayVideos,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('autoPlayVideos', value);
              },
            ),
            SettingsTile.switch(
              title: 'Sound Effects',
              subtitle: 'Play workout sound effects',
              icon: Icons.volume_up,
              value: settings.soundEffects,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('soundEffects', value);
              },
            ),
            SettingsTile.slider(
              title: 'Rest Timer Duration',
              subtitle: '${settings.restTimerDuration} seconds',
              icon: Icons.timer,
              value: settings.restTimerDuration.toDouble(),
              min: 30,
              max: 300,
              divisions: 27,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('restTimerDuration', value.round());
              },
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Privacy Section
        SettingsSection(
          title: 'Privacy & Data',
          children: [
            SettingsTile.switch(
              title: 'Analytics',
              subtitle: 'Help improve the app with usage data',
              icon: Icons.analytics,
              value: settings.analyticsEnabled,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('analyticsEnabled', value);
              },
            ),
            SettingsTile.switch(
              title: 'Crash Reporting',
              subtitle: 'Send crash reports to help fix bugs',
              icon: Icons.bug_report,
              value: settings.crashReporting,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('crashReporting', value);
              },
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Account Section
        SettingsSection(
          title: 'Account',
          children: [
            SettingsTile.navigation(
              title: 'Edit Profile',
              subtitle: 'Update your personal information',
              icon: Icons.person,
              onTap: () {
                Navigator.pushNamed(context, '/edit-profile');
              },
            ),
            SettingsTile.navigation(
              title: 'Fitness Goals',
              subtitle: 'Update your fitness goals',
              icon: Icons.flag,
              onTap: () {
                Navigator.pushNamed(context, '/fitness-goals');
              },
            ),
            SettingsTile.navigation(
              title: 'Export Data',
              subtitle: 'Download your workout data',
              icon: Icons.download,
              onTap: () {
                _showExportDialog(context);
              },
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Support Section
        SettingsSection(
          title: 'Support',
          children: [
            SettingsTile.navigation(
              title: 'Help & FAQ',
              subtitle: 'Get help and find answers',
              icon: Icons.help,
              onTap: () {
                Navigator.pushNamed(context, '/help');
              },
            ),
            SettingsTile.navigation(
              title: 'Contact Support',
              subtitle: 'Get in touch with our team',
              icon: Icons.support_agent,
              onTap: () {
                _showContactDialog(context);
              },
            ),
            SettingsTile.navigation(
              title: 'About',
              subtitle: 'App version and information',
              icon: Icons.info,
              onTap: () {
                _showAboutDialog(context);
              },
            ),
          ],
        ),

        const SizedBox(height: 40),
      ],
    );
  }

  String _getThemeModeLabel(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return 'System';
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
    }
  }

  String _getUnitsLabel(Units units) {
    switch (units) {
      case Units.metric:
        return 'Metric (kg, cm)';
      case Units.imperial:
        return 'Imperial (lbs, ft)';
    }
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Your workout data will be exported as a CSV file.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement export functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Export started...')),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: <EMAIL>'),
            SizedBox(height: 8),
            Text('Response time: 24-48 hours'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Open email app
            },
            child: const Text('Send Email'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Agentic Fit',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.fitness_center, size: 48),
      children: [
        const Text('Your AI-powered fitness companion'),
        const SizedBox(height: 16),
        const Text('Built with Flutter and Firebase'),
      ],
    );
  }
} 