import 'package:flutter/material.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'direct_http_test.dart';
import 'minimal_function_test.dart';
import '../services/auth_test_service.dart';

class FunctionsTestScreen extends StatefulWidget {
  const FunctionsTestScreen({Key? key}) : super(key: key);

  @override
  State<FunctionsTestScreen> createState() => _FunctionsTestScreenState();
}

class _FunctionsTestScreenState extends State<FunctionsTestScreen> {
  String _result = 'Press a button to test';
  bool _isLoading = false;
  final AuthTestService _authTestService = AuthTestService();
  
  Future<void> _testFunction(String functionName, Map<String, dynamic> data) async {
    setState(() {
      _isLoading = true;
      _result = 'Calling $functionName...';
    });
    
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _result = 'Error: Not authenticated';
          _isLoading = false;
        });
        return;
      }
      
      // Get fresh ID token
      final idToken = await user.getIdToken(true);
      print('User: ${user.uid}');
      print('Token: ${idToken?.substring(0, 50) ?? "null"}...');
      
      final functions = FirebaseFunctions.instanceFor(region: 'us-central1');
      final callable = functions.httpsCallable(
        functionName,
        options: HttpsCallableOptions(
          timeout: const Duration(seconds: 30),
        ),
      );
      
      print('Calling function: $functionName with data: $data');
      final result = await callable.call(data);
      
      setState(() {
        _result = 'Success!\n${result.data}';
        _isLoading = false;
      });
    } catch (e) {
      print('Error: $e');
      setState(() {
        _result = 'Error: $e';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _testAuthentication() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing authentication...';
    });
    
    try {
      final result = await _authTestService.testAuthentication();
      setState(() {
        _result = 'Authentication Test Result:\n${result.toString()}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'Auth Test Error: $e';
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Functions Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('User: ${user?.email ?? "Not signed in"}'),
                    Text('UID: ${user?.uid ?? "N/A"}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testAuthentication,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('🔐 Test Authentication'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : () => _testFunction(
                'fitnessChat',
                {
                  'userId': user?.uid ?? '',
                  'message': 'Hello, test message',
                },
              ),
              child: const Text('Test fitnessChat'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : () => _testFunction(
                'generateFitnessGuide',
                {
                  'userId': user?.uid ?? '',
                },
              ),
              child: const Text('Test generateFitnessGuide'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : () => _testFunction(
                'recommendNextExercise',
                {
                  'userId': user?.uid ?? '',
                  'saveWorkout': false,
                },
              ),
              child: const Text('Test recommendNextExercise'),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const DirectHttpTestScreen(),
                        ),
                      );
                    },
                    child: const Text('Direct HTTP'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MinimalFunctionTest(),
                        ),
                      );
                    },
                    child: const Text('Minimal Test'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Result:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        if (_isLoading)
                          const Center(child: CircularProgressIndicator())
                        else
                          Text(_result),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}