import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:ui';
import '../models/workout_model.dart';
import '../models/exercise_model.dart';
import '../services/firestore_service.dart';
import '../services/auth_service.dart';
import 'workout_session_screen.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final WorkoutPlanModel workoutPlan;

  const WorkoutDetailScreen({super.key, required this.workoutPlan});

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen>
    with TickerProviderStateMixin {
  final FirestoreService _firestoreService = FirestoreService();
  final AuthService _authService = AuthService();
  
  List<ExerciseModel> _exercises = [];
  List<WorkoutExercise> _workoutExercises = [];
  bool _isLoading = true;
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Lock to portrait mode
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _workoutExercises = List.from(widget.workoutPlan.exercises);
    _loadExercises();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    // Restore orientation settings
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  Future<void> _loadExercises() async {
    List<ExerciseModel> exercises = [];
    for (WorkoutExercise workoutExercise in _workoutExercises) {
      ExerciseModel? exercise = await _firestoreService.getExerciseById(workoutExercise.exerciseId);
      if (exercise != null) {
        exercises.add(exercise);
      }
    }
    
    if (mounted) {
      setState(() {
        _exercises = exercises;
        _isLoading = false;
      });
      _fadeController.forward();
      _slideController.forward();
    }
  }

  String _getHeroImage() {
    // Return different images based on workout category
    switch (widget.workoutPlan.category.toLowerCase()) {
      case 'chest':
        return 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1080';
      case 'back':
        return 'https://images.unsplash.com/photo-1603287681836-b174ce5074c2?w=1080';
      case 'legs':
        return 'https://images.unsplash.com/photo-1434682881908-b43d0467b798?w=1080';
      case 'shoulders':
        return 'https://images.unsplash.com/photo-1532029837206-abbe2b7620e3?w=1080';
      case 'arms':
        return 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=1080';
      case 'core':
      case 'abs':
        return 'https://images.unsplash.com/photo-1571019613576-2b22c76fd955?w=1080';
      case 'cardio':
        return 'https://images.unsplash.com/photo-1538805060514-97d9cc17730c?w=1080';
      case 'full body':
      case 'strength':
      default:
        return 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=1080';
    }
  }

  int _calculateTotalCalories() {
    double totalCalories = 0;
    for (int i = 0; i < _exercises.length && i < _workoutExercises.length; i++) {
      final exercise = _exercises[i];
      final workoutExercise = _workoutExercises[i];
      
      // Calculate time per exercise
      double exerciseMinutes;
      if (workoutExercise.duration > 0) {
        exerciseMinutes = (workoutExercise.duration * workoutExercise.sets) / 60.0;
      } else {
        // Estimate 2 seconds per rep
        exerciseMinutes = (workoutExercise.reps * 2 * workoutExercise.sets) / 60.0;
      }
      
      totalCalories += exercise.caloriesPerMinute * exerciseMinutes;
    }
    
    return totalCalories.round();
  }

  void _showExerciseOptions(BuildContext context, int index) {
    final exercise = _exercises[index];
    final workoutExercise = _workoutExercises[index];
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ExerciseOptionsSheet(
        exercise: exercise,
        workoutExercise: workoutExercise,
        onUpdate: (updated) {
          setState(() {
            _workoutExercises[index] = updated;
          });
          Navigator.pop(context);
        },
        onRemove: () {
          setState(() {
            _exercises.removeAt(index);
            _workoutExercises.removeAt(index);
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  void _startWorkout() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutSessionScreen(
          workoutPlan: widget.workoutPlan,
          exercises: _exercises,
          workoutExercises: _workoutExercises,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Main content
          CustomScrollView(
            slivers: [
              // Hero section with image
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.5,
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Background image
                        Image.network(
                          _getHeroImage(),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[900],
                              child: const Icon(
                                Icons.fitness_center,
                                size: 80,
                                color: Colors.white24,
                              ),
                            );
                          },
                        ),
                        // Gradient overlay
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.3),
                                Colors.black.withOpacity(0.7),
                                Colors.black.withOpacity(0.9),
                              ],
                              stops: const [0.0, 0.5, 0.8, 1.0],
                            ),
                          ),
                        ),
                        // Back button
                        Positioned(
                          top: MediaQuery.of(context).padding.top + 8,
                          left: 8,
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.black26,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Icon(
                                    Icons.arrow_back_ios_new,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Content
                        Positioned(
                          bottom: 40,
                          left: 24,
                          right: 24,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  widget.workoutPlan.name,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: -0.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  widget.workoutPlan.description,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 16,
                                    height: 1.4,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Stats row
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    color: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildStatItem(
                          Icons.access_time,
                          '${widget.workoutPlan.duration}',
                          'min',
                        ),
                        _buildStatItem(
                          Icons.local_fire_department,
                          '${_calculateTotalCalories()}',
                          'kcal',
                        ),
                        _buildStatItem(
                          Icons.fitness_center,
                          '${_exercises.length}',
                          'exercises',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Exercise list
              SliverPadding(
                padding: const EdgeInsets.only(bottom: 100),
                sliver: SliverReorderableList(
                  itemBuilder: (context, index) {
                    final exercise = _exercises[index];
                    final workoutExercise = _workoutExercises[index];
                    
                    return ReorderableDragStartListener(
                      key: ValueKey(exercise.id),
                      index: index,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: _ExerciseCard(
                          exercise: exercise,
                          workoutExercise: workoutExercise,
                          index: index,
                          onOptionsTap: () => _showExerciseOptions(context, index),
                        ),
                      ),
                    );
                  },
                  itemCount: _exercises.length,
                  onReorder: (oldIndex, newIndex) {
                    setState(() {
                      if (newIndex > oldIndex) {
                        newIndex -= 1;
                      }
                      final exercise = _exercises.removeAt(oldIndex);
                      final workoutExercise = _workoutExercises.removeAt(oldIndex);
                      _exercises.insert(newIndex, exercise);
                      _workoutExercises.insert(newIndex, workoutExercise);
                    });
                  },
                ),
              ),
            ],
          ),
          
          // Start workout button
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.only(
                left: 24,
                right: 24,
                bottom: MediaQuery.of(context).padding.bottom + 16,
                top: 16,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0),
                    Colors.black.withOpacity(0.8),
                    Colors.black,
                  ],
                ),
              ),
              child: SlideTransition(
                position: _slideAnimation,
                child: ElevatedButton(
                  onPressed: _startWorkout,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 0,
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.play_arrow, size: 24),
                      SizedBox(width: 8),
                      Text(
                        'Start Workout',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white60,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

class _ExerciseCard extends StatelessWidget {
  final ExerciseModel exercise;
  final WorkoutExercise workoutExercise;
  final int index;
  final VoidCallback onOptionsTap;

  const _ExerciseCard({
    required this.exercise,
    required this.workoutExercise,
    required this.index,
    required this.onOptionsTap,
  });

  @override
  Widget build(BuildContext context) {
    final isTimerBased = exercise.isTimerBased || workoutExercise.duration > 0;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // TODO: Navigate to exercise detail/demo
          },
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[800],
                    child: exercise.imageUrl.isNotEmpty
                        ? Image.network(
                            exercise.imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.fitness_center,
                                color: Colors.grey[600],
                                size: 32,
                              );
                            },
                          )
                        : Icon(
                            Icons.fitness_center,
                            color: Colors.grey[600],
                            size: 32,
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                // Exercise info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exercise.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isTimerBased
                            ? '${workoutExercise.sets} × ${workoutExercise.duration}s'
                            : '${workoutExercise.sets} × ${workoutExercise.reps} reps',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 14,
                        ),
                      ),
                      if (exercise.primaryMuscleGroup.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          exercise.primaryMuscleGroup,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Options button
                IconButton(
                  onPressed: onOptionsTap,
                  icon: Icon(
                    Icons.more_vert,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ExerciseOptionsSheet extends StatefulWidget {
  final ExerciseModel exercise;
  final WorkoutExercise workoutExercise;
  final Function(WorkoutExercise) onUpdate;
  final VoidCallback onRemove;

  const _ExerciseOptionsSheet({
    required this.exercise,
    required this.workoutExercise,
    required this.onUpdate,
    required this.onRemove,
  });

  @override
  State<_ExerciseOptionsSheet> createState() => _ExerciseOptionsSheetState();
}

class _ExerciseOptionsSheetState extends State<_ExerciseOptionsSheet> {
  late int _sets;
  late int _reps;
  late int _duration;
  late double _weight;

  @override
  void initState() {
    super.initState();
    _sets = widget.workoutExercise.sets;
    _reps = widget.workoutExercise.reps;
    _duration = widget.workoutExercise.duration;
    _weight = 0; // TODO: Add weight tracking
  }

  void _updateExercise() {
    widget.onUpdate(WorkoutExercise(
      exerciseId: widget.workoutExercise.exerciseId,
      sets: _sets,
      reps: _reps,
      duration: _duration,
      restTime: widget.workoutExercise.restTime,
      notes: widget.workoutExercise.notes,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final isTimerBased = widget.exercise.isTimerBased || widget.workoutExercise.duration > 0;
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[700],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Title
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              widget.exercise.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Sets
                _buildOptionRow(
                  'Sets',
                  _sets.toString(),
                  onDecrease: _sets > 1 ? () => setState(() => _sets--) : null,
                  onIncrease: _sets < 10 ? () => setState(() => _sets++) : null,
                ),
                const SizedBox(height: 16),
                // Reps or Duration
                if (isTimerBased)
                  _buildOptionRow(
                    'Duration',
                    '${_duration}s',
                    onDecrease: _duration > 10 ? () => setState(() => _duration -= 5) : null,
                    onIncrease: _duration < 300 ? () => setState(() => _duration += 5) : null,
                  )
                else
                  _buildOptionRow(
                    'Reps',
                    _reps.toString(),
                    onDecrease: _reps > 1 ? () => setState(() => _reps--) : null,
                    onIncrease: _reps < 50 ? () => setState(() => _reps++) : null,
                  ),
                const SizedBox(height: 16),
                // Weight
                _buildOptionRow(
                  'Weight',
                  _weight > 0 ? '${_weight.toStringAsFixed(1)} kg' : 'Bodyweight',
                  onDecrease: _weight > 0 ? () => setState(() => _weight -= 2.5) : null,
                  onIncrease: () => setState(() => _weight += 2.5),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          // Actions
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: widget.onRemove,
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Remove Exercise'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _updateExercise,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Update'),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }

  Widget _buildOptionRow(
    String label,
    String value,
    {VoidCallback? onDecrease, VoidCallback? onIncrease}
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        Row(
          children: [
            IconButton(
              onPressed: onDecrease,
              icon: Icon(
                Icons.remove_circle_outline,
                color: onDecrease != null ? Colors.white : Colors.grey[700],
              ),
            ),
            Container(
              constraints: const BoxConstraints(minWidth: 80),
              alignment: Alignment.center,
              child: Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            IconButton(
              onPressed: onIncrease,
              icon: Icon(
                Icons.add_circle_outline,
                color: onIncrease != null ? Colors.white : Colors.grey[700],
              ),
            ),
          ],
        ),
      ],
    );
  }
}