import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/workout_model.dart';
import '../providers/workout_providers.dart';
import '../widgets/common/custom_workout_card.dart';
import '../shared/widgets/state_widgets.dart';
import 'workout_detail_screen.dart';
import 'my_workouts_screen.dart';
import 'create_workout_screen.dart';

// Provider for view mode (grid vs list)
final workoutsViewModeProvider = StateProvider<bool>((ref) => true);

// Provider for search query
final workoutsSearchQueryProvider = StateProvider<String>((ref) => '');

// Provider for category filter
final workoutsCategoryFilterProvider = StateProvider<String>((ref) => 'All');

// Provider for difficulty filter
final workoutsDifficultyFilterProvider = StateProvider<String>((ref) => 'All');

// Provider for filtered workouts based on search and filters
final filteredWorkoutPlansProvider = Provider<List<WorkoutPlanModel>>((ref) {
  final workouts = ref.watch(workoutPlansProvider).asData?.value ?? [];
  final searchQuery = ref.watch(workoutsSearchQueryProvider);
  final categoryFilter = ref.watch(workoutsCategoryFilterProvider);
  final difficultyFilter = ref.watch(workoutsDifficultyFilterProvider);

  List<WorkoutPlanModel> filtered = workouts;

  // Apply search filter
  if (searchQuery.isNotEmpty) {
    filtered = filtered.where((workout) =>
      workout.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
      workout.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
      workout.category.toLowerCase().contains(searchQuery.toLowerCase())
    ).toList();
  }

  // Apply category filter
  if (categoryFilter != 'All') {
    filtered = filtered.where((workout) => 
      workout.category.toLowerCase() == categoryFilter.toLowerCase()
    ).toList();
  }

  // Apply difficulty filter
  if (difficultyFilter != 'All') {
    filtered = filtered.where((workout) => 
      workout.difficulty.toLowerCase() == difficultyFilter.toLowerCase()
    ).toList();
  }

  return filtered;
});

// Provider for available categories
final availableCategoriesProvider = Provider<List<String>>((ref) {
  final workouts = ref.watch(workoutPlansProvider).asData?.value ?? [];
  final categories = workouts.map((w) => w.category).toSet().toList();
  categories.sort();
  return ['All', ...categories];
});

// Provider for available difficulties
final availableDifficultiesProvider = Provider<List<String>>((ref) {
  final workouts = ref.watch(workoutPlansProvider).asData?.value ?? [];
  final difficulties = workouts.map((w) => w.difficulty).toSet().toList();
  final orderedDifficulties = ['Beginner', 'Intermediate', 'Advanced'];
  final availableDifficulties = orderedDifficulties.where((d) => 
    difficulties.any((difficulty) => difficulty.toLowerCase() == d.toLowerCase())
  ).toList();
  return ['All', ...availableDifficulties];
});

class WorkoutsPage extends ConsumerStatefulWidget {
  const WorkoutsPage({super.key});

  @override
  ConsumerState<WorkoutsPage> createState() => _WorkoutsPageState();
}

class _WorkoutsPageState extends ConsumerState<WorkoutsPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _searchController.addListener(() {
      ref.read(workoutsSearchQueryProvider.notifier).state = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search workouts, categories, difficulty...',
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Theme.of(context).colorScheme.primary,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  onPressed: () {
                    _searchController.clear();
                    ref.read(workoutsSearchQueryProvider.notifier).state = '';
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    final categories = ref.watch(availableCategoriesProvider);
    final difficulties = ref.watch(availableDifficultiesProvider);
    final selectedCategory = ref.watch(workoutsCategoryFilterProvider);
    final selectedDifficulty = ref.watch(workoutsDifficultyFilterProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (categories.length > 1) ...[
            Row(
              children: [
                Icon(
                  Icons.category,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Category',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = selectedCategory == category;
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        ref.read(workoutsCategoryFilterProvider.notifier).state = category;
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                      checkmarkColor: Theme.of(context).colorScheme.primary,
                      labelStyle: TextStyle(
                        color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
          ],
          if (difficulties.length > 1) ...[
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  size: 16,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Difficulty',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: difficulties.length,
                itemBuilder: (context, index) {
                  final difficulty = difficulties[index];
                  final isSelected = selectedDifficulty == difficulty;
                  
                  Color getDifficultyColor() {
                    switch (difficulty.toLowerCase()) {
                      case 'beginner':
                        return Theme.of(context).colorScheme.secondary;
                      case 'intermediate':
                        return Theme.of(context).colorScheme.tertiary;
                      case 'advanced':
                        return Theme.of(context).colorScheme.error;
                      default:
                        return Theme.of(context).colorScheme.secondary;
                    }
                  }

                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(difficulty),
                      selected: isSelected,
                      onSelected: (selected) {
                        ref.read(workoutsDifficultyFilterProvider.notifier).state = difficulty;
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: getDifficultyColor().withOpacity(0.2),
                      checkmarkColor: getDifficultyColor(),
                      labelStyle: TextStyle(
                        color: isSelected 
                          ? getDifficultyColor()
                          : Theme.of(context).colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected 
                          ? getDifficultyColor()
                          : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Widget _buildWorkoutGrid(List<WorkoutPlanModel> workouts) {
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final workout = workouts[index];
                return Hero(
                  tag: 'workout_${workout.id}',
                  child: CustomWorkoutCard(
                    workout: workout,
                    showActions: false,
                    onTap: () {
                      Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder: (context, animation, secondaryAnimation) =>
                              WorkoutDetailScreen(workoutPlan: workout),
                          transitionsBuilder: (context, animation, secondaryAnimation, child) {
                            return FadeTransition(opacity: animation, child: child);
                          },
                          transitionDuration: const Duration(milliseconds: 300),
                        ),
                      );
                    },
                  ),
                );
              },
              childCount: workouts.length,
            ),
          ),
        ),
        const SliverPadding(padding: EdgeInsets.only(bottom: 100)),
      ],
    );
  }

  Widget _buildWorkoutList(List<WorkoutPlanModel> workouts) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 100),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Hero(
            tag: 'workout_${workout.id}',
            child: CustomWorkoutCard(
              workout: workout,
              isListView: true,
              showActions: false,
              onTap: () {
                Navigator.push(
                  context,
                  PageRouteBuilder(
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        WorkoutDetailScreen(workoutPlan: workout),
                    transitionsBuilder: (context, animation, secondaryAnimation, child) {
                      return FadeTransition(opacity: animation, child: child);
                    },
                    transitionDuration: const Duration(milliseconds: 300),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsHeader(List<WorkoutPlanModel> workouts) {
    final totalWorkouts = workouts.length;
    final categories = workouts.map((w) => w.category).toSet().length;
    final avgDuration = workouts.isEmpty 
        ? 0 
        : workouts.map((w) => w.duration).reduce((a, b) => a + b) ~/ workouts.length;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.1),
            Theme.of(context).colorScheme.secondary.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.fitness_center,
              value: totalWorkouts.toString(),
              label: 'Total Workouts',
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          Container(
            height: 40,
            width: 1,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.category,
              value: categories.toString(),
              label: 'Categories',
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          Container(
            height: 40,
            width: 1,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.timer,
              value: '${avgDuration}m',
              label: 'Avg Duration',
              color: Theme.of(context).colorScheme.tertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final workoutsAsync = ref.watch(workoutPlansProvider);
    final filteredWorkouts = ref.watch(filteredWorkoutPlansProvider);
    final isGridView = ref.watch(workoutsViewModeProvider);
    final selectedCategory = ref.watch(workoutsCategoryFilterProvider);
    final selectedDifficulty = ref.watch(workoutsDifficultyFilterProvider);

    // Trigger animation when data loads
    ref.listen(workoutPlansProvider, (previous, next) {
      if (next.hasValue && (previous == null || !previous.hasValue)) {
        _animationController.forward();
      }
    });

    // Calculate active filters count
    int activeFiltersCount = 0;
    if (selectedCategory != 'All') activeFiltersCount++;
    if (selectedDifficulty != 'All') activeFiltersCount++;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.fitness_center,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text('Workouts'),
          ],
        ),
        centerTitle: false,
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        actions: [
          // Filters indicator
          if (activeFiltersCount > 0)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () {
                      ref.read(workoutsCategoryFilterProvider.notifier).state = 'All';
                      ref.read(workoutsDifficultyFilterProvider.notifier).state = 'All';
                    },
                    tooltip: 'Clear Filters',
                  ),
                  Positioned(
                    right: 6,
                    top: 6,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        activeFiltersCount.toString(),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onError,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          IconButton(
            icon: Icon(isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              ref.read(workoutsViewModeProvider.notifier).state = !isGridView;
            },
            tooltip: isGridView ? 'List View' : 'Grid View',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(workoutPlansProvider),
            tooltip: 'Refresh',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'my_workouts':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyWorkoutsScreen(),
                    ),
                  );
                  break;
                case 'create_workout':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateWorkoutScreen(),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'my_workouts',
                child: Row(
                  children: [
                    Icon(Icons.person),
                    SizedBox(width: 12),
                    Text('My Workouts'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'create_workout',
                child: Row(
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 12),
                    Text('Create Workout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildFilterChips(),
          Expanded(
            child: AsyncValueWidget(
              value: workoutsAsync,
              onRetry: () => ref.invalidate(workoutPlansProvider),
              data: (workouts) {
                if (workouts.isEmpty) {
                  return Center(
                    child: EmptyState(
                      icon: Icons.fitness_center,
                      title: 'No Workouts Available',
                      message: 'Check back later for new workout plans',
                      onAction: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const CreateWorkoutScreen(),
                          ),
                        );
                      },
                      actionLabel: 'Create Your First Workout',
                    ),
                  );
                }

                if (filteredWorkouts.isEmpty) {
                  return Center(
                    child: EmptyState(
                      icon: Icons.search_off,
                      title: 'No Results Found',
                      message: activeFiltersCount > 0 
                          ? 'Try adjusting your filters or search terms'
                          : 'No workouts match your search',
                      onAction: activeFiltersCount > 0
                          ? () {
                              ref.read(workoutsCategoryFilterProvider.notifier).state = 'All';
                              ref.read(workoutsDifficultyFilterProvider.notifier).state = 'All';
                              _searchController.clear();
                              ref.read(workoutsSearchQueryProvider.notifier).state = '';
                            }
                          : null,
                      actionLabel: activeFiltersCount > 0 ? 'Clear All Filters' : null,
                    ),
                  );
                }

                return Column(
                  children: [
                    _buildStatsHeader(filteredWorkouts),
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () async => ref.invalidate(workoutPlansProvider),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: isGridView
                                ? _buildWorkoutGrid(filteredWorkouts)
                                : _buildWorkoutList(filteredWorkouts),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'workouts_screen_create_fab',
        onPressed: () {
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const CreateWorkoutScreen(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeOutCubic,
                  )),
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 400),
            ),
          );
        },
        icon: const Icon(Icons.add),
        label: const Text('Create'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 8,
      ),
    );
  }
}
