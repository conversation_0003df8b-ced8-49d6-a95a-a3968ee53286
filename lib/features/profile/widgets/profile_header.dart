import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../models/consolidated_user_model_fixed.dart';

class ProfileHeader extends StatelessWidget {
  final User user;
  final ConsolidatedUserModel? userModel;

  const ProfileHeader({
    super.key,
    required this.user,
    this.userModel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Picture
          Stack(
            children: [
              CircleAvatar(
                radius: 50,
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                backgroundImage: user.photoURL != null 
                    ? NetworkImage(user.photoURL!)
                    : null,
                child: user.photoURL == null
                    ? Icon(
                        Icons.person,
                        size: 50,
                        color: Theme.of(context).primaryColor,
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Name
          Text(
            userModel?.personalInfo.name ?? user.displayName ?? 'User',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Email
          Text(
            user.email ?? '',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Info Row
          if (userModel?.personalInfo != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildInfoItem(
                  icon: Icons.cake,
                  label: 'Age',
                  value: _calculateAge(userModel!.personalInfo.dateOfBirth).toString(),
                ),
                _buildInfoItem(
                  icon: Icons.fitness_center,
                  label: 'Goal',
                  value: _getMainGoal(userModel!.fitnessProfile.goals),
                ),
                _buildInfoItem(
                  icon: Icons.schedule,
                  label: 'Weekly',
                  value: '${userModel!.workoutPreferences.workoutsPerWeek}x',
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  int _calculateAge(DateTime? birthDate) {
    if (birthDate == null) return 0;
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  String _getMainGoal(List<FitnessGoal> goals) {
    if (goals.isEmpty) return 'None';
    final mainGoal = goals.first;
    switch (mainGoal.type) {
      case FitnessGoalType.buildMuscle:
        return 'Muscle';
      case FitnessGoalType.weightLoss:
        return 'Weight Loss';
      case FitnessGoalType.increaseStrength:
        return 'Strength';
      case FitnessGoalType.increaseStamina:
        return 'Stamina';
      case FitnessGoalType.healthOptimization:
        return 'Health';
      case FitnessGoalType.sportSpecific:
        return 'Sport';
      default:
        return 'Fitness';
    }
  }
} 