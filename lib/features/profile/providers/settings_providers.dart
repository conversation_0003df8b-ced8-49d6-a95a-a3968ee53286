import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/app_settings_model.dart';
import '../../../services/settings_service.dart';

// Settings service provider
final settingsServiceProvider = Provider<SettingsService>((ref) {
  return SettingsService();
});

// Settings state provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, AsyncValue<AppSettings>>((ref) {
  final settingsService = ref.watch(settingsServiceProvider);
  return SettingsNotifier(settingsService);
});

class SettingsNotifier extends StateNotifier<AsyncValue<AppSettings>> {
  final SettingsService _settingsService;

  SettingsNotifier(this._settingsService) : super(const AsyncValue.loading()) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      state = const AsyncValue.loading();
      final settings = await _settingsService.loadSettings();
      state = AsyncValue.data(settings);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> updateSettings(AppSettings settings) async {
    try {
      await _settingsService.saveSettings(settings);
      state = AsyncValue.data(settings);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> updateSetting<T>(String key, T value) async {
    try {
      await _settingsService.updateSetting(key, value);
      await loadSettings(); // Reload to get updated settings
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
} 