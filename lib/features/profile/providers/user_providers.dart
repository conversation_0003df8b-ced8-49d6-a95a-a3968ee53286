import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/consolidated_user_model_fixed.dart';
import '../../../services/consolidated_user_service_fixed.dart';
import '../../auth/providers/auth_providers.dart';

/// Provider for ConsolidatedUserService
final consolidatedUserServiceProvider = Provider<ConsolidatedUserServiceFixed>((ref) {
  return ConsolidatedUserServiceFixed();
});

/// Provider for current user data
final userDataProvider = FutureProvider<ConsolidatedUserModel?>((ref) async {
  final authUser = ref.watch(currentUserProvider);
  if (authUser == null) return null;
  
  final userService = ref.watch(consolidatedUserServiceProvider);
  return await userService.getUserData();
});

/// State notifier for user profile updates
class UserProfileNotifier extends StateNotifier<AsyncValue<ConsolidatedUserModel?>> {
  final ConsolidatedUserServiceFixed _userService;
  final Ref _ref;
  
  UserProfileNotifier(this._userService, this._ref) : super(const AsyncValue.loading()) {
    _loadUserData();
  }
  
  Future<void> _loadUserData() async {
    state = const AsyncValue.loading();
    try {
      final userData = await _userService.getUserData();
      state = AsyncValue.data(userData);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
  
  Future<void> updateUserProfile(ConsolidatedUserModel updatedUser) async {
    state = const AsyncValue.loading();
    try {
      await _userService.updateUserData(updatedUser);
      state = AsyncValue.data(updatedUser);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
  
  Future<void> updateWorkoutStats(int workoutMinutes, int caloriesBurned) async {
    final currentData = state.valueOrNull;
    if (currentData == null) return;
    
    try {
      // Create new stats object with updated values
      final updatedStats = currentData.stats.copyWith(
        totalWorkouts: currentData.stats.totalWorkouts + 1,
        totalMinutes: currentData.stats.totalMinutes + workoutMinutes,
        totalCaloriesBurned: (currentData.stats.totalCaloriesBurned ?? 0) + caloriesBurned,
        lastWorkoutDate: DateTime.now(),
      );
      
      // Create updated user model with new stats
      final updatedUser = currentData.copyWith(stats: updatedStats);
      
      // Update in backend
      await _userService.updateUserData(updatedUser);
      state = AsyncValue.data(updatedUser);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }
  
  void refresh() {
    _loadUserData();
  }
}

/// Provider for user profile state
final userProfileProvider = StateNotifierProvider<UserProfileNotifier, AsyncValue<ConsolidatedUserModel?>>((ref) {
  final userService = ref.watch(consolidatedUserServiceProvider);
  return UserProfileNotifier(userService, ref);
});