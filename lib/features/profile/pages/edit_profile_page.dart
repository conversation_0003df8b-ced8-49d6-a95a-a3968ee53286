import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/consolidated_user_model_fixed.dart';
import '../../../services/consolidated_user_service_fixed.dart';

class EditProfilePage extends ConsumerStatefulWidget {
  final User user;
  final ConsolidatedUserModel? userModel;

  const EditProfilePage({
    super.key,
    required this.user,
    this.userModel,
  });

  @override
  ConsumerState<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends ConsumerState<EditProfilePage> {
  late TextEditingController _nameController;
  late TextEditingController _ageController;
  late TextEditingController _heightController;
  late TextEditingController _weightController;
  
  String _selectedGender = 'male';
  String _selectedActivityLevel = 'moderate';
  String _selectedUnits = 'imperial';
  List<String> _selectedGoals = [];
  
  bool _isLoading = false;

  final _activityLevels = {
    'sedentary': 'Sedentary (Little or no exercise)',
    'light': 'Light (Exercise 1-3 days/week)',
    'moderate': 'Moderate (Exercise 3-5 days/week)',
    'active': 'Active (Exercise 6-7 days/week)',
    'veryActive': 'Very Active (Hard exercise daily)',
  };

  final _fitnessGoals = {
    'buildMuscle': 'Build Muscle',
    'weightLoss': 'Lose Weight',
    'increaseStrength': 'Increase Strength',
    'increaseStamina': 'Improve Stamina',
    'healthOptimization': 'General Health',
    'sportSpecific': 'Sport Performance',
  };

  @override
  void initState() {
    super.initState();
    final personalInfo = widget.userModel?.personalInfo;
    final fitnessProfile = widget.userModel?.fitnessProfile;
    
    _nameController = TextEditingController(
      text: personalInfo?.name ?? widget.user.displayName ?? '',
    );
    
    // Calculate age from date of birth
    int age = 25; // Default
    if (personalInfo?.dateOfBirth != null) {
      final now = DateTime.now();
      age = now.year - personalInfo!.dateOfBirth!.year;
      if (now.month < personalInfo.dateOfBirth!.month ||
          (now.month == personalInfo.dateOfBirth!.month && 
           now.day < personalInfo.dateOfBirth!.day)) {
        age--;
      }
    }
    _ageController = TextEditingController(text: age.toString());
    
    _heightController = TextEditingController(
      text: personalInfo?.height?.toString() ?? '',
    );
    _weightController = TextEditingController(
      text: personalInfo?.weight?.toString() ?? '',
    );
    
    _selectedGender = personalInfo?.gender.toString().split('.').last ?? 'male';
    _selectedActivityLevel = 'moderate'; // Default activity level
    _selectedUnits = personalInfo?.preferredUnits ?? 'imperial';
    
    _selectedGoals = fitnessProfile?.goals.map((g) => g.type.toString().split('.').last).toList() ?? [];
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (!_validateForm()) return;

    setState(() => _isLoading = true);

    try {
      final service = ConsolidatedUserServiceFixed();
      
      // Create updated user model
      final age = int.tryParse(_ageController.text) ?? 25;
      final birthYear = DateTime.now().year - age;
      
      final currentModel = widget.userModel ?? ConsolidatedUserModel.createDefault(
        uid: widget.user.uid,
        email: widget.user.email ?? '',
      );
      
      // Update the model with new values
      final updatedModel = currentModel.copyWith(
        personalInfo: currentModel.personalInfo.copyWith(
          name: _nameController.text.trim(),
          dateOfBirth: DateTime(birthYear, 1, 1),
          gender: _selectedGender == 'male' ? Gender.male : 
                  _selectedGender == 'female' ? Gender.female : Gender.other,
          height: double.tryParse(_heightController.text) ?? 0,
          weight: double.tryParse(_weightController.text) ?? 0,
          preferredUnits: _selectedUnits,
        ),
        fitnessProfile: currentModel.fitnessProfile.copyWith(
          goals: _selectedGoals.map((g) => FitnessGoal(
            type: FitnessGoalType.values.firstWhere(
              (t) => t.toString().split('.').last == g,
              orElse: () => FitnessGoalType.healthOptimization,
            ),
            priority: 1,
            selectedAt: DateTime.now(),
          )).toList(),
        ),
        preferences: currentModel.preferences.copyWith(
          workoutPreferences: currentModel.preferences.workoutPreferences.copyWith(
            workoutsPerWeek: currentModel.preferences.workoutPreferences.workoutsPerWeek,
            durationMinutes: currentModel.preferences.workoutPreferences.durationMinutes,
            environments: currentModel.preferences.workoutPreferences.environments,
          ),
        ),
      );

      await service.updateUserModel(updatedModel);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  bool _validateForm() {
    if (_nameController.text.trim().isEmpty) {
      _showError('Please enter your name');
      return false;
    }
    if (_ageController.text.isEmpty || int.tryParse(_ageController.text) == null) {
      _showError('Please enter a valid age');
      return false;
    }
    if (_heightController.text.isEmpty || double.tryParse(_heightController.text) == null) {
      _showError('Please enter a valid height');
      return false;
    }
    if (_weightController.text.isEmpty || double.tryParse(_weightController.text) == null) {
      _showError('Please enter a valid weight');
      return false;
    }
    if (_selectedGoals.isEmpty) {
      _showError('Please select at least one fitness goal');
      return false;
    }
    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Personal Information Section
            _buildSectionHeader('Personal Information', Icons.person_outline),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _nameController,
              label: 'Name',
              icon: Icons.person,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _ageController,
                    label: 'Age',
                    icon: Icons.cake,
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDropdownField(
                    label: 'Gender',
                    icon: Icons.wc,
                    value: _selectedGender,
                    items: {
                      'male': 'Male',
                      'female': 'Female',
                      'other': 'Other',
                    },
                    onChanged: (value) {
                      setState(() => _selectedGender = value!);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Physical Stats Section
            _buildSectionHeader('Physical Stats', Icons.straighten),
            const SizedBox(height: 16),
            _buildUnitToggle(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _heightController,
                    label: _selectedUnits == 'imperial' ? 'Height (in)' : 'Height (cm)',
                    icon: Icons.height,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTextField(
                    controller: _weightController,
                    label: _selectedUnits == 'imperial' ? 'Weight (lbs)' : 'Weight (kg)',
                    icon: Icons.monitor_weight,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Fitness Profile Section
            _buildSectionHeader('Fitness Profile', Icons.fitness_center),
            const SizedBox(height: 16),
            _buildDropdownField(
              label: 'Activity Level',
              icon: Icons.trending_up,
              value: _selectedActivityLevel,
              items: _activityLevels,
              onChanged: (value) {
                setState(() => _selectedActivityLevel = value!);
              },
            ),
            
            const SizedBox(height: 32),
            
            // Fitness Goals Section
            _buildSectionHeader('Fitness Goals', Icons.flag),
            const SizedBox(height: 8),
            Text(
              'Select all that apply',
              style: TextStyle(
                color: theme.colorScheme.onBackground.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            _buildGoalSelection(),
            
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
  }) {
    final theme = Theme.of(context);
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: theme.colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required IconData icon,
    required String value,
    required Map<String, String> items,
    required Function(String?) onChanged,
  }) {
    final theme = Theme.of(context);
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: theme.colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
      ),
      items: items.entries.map((entry) {
        return DropdownMenuItem(
          value: entry.key,
          child: Text(entry.value),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildUnitToggle() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedUnits = 'imperial'),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedUnits == 'imperial'
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Imperial',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: _selectedUnits == 'imperial'
                        ? Colors.white
                        : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedUnits = 'metric'),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedUnits == 'metric'
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Metric',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: _selectedUnits == 'metric'
                        ? Colors.white
                        : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalSelection() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _fitnessGoals.entries.map((entry) {
        final isSelected = _selectedGoals.contains(entry.key);
        return FilterChip(
          label: Text(entry.value),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedGoals.add(entry.key);
              } else {
                _selectedGoals.remove(entry.key);
              }
            });
          },
          selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          checkmarkColor: Theme.of(context).colorScheme.primary,
          backgroundColor: Theme.of(context).colorScheme.surface,
          side: BorderSide(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        );
      }).toList(),
    );
  }
}