import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/workout_model.dart';
import '../../../models/exercise_model.dart';
import '../../../services/firestore_service.dart';
import '../../../services/custom_workout_service.dart';
import '../../auth/providers/auth_providers.dart';

/// Provider for FirestoreService
final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  return FirestoreService();
});

/// Provider for CustomWorkoutService
final customWorkoutServiceProvider = Provider<CustomWorkoutService>((ref) {
  return CustomWorkoutService();
});

/// Provider for all exercises
final exercisesProvider = FutureProvider<List<ExerciseModel>>((ref) async {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return await firestoreService.getAllExercises();
});

/// Provider for all workout plans
final workoutPlansProvider = FutureProvider<List<WorkoutPlanModel>>((ref) async {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return await firestoreService.getAllWorkoutPlans();
});

/// Provider for user's custom workouts
final customWorkoutsProvider = FutureProvider<List<WorkoutPlanModel>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  
  final customWorkoutService = ref.watch(customWorkoutServiceProvider);
  return await customWorkoutService.getUserCustomWorkouts(user.uid);
});

/// Provider for user's workout history
final workoutHistoryProvider = FutureProvider.family<List<WorkoutSessionModel>, String>(
  (ref, userId) async {
    final firestoreService = ref.watch(firestoreServiceProvider);
    return await firestoreService.getUserWorkoutHistory(userId);
  },
);

/// Provider for recent workouts
final recentWorkoutsProvider = FutureProvider<List<WorkoutSessionModel>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  
  final firestoreService = ref.watch(firestoreServiceProvider);
  return await firestoreService.getRecentWorkouts(user.uid);
});

/// Provider for user's workout plans (custom workouts)
final userWorkoutsProvider = FutureProvider<List<WorkoutPlanModel>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];
  
  final firestoreService = ref.watch(firestoreServiceProvider);
  return await firestoreService.getCustomWorkoutPlans(user.uid);
});

/// State notifier for current workout session
class WorkoutSessionNotifier extends StateNotifier<WorkoutSessionModel?> {
  WorkoutSessionNotifier() : super(null);
  
  void startSession(WorkoutSessionModel session) {
    state = session;
  }
  
  void updateSession(WorkoutSessionModel session) {
    state = session;
  }
  
  void endSession() {
    state = null;
  }
}

/// Provider for current workout session
final workoutSessionProvider = StateNotifierProvider<WorkoutSessionNotifier, WorkoutSessionModel?>((ref) {
  return WorkoutSessionNotifier();
});