# Security Update - API Key Management

## Important Security Changes

We have removed hardcoded API keys from the codebase for security reasons. The Google AI API key that was previously in the `.env` file has been removed.

## For Developers

### Setting Up Your Development Environment

1. **Get your own API key**:
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key for your project

2. **Configure your local environment**:
   
   Option A - Using .env file (Development only):
   ```bash
   # Copy the example env file
   cp .env.example .env
   
   # Edit .env and add your API key
   # Replace YOUR_API_KEY_HERE with your actual key
   ```

   Option B - Using secure storage (Recommended):
   - The app now uses `flutter_secure_storage` for encrypted storage
   - On first run, you'll be prompted to enter your API key
   - The key will be stored securely on the device

3. **Run flutter pub get** to install the new dependencies:
   ```bash
   flutter pub get
   ```

## For Production

For production deployments, you should:

1. **Use Firebase Remote Config**:
   - Store API keys in Firebase Remote Config
   - Fetch them at runtime
   - This allows key rotation without app updates

2. **Use Environment Variables**:
   - Set API keys as environment variables on your CI/CD platform
   - Inject them during the build process

3. **Never commit API keys**:
   - The `.env` file is gitignored
   - Never bypass this protection
   - Use `.env.example` as a template

## Security Best Practices

1. **Rotate keys regularly**
2. **Use different keys for development and production**
3. **Monitor API key usage in Google Cloud Console**
4. **Set up API key restrictions** (IP, referrer, etc.)
5. **Use least privilege principle** - only grant necessary permissions

## Affected Files

- `.env` - API key removed
- `.env.example` - Created as template
- `lib/core/constants/app_config.dart` - Configuration constants
- `lib/core/utils/secure_config.dart` - Secure storage manager
- `pubspec.yaml` - Added flutter_secure_storage dependency

## Migration Steps

If you had the old API key saved:
1. Keep it secure and don't share it
2. Consider rotating it if it was exposed
3. Update your local `.env` file with the key
4. Or use the secure storage option in the app

## Questions?

If you have any questions about these security changes, please contact the development team.