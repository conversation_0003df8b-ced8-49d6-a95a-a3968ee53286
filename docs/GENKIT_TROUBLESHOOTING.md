# GenKit Function Troubleshooting Guide

## 🚨 Current Issue: 403 Forbidden Error

Your GenKit function is returning a **403 Forbidden** error, which means the request is being rejected. Here's how to fix it:

## 🔧 Quick Fixes

### 1. **Check GenKit Function Deployment**
Your function URL: `https://generateandsaveworkout-hidgimzz2q-uc.a.run.app`

**Verify deployment:**
```bash
# Check if your function is properly deployed
gcloud functions list --filter="name:generateandsaveworkout"

# Or check Cloud Run services
gcloud run services list --filter="name:generateandsaveworkout"
```

### 2. **Update Function Permissions**
The 403 error usually means the function needs proper authentication/permissions.

**For Cloud Functions:**
```bash
# Allow unauthenticated access (if appropriate)
gcloud functions add-iam-policy-binding generateandsaveworkout \
    --member="allUsers" \
    --role="roles/cloudfunctions.invoker"
```

**For Cloud Run:**
```bash
# Allow unauthenticated access
gcloud run services add-iam-policy-binding generateandsaveworkout \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --region=us-central1
```

### 3. **Check Function Configuration**
Your GenKit function might expect a specific:
- **HTTP Method**: POST vs GET
- **Endpoint Path**: `/generateandsaveworkout` vs `/`
- **Request Format**: `{data: {...}}` vs `{...}`

## 🧪 Debug Steps

### Step 1: Run Debug Analysis
In your app, go to the GenKit Test Page and click **"Debug Analysis"**. This will:
- Test connectivity
- Try different endpoints
- Test various request formats
- Check headers and CORS

### Step 2: Check Console Logs
After running debug analysis, check your Flutter console for detailed information about:
- Which endpoints respond
- What status codes are returned
- What the actual error messages are

### Step 3: Test Manually
You can test your function manually using curl:

```bash
# Test basic connectivity
curl -X GET "https://generateandsaveworkout-hidgimzz2q-uc.a.run.app"

# Test POST with data
curl -X POST "https://generateandsaveworkout-hidgimzz2q-uc.a.run.app" \
  -H "Content-Type: application/json" \
  -d '{"data": {"message": "test"}}'

# Test the specific function endpoint
curl -X POST "https://generateandsaveworkout-hidgimzz2q-uc.a.run.app/generateandsaveworkout" \
  -H "Content-Type: application/json" \
  -d '{"workoutType": "full_body", "durationMinutes": 30}'
```

## 🛠️ Likely Solutions

### Solution 1: **Function Not Public**
Your function might not allow public access. You need to:

1. **Go to Google Cloud Console**
2. **Navigate to Cloud Functions** (or Cloud Run)
3. **Find your function**: `generateandsaveworkout`
4. **Check permissions**: Make sure "allUsers" has the "Cloud Functions Invoker" role

### Solution 2: **Wrong Endpoint**
Your function might be at a different path:

**Try these URLs in the debug test:**
- `https://generateandsaveworkout-hidgimzz2q-uc.a.run.app/generateandsaveworkout`
- `https://generateandsaveworkout-hidgimzz2q-uc.a.run.app/api/generateandsaveworkout`
- `https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net/generateandsaveworkout`

### Solution 3: **CORS Configuration**
If it's a CORS issue, your GenKit function needs to handle preflight requests:

```javascript
// In your GenKit function
export const generateandsaveworkout = onRequest({cors: true}, async (req, res) => {
  // Your function code
});
```

### Solution 4: **Authentication Required**
If your function requires authentication, you need to:

1. **Get an ID token** from Firebase Auth
2. **Send it in the Authorization header**:

```dart
// In your GenKit service
headers: {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer $idToken',
}
```

## 📱 Testing in Your App

### 1. **Use the Enhanced Debug Features**
The app now has improved debugging:
- Multiple endpoint testing
- Different request format attempts
- Detailed console logging

### 2. **Check Request/Response Format**
Look for these in the console logs:
- What request format works
- What response format is returned
- Any specific error messages

### 3. **Test Connection First**
Before testing workout generation, use the "Test Connection" button to verify basic connectivity.

## 🔍 Common GenKit Issues

### Issue: "Function Not Found"
- **Solution**: Check the function name and URL
- **Fix**: Verify deployment with `gcloud functions list`

### Issue: "CORS Error"
- **Solution**: Enable CORS in your GenKit function
- **Fix**: Add `cors: true` to your function configuration

### Issue: "Authentication Required"
- **Solution**: Either make function public or add authentication
- **Fix**: Set IAM permissions or add auth headers

### Issue: "Wrong Request Format"
- **Solution**: GenKit expects specific request structure
- **Fix**: Check your GenKit function code for expected input format

## ✅ Success Indicators

You'll know it's working when:
- ✅ Debug analysis shows 200 status codes
- ✅ Test connection succeeds
- ✅ Workout generation returns actual workout data
- ✅ Chat integration routes workout requests to GenKit

## 🆘 Still Having Issues?

If you're still getting 403 errors:

1. **Share the debug analysis results** from the console
2. **Check your GenKit function logs** in Google Cloud Console
3. **Verify the function is actually deployed** and running
4. **Try the manual curl commands** to isolate the issue

The debug analysis in your app will give you the exact information needed to fix the issue! 🚀