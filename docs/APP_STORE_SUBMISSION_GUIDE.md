# App Store Submission Guide for Agentic Fit

## Pre-Submission Checklist

### 1. App Information
- [ ] **App Name**: Currently "DreamFlow" - Update to "Agentic Fit"
- [ ] **Bundle ID/Package Name**: 
  - iOS: `com.ben25.openfitt` (needs updating)
  - Android: `com.mycompany.WorkoutTracker` (needs updating)
- [ ] **Version**: Currently 1.0.0
- [ ] **App Icon**: Ensure you have all required sizes
- [ ] **App Description**: Prepare compelling description
- [ ] **Screenshots**: Prepare for all required device sizes
- [ ] **Privacy Policy URL**: Required for both stores
- [ ] **Terms of Service URL**: Recommended

## iOS App Store Submission

### Prerequisites
1. **Apple Developer Account** ($99/year)
2. **Xcode** (latest version)
3. **Valid provisioning profiles and certificates**

### Step 1: Update iOS Configuration

```bash
# Update bundle identifier
cd ios
open Runner.xcworkspace
```

In Xcode:
1. Select Runner project
2. Go to "Signing & Capabilities"
3. Update Bundle Identifier to something like `com.yourcompany.agenticfit`
4. Enable "Automatically manage signing"
5. Select your team

### Step 2: Update App Information

Update `ios/Runner/Info.plist`:
```xml
<key>CFBundleDisplayName</key>
<string>Agentic Fit</string>
<key>CFBundleName</key>
<string>Agentic Fit</string>
```

Add required permissions:
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to take workout progress photos</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs photo library access to select workout images</string>
<key>NSMotionUsageDescription</key>
<string>This app uses motion data to track your workouts</string>
```

### Step 3: Generate App Icons

Create `ios/Runner/Assets.xcassets/AppIcon.appiconset/` with:
- 20x20 (2x, 3x)
- 29x29 (2x, 3x)
- 40x40 (2x, 3x)
- 60x60 (2x, 3x)
- 1024x1024 (App Store)

### Step 4: Build and Archive

```bash
# Clean and build
flutter clean
flutter pub get
cd ios
pod install
cd ..

# Build release version
flutter build ios --release

# Open in Xcode
open ios/Runner.xcworkspace
```

In Xcode:
1. Select "Any iOS Device" as target
2. Product → Archive
3. Window → Organizer
4. Select archive → Distribute App
5. App Store Connect → Upload

### Step 5: App Store Connect

1. Go to [App Store Connect](https://appstoreconnect.apple.com)
2. Create new app
3. Fill in:
   - App name
   - Primary language
   - Bundle ID
   - SKU (unique identifier)
4. Add app information:
   - Description
   - Keywords
   - Support URL
   - Marketing URL
   - Screenshots (6.7", 6.5", 5.5", iPad)
   - App preview videos (optional)
5. Set pricing and availability
6. Submit for review

## Android Google Play Store Submission

### Prerequisites
1. **Google Play Developer Account** ($25 one-time)
2. **Android Studio**
3. **Signing key**

### Step 1: Update Android Configuration

Update `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        applicationId "com.yourcompany.agenticfit"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }
}
```

### Step 2: Create Signing Key

```bash
# Generate keystore
keytool -genkey -v -keystore ~/agentic-fit-release.keystore \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -alias agentic-fit
```

Create `android/key.properties`:
```properties
storePassword=<your-store-password>
keyPassword=<your-key-password>
keyAlias=agentic-fit
storeFile=/Users/<USER>/agentic-fit-release.keystore
```

Update `android/app/build.gradle`:
```gradle
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### Step 3: Build Release APK/AAB

```bash
# Clean build
flutter clean
flutter pub get

# Build App Bundle (recommended)
flutter build appbundle --release

# Or build APK
flutter build apk --release
```

### Step 4: Google Play Console

1. Go to [Google Play Console](https://play.google.com/console)
2. Create application
3. Fill in:
   - App name
   - Default language
   - App or game
   - Free or paid
4. Complete store listing:
   - Short description (80 chars)
   - Full description (4000 chars)
   - Screenshots (phone, 7" tablet, 10" tablet)
   - Feature graphic (1024x500)
   - Icon (512x512)
5. Content rating questionnaire
6. Set up pricing and distribution
7. Upload AAB/APK to production track
8. Submit for review

## Quick Commands Script

Create `scripts/prepare_release.sh`:
```bash
#!/bin/bash

echo "🚀 Preparing Agentic Fit for release..."

# Update version
echo "Current version in pubspec.yaml:"
grep "version:" pubspec.yaml

read -p "Enter new version (e.g., 1.0.1+2): " VERSION
sed -i '' "s/version: .*/version: $VERSION/" pubspec.yaml

# Clean and get dependencies
flutter clean
flutter pub get

# iOS
echo "📱 Building iOS..."
cd ios
pod install
cd ..
flutter build ios --release

# Android
echo "🤖 Building Android..."
flutter build appbundle --release

echo "✅ Build complete!"
echo "iOS: ios/build/ios/iphoneos/Runner.app"
echo "Android: build/app/outputs/bundle/release/app-release.aab"
```

## Important Considerations

### 1. Firebase Configuration
- Ensure Firebase project is properly configured
- Update `google-services.json` (Android)
- Update `GoogleService-Info.plist` (iOS)
- Set up proper security rules

### 2. App Permissions
- Camera (for workout photos)
- Photo library
- Internet
- Push notifications (if implemented)

### 3. Testing
- Test on real devices
- Test all Firebase features
- Test in-app purchases (if any)
- Test on different screen sizes

### 4. Legal Requirements
- Privacy policy (required)
- Terms of service
- GDPR compliance
- Data deletion policy

### 5. App Store Optimization (ASO)
- Research keywords
- Compelling screenshots
- Clear app description
- Localization (if targeting multiple regions)

## Common Rejection Reasons

### iOS
- Incomplete app (crashes, placeholder content)
- Broken links
- Missing privacy policy
- Inappropriate content
- Performance issues
- Guideline violations

### Android
- Policy violations
- Malware/security issues
- Impersonation
- Misleading claims
- Copyright infringement

## Timeline
- iOS: 24-48 hours for review (can be up to 7 days)
- Android: 2-3 hours for review (can be up to 24 hours)

## Post-Launch
1. Monitor crash reports
2. Respond to user reviews
3. Regular updates
4. A/B testing for store listing
5. Implement user feedback

## Resources
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)
- [Flutter Deployment Docs](https://docs.flutter.dev/deployment) 