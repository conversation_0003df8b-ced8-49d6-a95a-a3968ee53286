# App Store Upload Steps for Agentic Fit

## Prerequisites Completed ✅
- App ID created: `com.abenezernuro.agenticfit`
- Development Team: HP284BJ924
- iOS app built in release mode

## Step 1: Open Xcode and Archive

Once the Flutter build completes, open Xcode:

```bash
open ios/Runner.xcworkspace
```

## Step 2: Configure Signing in Xcode

1. Select the **Runner** project in the navigator
2. Select the **Runner** target
3. Go to **Signing & Capabilities** tab
4. Ensure:
   - ✅ Automatically manage signing is checked
   - Team: Your Apple Developer Team (HP284BJ924)
   - Bundle Identifier: `com.abenezernuro.agenticfit`

## Step 3: Update Version and Build Number

In Xcode:
1. Select **Runner** target → **General** tab
2. Set:
   - Version: 1.0.0
   - Build: 1

## Step 4: Create Archive

1. Select **Any iOS Device (arm64)** as the destination (top toolbar)
2. Menu: **Product** → **Archive**
3. Wait for the archive process to complete (5-10 minutes)

## Step 5: Upload to App Store Connect

When archiving completes, the Organizer window opens:

1. Select your archive
2. Click **Distribute App**
3. Select **App Store Connect** → **Next**
4. Select **Upload** → **Next**
5. Options:
   - ✅ Include bitcode for iOS content
   - ✅ Upload your app's symbols
   - Click **Next**
6. Select certificate and profile → **Next**
7. Review and click **Upload**

## Step 6: Wait for Processing

After upload:
- Processing takes 5-30 minutes
- You'll receive an email when processing completes
- Check status in App Store Connect

## Step 7: Complete App Store Connect Submission

1. Go to [App Store Connect](https://appstoreconnect.apple.com)
2. Select your app
3. Under **1.0 Prepare for Submission**:
   - Select the build that just processed
   - Fill in all required fields
   - Upload screenshots
   - Submit for review

## Common Issues and Solutions

### Issue: "No suitable application records found"
**Solution**: Create the app in App Store Connect first

### Issue: "Invalid Bundle ID"
**Solution**: Ensure Bundle ID in Xcode matches App Store Connect exactly

### Issue: "Missing Info.plist keys"
**Solution**: Add required permission descriptions:
```xml
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to take workout progress photos</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs photo library access to select workout images</string>
```

### Issue: "Archive not showing in Organizer"
**Solution**: 
- Ensure you selected "Any iOS Device" not a simulator
- Check build succeeded without errors
- Try Product → Clean Build Folder, then archive again

## Upload Checklist

Before uploading, ensure:
- [ ] App icons are set (all sizes)
- [ ] Launch screen configured
- [ ] Version and build number updated
- [ ] Signing configured correctly
- [ ] All capabilities match App ID
- [ ] Info.plist permissions added
- [ ] No debug code or print statements
- [ ] Firebase configured for production

## Alternative Upload Methods

### Using Transporter App
1. Download [Transporter](https://apps.apple.com/us/app/transporter/id1450874784) from Mac App Store
2. Export IPA from Xcode Organizer
3. Drag IPA to Transporter
4. Sign in and upload

### Using Command Line (xcrun)
```bash
xcrun altool --upload-app -f path/to/app.ipa -t ios -u YOUR_APPLE_ID -p YOUR_APP_SPECIFIC_PASSWORD
```

## After Upload

1. **Processing**: 5-30 minutes
2. **Review Time**: 24-48 hours typically
3. **Monitor**: Check email and App Store Connect for updates
4. **Respond Quickly**: If reviewer has questions

## Need Help?

- [App Store Connect Help](https://help.apple.com/app-store-connect/)
- [Xcode Upload Documentation](https://developer.apple.com/documentation/xcode/uploading-your-app-to-app-store-connect)
- [Common App Store Rejections](https://developer.apple.com/app-store/review/) 