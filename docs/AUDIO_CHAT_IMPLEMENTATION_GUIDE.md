# Audio Chat Implementation Guide for AgenticFit

## Overview

This guide explains how to implement bidirectional voice conversations in your fitness app using Firebase AI's Gemini Live API. The implementation allows users to have natural voice conversations with the AI fitness assistant.

## Architecture

### Components

1. **FirebaseAIAudioChatService** (`lib/services/firebase_ai_audio_chat_service.dart`)
   - Manages both text and voice chat functionality
   - Handles audio recording and playback
   - Integrates with Gemini AI for responses

2. **AIAudioChatScreen** (`lib/screens/ai_audio_chat_screen.dart`)
   - Enhanced UI with voice chat capabilities
   - Visual feedback for active conversations
   - Seamless switching between text and voice

## Current Implementation Status

### ✅ Completed
- Basic service structure for audio chat
- UI with voice chat button and visual indicators
- Audio recording setup using `record` package
- Audio playback setup using `flutter_soloud` package
- Text chat functionality (already working)
- Animations for voice conversation state

### 🚧 Needs Implementation
- Firebase AI Live Session integration
- Speech-to-text conversion
- Text-to-speech for AI responses
- Proper audio streaming to/from Gemini Live

## Required Dependencies

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  # Existing dependencies...
  
  # Audio packages
  record: ^5.1.2
  flutter_soloud: ^2.1.3
  
  # Firebase AI (when available)
  # firebase_ai: ^2.0.0  # Uncomment when package is available
```

## Implementation Steps

### 1. Enable Firebase AI with Gemini Live

First, you need to set up Firebase AI with Gemini Live support:

```dart
// In firebase_ai_audio_chat_service.dart
import 'package:firebase_ai/firebase_ai.dart';

Future<void> _initializeLiveChat() async {
  // Initialize Firebase AI with Gemini Live
  final liveModel = FirebaseAI.vertexAI().liveGenerativeModel(
    systemInstruction: Content.text('''
      You are a friendly and helpful AI fitness assistant. 
      Help users with workout plans, nutrition advice, and fitness goals.
      Keep responses concise and conversational for voice chat.
    '''),
    model: 'gemini-2.0-flash-live-preview',
    liveGenerationConfig: LiveGenerationConfig(
      speechConfig: SpeechConfig(
        voiceName: 'fenrir', // or another voice
      ),
      responseModalities: [ResponseModalities.audio],
    ),
  );
  
  _liveSession = await liveModel.connect();
}
```

### 2. Update Audio Processing

Replace the placeholder `_processAudioInput` method:

```dart
Future<void> _processAudioInput() async {
  if (_inputStream == null || _liveSession == null) return;

  // Convert audio stream to InlineDataPart stream for Gemini
  Stream<InlineDataPart> inlineDataStream = _inputStream!.map((data) {
    return InlineDataPart('audio/pcm', data);
  });
  
  // Send audio stream to Gemini Live
  _liveSession!.sendMediaStream(inlineDataStream);
  
  // Process responses
  _processLiveResponses();
}

Future<void> _processLiveResponses() async {
  await for (final response in _liveSession!.receive()) {
    if (response.message is LiveServerContent) {
      await _handleLiveContent(response.message as LiveServerContent);
    }
  }
}

Future<void> _handleLiveContent(LiveServerContent content) async {
  final parts = content.modelTurn?.parts;
  if (parts != null) {
    for (final part in parts) {
      if (part is TextPart) {
        // Add text to chat messages
        final message = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          text: part.text,
          isUser: false,
          timestamp: DateTime.now(),
        );
        _messages.add(message);
        notifyListeners();
      } else if (part is InlineDataPart && part.mimeType.startsWith('audio')) {
        // Play audio response
        if (_audioSource != null) {
          SoLoud.instance.addAudioDataStream(_audioSource!, part.bytes);
        }
      }
    }
  }
}
```

### 3. Platform-Specific Setup

#### iOS Setup

Add to `ios/Runner/Info.plist`:

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for voice chat with your AI fitness assistant</string>
<key>NSSpeechRecognitionUsageDescription</key>
<string>This app uses speech recognition to understand your voice commands</string>
```

#### Android Setup

Add to `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

### 4. Enhanced User Experience Features

#### Voice Activity Detection
```dart
// Add voice activity detection to show when user is speaking
StreamController<bool> _voiceActivityController = StreamController<bool>.broadcast();

void _detectVoiceActivity(Uint8List audioData) {
  // Simple volume-based detection
  double sum = 0;
  for (int i = 0; i < audioData.length; i += 2) {
    int sample = audioData[i] | (audioData[i + 1] << 8);
    if (sample > 32767) sample -= 65536;
    sum += sample.abs();
  }
  double average = sum / (audioData.length / 2);
  bool isActive = average > 1000; // Threshold
  _voiceActivityController.add(isActive);
}
```

#### Conversation History
```dart
// Save voice conversations with transcriptions
Future<void> _saveConversation() async {
  final user = FirebaseAuth.instance.currentUser;
  if (user != null) {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('voice_conversations')
        .add({
      'messages': _messages.map((m) => {
        'text': m.text,
        'isUser': m.isUser,
        'timestamp': m.timestamp,
      }).toList(),
      'createdAt': FieldValue.serverTimestamp(),
    });
  }
}
```

## Usage Example

```dart
// In your screen or widget
class MyFitnessScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AIAudioChatScreen(),
              ),
            );
          },
          child: const Text('Chat with AI Assistant'),
        ),
      ),
    );
  }
}
```

## Testing

1. **Test Audio Permissions**
   ```dart
   // Check if permissions are granted
   final hasPermission = await _recorder.hasPermission();
   ```

2. **Test Audio Recording**
   ```dart
   // Record a test audio clip
   await _recorder.start();
   await Future.delayed(Duration(seconds: 3));
   final path = await _recorder.stop();
   ```

3. **Test Audio Playback**
   ```dart
   // Play a test sound
   final testData = Uint8List.fromList([/* PCM data */]);
   simulateAudioResponse(testData);
   ```

## Troubleshooting

### Common Issues

1. **"firebase_ai package not found"**
   - The package might not be publicly available yet
   - Use the Google Generative AI package as a fallback

2. **Audio not recording**
   - Check microphone permissions
   - Ensure audio configuration matches device capabilities

3. **No audio playback**
   - Verify SoLoud initialization
   - Check audio format compatibility (24kHz, mono, PCM16)

4. **Gemini Live connection fails**
   - Verify Firebase AI is enabled in your project
   - Check API quotas and limits
   - Ensure proper authentication

## Future Enhancements

1. **Offline Support**
   - Cache common responses
   - Use on-device speech recognition

2. **Multi-language Support**
   - Add language detection
   - Support multiple TTS voices

3. **Advanced Features**
   - Background listening mode
   - Wake word detection ("Hey Fitness")
   - Conversation context persistence

4. **Analytics**
   - Track voice interaction metrics
   - Analyze conversation patterns
   - User satisfaction feedback

## Security Considerations

1. **Audio Data Privacy**
   - Don't store raw audio without user consent
   - Encrypt audio streams in transit
   - Clear audio buffers after use

2. **API Key Security**
   - Never expose API keys in client code
   - Use Firebase Security Rules
   - Implement rate limiting

## Performance Optimization

1. **Audio Buffer Management**
   ```dart
   // Optimize buffer size for low latency
   const bufferSize = 4096; // Adjust based on testing
   ```

2. **Memory Management**
   ```dart
   // Clean up resources when not in use
   @override
   void dispose() {
     _recorder.dispose();
     _audioSource?.dispose();
     super.dispose();
   }
   ```

3. **Network Optimization**
   - Use adaptive bitrate for audio
   - Implement connection quality detection
   - Add offline queue for messages

## Conclusion

This implementation provides a foundation for bidirectional voice conversations in your fitness app. The key is to properly integrate with Firebase AI's Gemini Live API once it becomes available, while maintaining a smooth user experience with proper audio handling and visual feedback. 