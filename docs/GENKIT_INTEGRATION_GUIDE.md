# GenKit Integration Guide

Your deployed GenKit function is now integrated with your Flutter fitness app! 🚀

## ✅ What's Been Implemented

### 1. GenKit Workout Service (`lib/services/genkit_workout_service.dart`)
- **Purpose**: Connects to your deployed GenKit function
- **URL**: `https://generateandsaveworkout-hidgimzz2q-uc.a.run.app`
- **Functions**:
  - `generateWorkout()` - Custom workout generation
  - `generateQuickWorkout()` - Fast generation with user preferences
  - `generatePersonalizedWorkout()` - Uses complete user profile
  - `generateWorkoutFromChat()` - Natural language requests

### 2. Enhanced Chat Integration
- **Smart Routing**: Automatically detects workout generation requests
- **Keywords**: "create workout", "generate workout", "make me a workout"
- **Fallback**: Uses enhanced responses when GenKit is unavailable

### 3. Test Interface (`lib/pages/genkit_test_page.dart`)
- **Features**:
  - Connection testing
  - Workout generation forms
  - Chat interface
  - Quick test buttons

### 4. UI Components (`lib/widgets/genkit_workout_generator.dart`)
- **User-friendly Forms**: Dropdown selections for workout parameters
- **Real-time Feedback**: Loading states and error handling
- **Personalization**: Uses user profile data

## 🚀 How to Use

### Option 1: Replace Home Page (Recommended for Testing)
In your `main.dart` or routing, replace:
```dart
import 'pages/home_page.dart';
// Use: WorkoutHomePage()
```

With:
```dart
import 'pages/home_page_with_genkit.dart';
// Use: HomePageWithGenKit()
```

### Option 2: Add to Navigation Menu
Add this to your drawer or navigation:
```dart
ListTile(
  leading: const Icon(Icons.psychology),
  title: const Text('GenKit AI Test'),
  onTap: () => Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => const GenKitTestPage()),
  ),
),
```

### Option 3: Direct Integration in Chat
Your chat is already enhanced! Try saying:
- "Create me a 30-minute workout"
- "Generate a strength training routine"
- "Make me a cardio workout"

## 🎯 GenKit Function Integration

### Request Format
Your GenKit function receives:
```json
{
  "workoutType": "full_body",
  "durationMinutes": 30,
  "fitnessLevel": "intermediate",
  "equipment": ["dumbbells", "bodyweight"],
  "targetMuscleGroups": ["full_body"],
  "userProfile": {
    "profile": { "name": "...", "age": 25, ... },
    "fitnessGoals": [...],
    "fitnessLevel": {...},
    "workoutPreferences": {...}
  },
  "userId": "user123"
}
```

### Response Format
Expected response from your GenKit function:
```json
{
  "workoutPlan": {...},
  "exercises": [...],
  "message": "Generated a 30-minute full body workout!",
  "success": true
}
```

## 🔧 Customization

### Modify GenKit Service
Edit `lib/services/genkit_workout_service.dart`:
- Change request parameters
- Update user context data
- Modify response handling

### Update Chat Keywords
Edit `lib/services/gemini_chat_service.dart` in `_shouldUseGenKit()`:
```dart
bool _shouldUseGenKit(String message) {
  final lowerMessage = message.toLowerCase();
  return lowerMessage.contains('your_keyword') ||
         lowerMessage.contains('another_keyword');
}
```

### GenKit Function URL
Update the URL in `genkit_workout_service.dart`:
```dart
static const String _baseUrl = 'YOUR_NEW_GENKIT_URL';
```

## 🧪 Testing

### Test Connection
1. Open the GenKit Test Page
2. Click "Test GenKit Connection"
3. Should show ✅ success or ❌ error

### Test Workout Generation
1. Use the form to set parameters
2. Click "Generate Workout"
3. Check the response

### Test Chat Integration
1. In the chat interface, type: "Create me a workout"
2. Should route to GenKit function
3. Fallback to regular chat if GenKit fails

## 🚨 Troubleshooting

### Connection Issues
- Check if GenKit function is deployed and running
- Verify the URL is correct
- Check CORS settings on your GenKit function

### Authentication Errors
- Ensure user is logged in
- Check Firebase Auth tokens
- Verify user profile data exists

### Response Issues
- Check GenKit function logs
- Verify response format matches expected structure
- Add logging in `genkit_workout_service.dart`

## 📱 Production Deployment

### Before Going Live:
1. **Test thoroughly** with the GenKit Test Page
2. **Remove debug prints** from production code
3. **Add error monitoring** for GenKit calls
4. **Set up fallbacks** for when GenKit is unavailable
5. **Configure CORS** properly on your GenKit function

### Performance Considerations:
- GenKit calls have 30-second timeout
- Implement caching for repeated requests
- Show loading states for better UX
- Handle network failures gracefully

## 🎉 Success!

Your app now has AI-powered workout generation using your deployed GenKit function! The integration is complete and ready for testing.

**Next Steps:**
1. Test the integration using the GenKit Test Page
2. Customize the parameters based on your GenKit function's API
3. Deploy to production once thoroughly tested

Your users can now get personalized, AI-generated workouts powered by your GenKit function! 🏋️‍♂️🤖