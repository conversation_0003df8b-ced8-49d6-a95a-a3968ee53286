# Firebase AI Logic Implementation Guide

## Overview
This guide outlines how to implement the Firebase AI Logic service to match the n8n chat flow functionality.

## Key Components from n8n Flow

### 1. System Architecture
The n8n flow uses a sophisticated multi-step process:
1. Fetches user context (preferences, workout history, fitness guide)
2. Aggregates all data before AI processing
3. Uses tools for calculations, research, and workout updates
4. Maintains conversation context with memory

### 2. Data Requirements

#### User Context Data
```typescript
interface UserContext {
  user_preferences: {
    primarygoal: string;
    fitnessgoals: string[];
    cardiolevel: string;
    weightliftinglevel: string;
    equipment: string[];
    workoutdays: string[];
    workoutduration: number;
    workoutfrequency: number;
    display_name: string;
    gender: string;
    age: number;
    height: number;
    height_unit: string;
    weight: number;
    weight_unit: string;
    sport_activity: string;
    excluded_exercises: string[];
    additional_notes: string;
  };
  
  fitness_guide: string; // Personalized deep research
  
  next_scheduled_workout_information: {
    name: string;
    duration: number;
    ai_description: string;
    exercises: Array<{
      name: string;
      sets: number;
      reps: number[];
      weight: number[];
      rest_interval: number;
      order_index: number;
    }>;
  };
  
  previous_workout_summaries_and_dates: Array<{
    completed_workout_summary: string;
    created_at: string;
  }>;
}
```

### 3. Tool Implementations

#### Calculator Tool
For arithmetic calculations, performance analysis, and workout metrics.

#### Workout Updater Tool
Key requirements:
- Must maintain exact exercise names from database
- Arrays for reps/weights must match number of sets
- Must include ALL exercises (even unchanged ones)
- Requires user confirmation before changes
- Provides personalized rationale

**Expected JSON Format:**
```json
{
  "next_workout": {
    "workout_name": "Full Body Strength Progression",
    "exercises": [
      {
        "name": "Barbell Back Squat",
        "sets": 4,
        "reps": [8, 8, 6, 6],
        "weight": [135, 145, 155, 155],
        "rest_interval": 90,
        "order_index": 1
      }
    ]
  },
  "workout_rationale": "Based on your last workout performance..."
}
```

#### Researcher Tool
For internet searches when internal knowledge is insufficient.

### 4. Firebase Functions Implementation

When re-enabling Cloud Functions, implement these endpoints:

#### Main Chat Function
```javascript
exports.chatWithAI = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const userId = context.auth.uid;
  const message = data.message;
  
  // Fetch user context
  const userContext = await getUserContext(userId);
  
  // Process with AI
  const response = await processWithAI(message, userContext);
  
  return { response };
});
```

#### Workout Generation Function
```javascript
exports.generateAndSaveWorkout = functions.https.onCall(async (data, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const userId = context.auth.uid;
  const params = data;
  
  // Generate workout based on parameters
  const workout = await generateWorkout(params);
  
  // Save to Firestore
  await saveWorkout(userId, workout);
  
  return { success: true, workout };
});
```

### 5. Firestore Collections

Required collections:
- `users`: User profiles and preferences
- `workouts`: Scheduled workouts
- `workout_exercises`: Exercise details for workouts
- `completed_workouts`: Workout history with AI summaries
- `exercises`: Master exercise database
- `fitness_guides`: Personalized research documents

### 6. Key Behavioral Rules

1. **Exercise Name Accuracy**: Always verify exercise names against database
2. **Confirmation Required**: Ask user before making workout changes
3. **Personalization**: Reference user's history, goals, and preferences
4. **Progressive Overload**: Consider past performance when generating workouts
5. **Clear Communication**: Use structured responses with emojis and formatting

### 7. Response Patterns

#### For Workout Generation
```
🏋️‍♂️ **Personalized Workout Generated**

✅ **[Workout Name]**
⏱️ Duration: [X] minutes
🎯 Type: [workout type]

**Exercises:**
1. **[Exercise Name]** - [sets] x [reps] @ [weight]lbs
   *[brief instruction or tip]*
2. ...

💡 **Why this workout?**
[Personalized rationale based on user context]

🚀 **Ready to start your workout?**
```

#### For General Advice
```
[Emoji] **[Topic Header]**

[Greeting with user name], [contextual opening]

• **[Key Point]**: [Explanation]
• **[Key Point]**: [Explanation]
...

[Closing motivation or call to action]

💪 [Action prompt - usually "Generate a workout"]
```

### 8. Error Handling

- Validate all user inputs
- Provide helpful error messages
- Fall back gracefully when services unavailable
- Log errors for debugging

### 9. Testing Checklist

- [ ] User authentication works
- [ ] User context fetches correctly
- [ ] Workout detection triggers appropriately
- [ ] Generated workouts have valid exercises
- [ ] Responses are personalized with user data
- [ ] Error cases handled gracefully
- [ ] Performance is acceptable (<3s response time)

### 10. Migration from Current Implementation

1. Uncomment Cloud Functions imports
2. Update `_callGenerateWorkoutFunction` to use actual Firebase Function
3. Implement the three tools (Calculator, Workout Updater, Researcher)
4. Add conversation memory/context management
5. Enhance response generation with user workout history
6. Add analytics tracking for user engagement