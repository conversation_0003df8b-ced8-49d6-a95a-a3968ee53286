# Firebase AI Setup Guide for AgenticFit

This guide will help you set up Firebase AI (formerly Vertex AI in Firebase) to enable real Gemini AI responses in your AgenticFit chat feature.

## Current Status

✅ **Completed:**
- Memory optimization issues resolved
- Enhanced chat interface with user context integration
- Placeholder responses using actual user profile data
- Firebase AI package (`firebase_ai: ^2.0.0`) added to dependencies
- Code structure ready for Firebase AI integration

⏳ **Pending:**
- Firebase AI configuration in Firebase Console
- Gemini API key setup
- Testing real AI responses

## Prerequisites

1. **Firebase Project**: Your AgenticFit app should already be connected to Firebase
2. **Billing Account**: Firebase AI requires the Blaze (pay-as-you-go) plan
3. **Google Cloud Project**: Your Firebase project should have an associated Google Cloud project

## Step-by-Step Setup

### 1. Enable Firebase AI in Firebase Console

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your AgenticFit project
3. In the left sidebar, look for **"Build with Gemini"** or **"Firebase AI Logic"**
4. Click **"Get started"** to launch the setup workflow

### 2. Choose Your API Provider

You'll be prompted to select an API provider:

**Option A: Gemini Developer API (Recommended for development)**
- ✅ Available on free Spark plan initially
- ✅ Easier setup
- ✅ Good for testing and development
- ⚠️ API key required (handled securely by Firebase)

**Option B: Vertex AI Gemini API**
- ⚠️ Requires Blaze plan (billing required)
- ✅ More enterprise features
- ✅ Better for production

### 3. Configure Billing (if needed)

If you choose Vertex AI or want to upgrade from Spark plan:

1. In Firebase Console, go to **Settings** → **Usage and billing**
2. Click **"Modify plan"**
3. Select **"Blaze"** plan
4. Set up billing account if not already done

### 4. Enable Required APIs

The Firebase console should automatically enable these APIs:
- Firebase AI API
- Vertex AI API (if using Vertex AI backend)
- Cloud Resource Manager API

### 5. Set up App Check (Recommended)

For production security:

1. In Firebase Console, go to **App Check**
2. Click **"Get started"**
3. Configure for your platforms:
   - **iOS**: App Attest or DeviceCheck
   - **Android**: Play Integrity or SafetyNet

### 6. Update Your Code

Once Firebase AI is configured, uncomment the Firebase AI code in `lib/services/gemini_chat_service.dart`:

```dart
// Change this:
// import 'package:firebase_ai/firebase_ai.dart';

// To this:
import 'package:firebase_ai/firebase_ai.dart';

// And uncomment the Firebase AI implementation in the initializeModel() method
```

### 7. Test the Integration

1. Run your app: `flutter run`
2. Navigate to the chat feature
3. Send a test message
4. Check the console logs for initialization success

## Code Changes Required

### In `lib/services/gemini_chat_service.dart`:

1. **Uncomment the import:**
```dart
import 'package:firebase_ai/firebase_ai.dart';
```

2. **Uncomment the model variables:**
```dart
GenerativeModel? _model;
ChatSession? _chatSession;
```

3. **Replace the initializeModel() method:**
```dart
Future<void> initializeModel() async {
  try {
    // Initialize Firebase AI with Google AI backend
    final ai = FirebaseAI.googleAI();
    
    // Build system instruction with user context
    final systemInstruction = await _buildSystemInstruction();
    
    // Create a GenerativeModel instance
    _model = ai.generativeModel(
      model: 'gemini-1.5-flash',
      systemInstruction: Content.system(systemInstruction),
    );
    
    // Start chat session
    _chatSession = _model!.startChat();
    _isInitialized = true;
    print('Firebase AI Chat Service Initialized Successfully.');
  } catch (e) {
    print('Error initializing Firebase AI Chat Service: $e');
    _isInitialized = false;
    // Falls back to placeholder responses
  }
}
```

4. **Replace the sendMessage() method:**
```dart
Future<String> sendMessage(String message) async {
  if (!_isInitialized) {
    await initializeModel();
    if (!_isInitialized) {
      return "I'm having trouble connecting right now. Please try again in a moment.";
    }
  }

  try {
    if (_chatSession == null) {
      throw Exception('Chat session not initialized');
    }

    // Send message to Gemini
    final response = await _chatSession!.sendMessage(Content.text(message));
    final responseText = response.text;
    
    if (responseText != null && responseText.isNotEmpty) {
      return responseText;
    } else {
      return "I received your message but couldn't generate a response. Please try rephrasing your question.";
    }
  } catch (e) {
    print('Error sending message to Firebase AI: $e');
    
    // Fallback to enhanced placeholder responses
    return _getEnhancedPlaceholderResponse(message);
  }
}
```

## Troubleshooting

### Common Issues:

1. **"firebase_ai package not found"**
   - Run `flutter clean && flutter pub get`
   - Restart your IDE
   - Check that `firebase_ai: ^2.0.0` is in pubspec.yaml

2. **"API not enabled" errors**
   - Go to Google Cloud Console
   - Enable Vertex AI API and Firebase AI API
   - Wait a few minutes for propagation

3. **Authentication errors**
   - Ensure your app is properly connected to Firebase
   - Check that `firebase_options.dart` is up to date
   - Run `flutterfire configure` if needed

4. **Billing errors**
   - Upgrade to Blaze plan in Firebase Console
   - Set up billing account in Google Cloud Console

### Testing Commands:

```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run

# Check Firebase connection
flutterfire configure

# View logs
flutter logs
```

## Expected Behavior

### Before Firebase AI Setup:
- Chat works with enhanced placeholder responses
- Responses use actual user profile data
- Fallback messages indicate AI is not fully configured

### After Firebase AI Setup:
- Real Gemini AI responses
- Personalized responses based on user fitness data
- Natural conversation flow
- Fallback to placeholder responses if AI fails

## Security Notes

- ✅ API keys are handled securely by Firebase (not embedded in app)
- ✅ App Check provides additional security against abuse
- ✅ User data is processed according to Firebase/Google privacy policies
- ✅ Chat history is stored in user's Firestore document

## Cost Considerations

- **Gemini Developer API**: Free tier available, then pay-per-use
- **Vertex AI**: Pay-per-use pricing
- **Firestore**: Minimal cost for chat history storage
- **Firebase Hosting**: Free tier sufficient for most use cases

Monitor usage in Firebase Console → Usage and billing

## Next Steps

1. Complete Firebase AI setup in console
2. Test with real AI responses
3. Monitor usage and costs
4. Consider implementing rate limiting for production
5. Add more sophisticated prompt engineering for better responses

## Support

- [Firebase AI Documentation](https://firebase.google.com/docs/vertex-ai)
- [Firebase Support](https://firebase.google.com/support)
- [Flutter Firebase Documentation](https://firebase.flutter.dev/)

---

**Note**: The current implementation gracefully falls back to enhanced placeholder responses if Firebase AI is not configured, so your app will continue to work during the setup process. 