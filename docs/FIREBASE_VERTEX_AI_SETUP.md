# Firebase Vertex AI Setup Guide

## 🚨 CRITICAL: No More Mock Data - Real AI Implementation

This guide will help you set up **real Firebase AI** using Vertex AI in Firebase, following the official Flutter demos pattern.

## Step 1: Enable Vertex AI in Firebase Console

1. **Go to Firebase Console**: https://console.firebase.google.com
2. **Select your project**: `agentic_fit`
3. **Navigate to Vertex AI**:
   - Click on "Build" in the left sidebar
   - Click on "Vertex AI in Firebase"
   - OR go directly to: https://console.firebase.google.com/project/YOUR_PROJECT_ID/vertex-ai

4. **Enable Vertex AI**:
   - Click "Get Started" 
   - Follow the setup wizard
   - **IMPORTANT**: You may need to upgrade to the Blaze plan (pay-as-you-go)
   - Enable the required APIs when prompted

5. **Configure Models**:
   - Enable Gemini 2.0 Flash (recommended for chat)
   - Enable Gemini 1.5 Pro (for complex tasks)

## Step 2: Verify Firebase AI Package

The `firebase_vertexai: ^1.7.0` package should already be in your `pubspec.yaml`. If not, add it:

```yaml
dependencies:
  firebase_vertexai: ^1.7.0
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.5.0
```

## Step 3: Test Firebase AI Connection

Create a simple test to verify the connection works:

```dart
// Test file: test_firebase_ai_connection.dart
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:firebase_core/firebase_core.dart';

Future<void> testFirebaseAI() async {
  try {
    // Initialize Firebase if not already done
    await Firebase.initializeApp();
    
    // Test Vertex AI connection
    final model = FirebaseVertexAI.instance.generativeModel(
      model: 'gemini-1.5-flash',
    );
    
    final response = await model.generateContent([
      Content.text('Hello, can you confirm you are working?')
    ]);
    
    print('✅ Firebase AI is working!');
    print('Response: ${response.text}');
  } catch (e) {
    print('❌ Firebase AI error: $e');
  }
}
```

## Step 4: Implement Real Fitness Agent

Once Vertex AI is enabled, the real implementation will use:

### Real Firebase AI Features:
- **Function Calling**: AI can call your app functions
- **Streaming Responses**: Real-time response streaming
- **Context Awareness**: Maintains conversation context
- **Tool Integration**: Access to user data and app functions

### Fitness-Specific Tools:
- `getUserProfile`: Get user's fitness data from Firestore
- `generateWorkout`: Create personalized workouts
- `trackProgress`: Analyze workout history
- `getNutritionAdvice`: Provide meal recommendations
- `saveWorkout`: Store workouts to user's library

## Step 5: Security with App Check (Recommended)

For production, enable Firebase App Check:

```dart
import 'package:firebase_app_check/firebase_app_check.dart';

await FirebaseAppCheck.instance.activate(
  webProvider: ReCaptchaV3Provider('your-recaptcha-key'),
  androidProvider: AndroidProvider.playIntegrity,
  appleProvider: AppleProvider.appAttest,
);

// Connect App Check to Vertex AI
final model = FirebaseVertexAI.instance.generativeModel(
  model: 'gemini-1.5-flash'
);
```

## Step 6: Monitor Usage

- Go to Firebase Console > Vertex AI > Usage
- Monitor token consumption and costs
- Set up billing alerts if needed

## Troubleshooting

### Common Issues:

1. **"Vertex AI not enabled"**:
   - Ensure you've enabled Vertex AI in Firebase Console
   - Check that billing is enabled (Blaze plan required)

2. **"Model not found"**:
   - Verify the model name (use 'gemini-2.0-flash' or 'gemini-1.5-pro')
   - Check that the model is enabled in your project

3. **"Permission denied"**:
   - Ensure your Firebase project has the correct permissions
   - Check that the user is authenticated

4. **Import errors**:
   - Run `flutter clean && flutter pub get`
   - Restart your IDE
   - Check that firebase_vertexai: ^1.7.0 is in pubspec.yaml
   - **CRITICAL**: Vertex AI must be enabled in Firebase Console first!

## Next Steps

After enabling Vertex AI in Firebase Console:

1. Test the connection with the test code above
2. Replace the mock chat service with the real Firebase AI implementation
3. Test the fitness-specific function calling
4. Monitor usage and costs in Firebase Console

## Resources

- [Firebase Vertex AI Documentation](https://firebase.google.com/docs/vertex-ai)
- [Flutter Firebase AI Package](https://pub.dev/packages/firebase_ai)
- [Official Flutter Demos](https://github.com/flutter/demos/tree/main/agentic_app_manager)
- [Reflection App Case Study](https://www.reflection.app/blog/google-gemini-vertex-ai-firebase-journaling-app)

---

**⚠️ IMPORTANT**: This is REAL AI, not mock data. Costs will apply based on token usage. Monitor your Firebase Console for usage metrics. 