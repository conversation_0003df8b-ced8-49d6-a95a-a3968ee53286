{"traceId": "********************************", "spans": {"50adb2d2bdb486c2": {"spanId": "50adb2d2bdb486c2", "traceId": "********************************", "parentSpanId": "b58492b08c66938f", "startTime": 1748223869021, "endTime": 1748223869505.003, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.1", "gcp.firestore.settings.project_id": "po2vf2ae7tal9invaj7jkf4a06hsac", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1", "transactional": false, "doc_count": 1}, "displayName": "BatchGetDocuments", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": [{"time": 1748223869048.0974, "annotation": {"attributes": {}, "description": "Firestore.batchGetDocuments: Start"}}, {"time": 1748223869503.2432, "annotation": {"attributes": {}, "description": "Firestore.batchGetDocuments: First response received"}}, {"time": 1748223869504.8748, "annotation": {"attributes": {"response_count": 1}, "description": "Firestore.batchGetDocuments: Completed"}}]}}, "b58492b08c66938f": {"spanId": "b58492b08c66938f", "traceId": "********************************", "parentSpanId": "74de1efe9c54140c", "startTime": 1748223869021, "endTime": 1748223869508.9407, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.1", "gcp.firestore.settings.project_id": "po2vf2ae7tal9invaj7jkf4a06hsac", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Get", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "87c71f9f2e2ede98": {"spanId": "87c71f9f2e2ede98", "traceId": "********************************", "parentSpanId": "74de1efe9c54140c", "startTime": 1748223869510, "endTime": 1748223869649.7537, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.1", "gcp.firestore.settings.project_id": "po2vf2ae7tal9invaj7jkf4a06hsac", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "Query.Get", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": [{"time": 1748223869510.6462, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1748223869513.6836, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1748223869639.2998, "annotation": {"attributes": {}, "description": "Firestore.runQuery: First response received"}}]}}, "c6a85242bba4cb99": {"spanId": "c6a85242bba4cb99", "traceId": "********************************", "parentSpanId": "563fcc03b1b36c0d", "startTime": 1748223870095, "endTime": 1748223870225.7175, "attributes": {"http.request.method": "POST", "http.request.method_original": "POST", "url.full": "https://us-central1-aiplatform.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash:generateContent", "url.path": "/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash:generateContent", "url.query": "", "url.scheme": "https", "server.address": "us-central1-aiplatform.googleapis.com", "server.port": 443, "user_agent.original": "model-builder/1.10.0 grpc-node/1.10.0", "network.peer.address": "2607:f8b0:4004:c1f::5f", "network.peer.port": 443, "http.response.status_code": 404}, "displayName": "POST", "links": [], "instrumentationLibrary": {"name": "@opentelemetry/instrumentation-undici", "version": "0.5.0"}, "spanKind": "CLIENT", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2}, "timeEvents": {"timeEvent": []}}, "563fcc03b1b36c0d": {"spanId": "563fcc03b1b36c0d", "traceId": "********************************", "parentSpanId": "b6db4940e17702de", "startTime": 1748223869674, "endTime": 1748223870229.3225, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "vertexai/gemini-1.5-flash", "genkit:path": "/{generateAndSaveWorkoutFlow,t:flow}/{generate,t:util}/{vertexai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"Generate a personalized workout plan based on this profile and history. Profile: {\\\"age\\\":28,\\\"createdAt\\\":1748184038597,\\\"gender\\\":\\\"male\\\",\\\"heightFeet\\\":5.666666666666667,\\\"name\\\":\\\"ben\\\",\\\"updatedAt\\\":null,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"weightLbs\\\":8}. History: [{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748159355704,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156099820,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's strength Workout\\\",\\\"description\\\":\\\"A personalized strength workout designed for Ben\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"06c6ffbc-782a-4176-974e-d171a55e0460\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on proper form. A breathing exercise designed to improve diaphragm function and encourage proper core activation\\\"},{\\\"exerciseId\\\":\\\"0e5c8a3c-f141-4dd1-959e-6279084344fd\\\",\\\"name\\\":\\\"Bodyweight Get-Up\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Focus on proper form. A full-body exercise designed to improve strength, mobility, and coordination by transitioning from the ground to a standing position\\\"},{\\\"exerciseId\\\":\\\"0fe0eb70-a230-41b8-844d-85e6b78d2a4e\\\",\\\"name\\\":\\\"Bird Dog\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":60,\\\"notes\\\":\\\"Focus on proper form. A core stability exercise that improves balance and coordination by extending one arm and the opposite leg simultaneously\\\"},{\\\"exerciseId\\\":\\\"100edd23-53b7-402d-9a25-f2eb8429823a\\\",\\\"name\\\":\\\"Side Plank Wall-Slide\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":75,\\\"notes\\\":\\\"Focus on proper form. A variation of the side plank incorporating a wall slide motion to engage the shoulders and core simultaneously\\\"},{\\\"exerciseId\\\":\\\"14656e91-1b5d-4901-b796-d74ad4b528cb\\\",\\\"name\\\":\\\"Side-Lying Clam Shell\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":90,\\\"notes\\\":\\\"Focus on proper form. A hip-strengthening exercise performed on the side, focusing on activating and working the glutes\\\"},{\\\"exerciseId\\\":\\\"15624a06-3e97-49d1-b375-afbd14f5a008\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":105,\\\"notes\\\":\\\"Focus on proper form. A foundational core strengthening exercise that builds stability by maintaining a straight and rigid body posture\\\"}],\\\"warmup\\\":[\\\"Light cardio (5 minutes)\\\",\\\"Dynamic stretching (5 minutes)\\\"],\\\"cooldown\\\":[\\\"Static stretching (5 minutes)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body\\\",\\\"Focus on proper form\\\",\\\"Stay hydrated\\\",\\\"Progress gradually\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:12:34.557Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"id\\\":\\\"\\\",\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"\\\",\\\"startTime\\\":1748147725626,\\\"notes\\\":\\\"Great workout session!\\\",\\\"completedExercises\\\":[{\\\"exerciseId\\\":\\\"1434f9f4-80bb-4fe9-ba95-0cbdb3e2c3cc\\\",\\\"completedSets\\\":3,\\\"sets\\\":[{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0},{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0},{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0}],\\\"completedAt\\\":1748147739816}],\\\"endTime\\\":1748147739817,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157258388,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157657750,\\\"isCompleted\\\":true},{\\\"startTime\\\":1748154208407,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout session!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748154216618,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"066141ba-ef3f-4cb4-8485-e98346deba8a\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748154218670,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748154223464,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748154223465,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748158077127,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748158101535,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:00:09.129Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157871988,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157889829,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T15:59:44.066Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156188064,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748157858271,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"startTime\\\":1748155477541,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748155492909,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"066141ba-ef3f-4cb4-8485-e98346deba8a\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155494925,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155497335,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748155497336,\\\"isCompleted\\\":true},{\\\"startTime\\\":1748155901446,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748155925708,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155931309,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748155931310,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156608699,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156296058,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748155729471,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156444081,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157662061,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157700291,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's strength Workout\\\",\\\"description\\\":\\\"A personalized strength workout designed for Ben\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"06c6ffbc-782a-4176-974e-d171a55e0460\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on proper form. A breathing exercise designed to improve diaphragm function and encourage proper core activation\\\"},{\\\"exerciseId\\\":\\\"0a3ec8a1-335f-48ae-97d8-0fc7e9a079be\\\",\\\"name\\\":\\\"Hip Flexor Stretch\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Focus on proper form. A static stretch designed to relieve tension in the hip flexors and improve hip mobility\\\"},{\\\"exerciseId\\\":\\\"0e5c8a3c-f141-4dd1-959e-6279084344fd\\\",\\\"name\\\":\\\"Bodyweight Get-Up\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":60,\\\"notes\\\":\\\"Focus on proper form. A full-body exercise designed to improve strength, mobility, and coordination by transitioning from the ground to a standing position\\\"},{\\\"exerciseId\\\":\\\"0f5493cb-39df-4b2d-a54b-f4fc45f814ec\\\",\\\"name\\\":\\\"Walking Warrior Lunge\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":75,\\\"notes\\\":\\\"Focus on proper form. A dynamic lower-body exercise that combines a forward lunge with a warrior pose for strength and flexibility\\\"},{\\\"exerciseId\\\":\\\"0fe0eb70-a230-41b8-844d-85e6b78d2a4e\\\",\\\"name\\\":\\\"Bird Dog\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":90,\\\"notes\\\":\\\"Focus on proper form. A core stability exercise that improves balance and coordination by extending one arm and the opposite leg simultaneously\\\"},{\\\"exerciseId\\\":\\\"100edd23-53b7-402d-9a25-f2eb8429823a\\\",\\\"name\\\":\\\"Side Plank Wall-Slide\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":105,\\\"notes\\\":\\\"Focus on proper form. A variation of the side plank incorporating a wall slide motion to engage the shoulders and core simultaneously\\\"}],\\\"warmup\\\":[\\\"Light cardio (5 minutes)\\\",\\\"Dynamic stretching (5 minutes)\\\"],\\\"cooldown\\\":[\\\"Static stretching (5 minutes)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body\\\",\\\"Focus on proper form\\\",\\\"Stay hydrated\\\",\\\"Progress gradually\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:02:40.612Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:00:34.612Z\\\",\\\"workoutType\\\":\\\"cardio\\\",\\\"targetMuscleGroups\\\":[\\\"Full Body\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156765157,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"}]. Return JSON.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}", "genkit:state": "error"}, "displayName": "vertexai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748223870229.2102, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/post_fetch_processing.ts:47:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/generate_content.ts:94:3)\n    at async ChatSessionPreview.sendMessage (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/models/chat_session.ts:352:58)\n    at async callGemini (/Users/<USER>/aifit/node_modules/@genkit-ai/vertexai/src/gemini.ts:1193:26)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/vertexai/src/gemini.ts:1249:11)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/action.ts:439:14)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/action.ts:335:26)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/tracing/instrumentation.ts:73:16)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/tracing/instrumentation.ts:115:24)"}, "description": "exception"}}]}}, "b6db4940e17702de": {"spanId": "b6db4940e17702de", "traceId": "********************************", "parentSpanId": "74de1efe9c54140c", "startTime": 1748223869652, "endTime": 1748223870230.2913, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:path": "/{generateAndSaveWorkoutFlow,t:flow}/{generate,t:util}", "genkit:input": "{\"model\":\"vertexai/gemini-1.5-flash\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"Generate a personalized workout plan based on this profile and history. Profile: {\\\"age\\\":28,\\\"createdAt\\\":1748184038597,\\\"gender\\\":\\\"male\\\",\\\"heightFeet\\\":5.666666666666667,\\\"name\\\":\\\"ben\\\",\\\"updatedAt\\\":null,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"weightLbs\\\":8}. History: [{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748159355704,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156099820,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's strength Workout\\\",\\\"description\\\":\\\"A personalized strength workout designed for Ben\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"06c6ffbc-782a-4176-974e-d171a55e0460\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on proper form. A breathing exercise designed to improve diaphragm function and encourage proper core activation\\\"},{\\\"exerciseId\\\":\\\"0e5c8a3c-f141-4dd1-959e-6279084344fd\\\",\\\"name\\\":\\\"Bodyweight Get-Up\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Focus on proper form. A full-body exercise designed to improve strength, mobility, and coordination by transitioning from the ground to a standing position\\\"},{\\\"exerciseId\\\":\\\"0fe0eb70-a230-41b8-844d-85e6b78d2a4e\\\",\\\"name\\\":\\\"Bird Dog\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":60,\\\"notes\\\":\\\"Focus on proper form. A core stability exercise that improves balance and coordination by extending one arm and the opposite leg simultaneously\\\"},{\\\"exerciseId\\\":\\\"100edd23-53b7-402d-9a25-f2eb8429823a\\\",\\\"name\\\":\\\"Side Plank Wall-Slide\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":75,\\\"notes\\\":\\\"Focus on proper form. A variation of the side plank incorporating a wall slide motion to engage the shoulders and core simultaneously\\\"},{\\\"exerciseId\\\":\\\"14656e91-1b5d-4901-b796-d74ad4b528cb\\\",\\\"name\\\":\\\"Side-Lying Clam Shell\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":90,\\\"notes\\\":\\\"Focus on proper form. A hip-strengthening exercise performed on the side, focusing on activating and working the glutes\\\"},{\\\"exerciseId\\\":\\\"15624a06-3e97-49d1-b375-afbd14f5a008\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":105,\\\"notes\\\":\\\"Focus on proper form. A foundational core strengthening exercise that builds stability by maintaining a straight and rigid body posture\\\"}],\\\"warmup\\\":[\\\"Light cardio (5 minutes)\\\",\\\"Dynamic stretching (5 minutes)\\\"],\\\"cooldown\\\":[\\\"Static stretching (5 minutes)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body\\\",\\\"Focus on proper form\\\",\\\"Stay hydrated\\\",\\\"Progress gradually\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:12:34.557Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"id\\\":\\\"\\\",\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"\\\",\\\"startTime\\\":1748147725626,\\\"notes\\\":\\\"Great workout session!\\\",\\\"completedExercises\\\":[{\\\"exerciseId\\\":\\\"1434f9f4-80bb-4fe9-ba95-0cbdb3e2c3cc\\\",\\\"completedSets\\\":3,\\\"sets\\\":[{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0},{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0},{\\\"reps\\\":12,\\\"weight\\\":0,\\\"duration\\\":0}],\\\"completedAt\\\":1748147739816}],\\\"endTime\\\":1748147739817,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157258388,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157657750,\\\"isCompleted\\\":true},{\\\"startTime\\\":1748154208407,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout session!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748154216618,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"066141ba-ef3f-4cb4-8485-e98346deba8a\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748154218670,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748154223464,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748154223465,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748158077127,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748158101535,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:00:09.129Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157871988,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157889829,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T15:59:44.066Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156188064,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748157858271,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"startTime\\\":1748155477541,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748155492909,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"066141ba-ef3f-4cb4-8485-e98346deba8a\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155494925,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155497335,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748155497336,\\\"isCompleted\\\":true},{\\\"startTime\\\":1748155901446,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"completedExercises\\\":[{\\\"completedAt\\\":1748155925708,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"0abf048c-7dd2-486b-8405-131085a9ed40\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]},{\\\"completedAt\\\":1748155931309,\\\"completedSets\\\":3,\\\"exerciseId\\\":\\\"7b91d509-145b-414b-a9c5-e12a686009fd\\\",\\\"sets\\\":[{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0},{\\\"duration\\\":0,\\\"reps\\\":12,\\\"weight\\\":0}]}],\\\"endTime\\\":1748155931310,\\\"isCompleted\\\":true},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156608699,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156296058,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748155729471,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156444081,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"},{\\\"completedExercises\\\":[],\\\"startTime\\\":1748157662061,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\",\\\"notes\\\":\\\"Great workout!\\\",\\\"endTime\\\":1748157700291,\\\"isCompleted\\\":true},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's strength Workout\\\",\\\"description\\\":\\\"A personalized strength workout designed for Ben\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"06c6ffbc-782a-4176-974e-d171a55e0460\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on proper form. A breathing exercise designed to improve diaphragm function and encourage proper core activation\\\"},{\\\"exerciseId\\\":\\\"0a3ec8a1-335f-48ae-97d8-0fc7e9a079be\\\",\\\"name\\\":\\\"Hip Flexor Stretch\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Focus on proper form. A static stretch designed to relieve tension in the hip flexors and improve hip mobility\\\"},{\\\"exerciseId\\\":\\\"0e5c8a3c-f141-4dd1-959e-6279084344fd\\\",\\\"name\\\":\\\"Bodyweight Get-Up\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":60,\\\"notes\\\":\\\"Focus on proper form. A full-body exercise designed to improve strength, mobility, and coordination by transitioning from the ground to a standing position\\\"},{\\\"exerciseId\\\":\\\"0f5493cb-39df-4b2d-a54b-f4fc45f814ec\\\",\\\"name\\\":\\\"Walking Warrior Lunge\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":75,\\\"notes\\\":\\\"Focus on proper form. A dynamic lower-body exercise that combines a forward lunge with a warrior pose for strength and flexibility\\\"},{\\\"exerciseId\\\":\\\"0fe0eb70-a230-41b8-844d-85e6b78d2a4e\\\",\\\"name\\\":\\\"Bird Dog\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":90,\\\"notes\\\":\\\"Focus on proper form. A core stability exercise that improves balance and coordination by extending one arm and the opposite leg simultaneously\\\"},{\\\"exerciseId\\\":\\\"100edd23-53b7-402d-9a25-f2eb8429823a\\\",\\\"name\\\":\\\"Side Plank Wall-Slide\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10-12\\\",\\\"restSeconds\\\":105,\\\"notes\\\":\\\"Focus on proper form. A variation of the side plank incorporating a wall slide motion to engage the shoulders and core simultaneously\\\"}],\\\"warmup\\\":[\\\"Light cardio (5 minutes)\\\",\\\"Dynamic stretching (5 minutes)\\\"],\\\"cooldown\\\":[\\\"Static stretching (5 minutes)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body\\\",\\\"Focus on proper form\\\",\\\"Stay hydrated\\\",\\\"Progress gradually\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:02:40.612Z\\\",\\\"workoutType\\\":\\\"strength\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlan\\\":{\\\"workoutName\\\":\\\"Ben's Core & Glute Strength Workout\\\",\\\"description\\\":\\\"A 45-minute bodyweight workout targeting core and glute muscles, perfect for home training\\\",\\\"estimatedDuration\\\":45,\\\"difficulty\\\":\\\"beginner\\\",\\\"targetMuscleGroups\\\":[\\\"Core\\\",\\\"Glutes\\\"],\\\"exercises\\\":[{\\\"exerciseId\\\":\\\"ribs-down-breathing\\\",\\\"name\\\":\\\"Ribs-Down Breathing\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":15,\\\"notes\\\":\\\"Focus on controlled breathing and core activation\\\"},{\\\"exerciseId\\\":\\\"glute-bridges\\\",\\\"name\\\":\\\"Glute Bridges\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Squeeze glutes at the top, keep core engaged\\\"},{\\\"exerciseId\\\":\\\"plank\\\",\\\"name\\\":\\\"Plank\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"30 seconds\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Maintain straight line from head to heels\\\"},{\\\"exerciseId\\\":\\\"bodyweight-squats\\\",\\\"name\\\":\\\"Bodyweight Squats\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"10\\\",\\\"restSeconds\\\":45,\\\"notes\\\":\\\"Keep chest up, weight in heels\\\"},{\\\"exerciseId\\\":\\\"dead-bug\\\",\\\"name\\\":\\\"Dead Bug\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"8 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Keep lower back pressed to floor\\\"},{\\\"exerciseId\\\":\\\"clamshells\\\",\\\"name\\\":\\\"Clamshells\\\",\\\"sets\\\":3,\\\"reps\\\":\\\"12 per side\\\",\\\"restSeconds\\\":30,\\\"notes\\\":\\\"Focus on glute activation\\\"}],\\\"warmup\\\":[\\\"Light cardio - marching in place (3 minutes)\\\",\\\"Arm circles and leg swings (2 minutes)\\\",\\\"Cat-cow stretches (2 minutes)\\\"],\\\"cooldown\\\":[\\\"Child's pose (1 minute)\\\",\\\"Hip flexor stretch (1 minute each side)\\\",\\\"Seated spinal twist (1 minute each side)\\\",\\\"Deep breathing (2 minutes)\\\"],\\\"tips\\\":[\\\"Listen to your body and stop if you feel pain\\\",\\\"Focus on proper form over speed\\\",\\\"Progress gradually by increasing reps or hold times\\\",\\\"Stay hydrated throughout the workout\\\",\\\"Breathe consistently during each exercise\\\"]},\\\"generatedAt\\\":\\\"2025-05-25T16:00:34.612Z\\\",\\\"workoutType\\\":\\\"cardio\\\",\\\"targetMuscleGroups\\\":[\\\"Full Body\\\"],\\\"customRequirements\\\":\\\"Focus on bodyweight exercises for home workout\\\",\\\"isCompleted\\\":false,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"notes\\\":\\\"\\\",\\\"completedExercises\\\":[]},{\\\"completedExercises\\\":[],\\\"endTime\\\":null,\\\"isCompleted\\\":false,\\\"notes\\\":\\\"\\\",\\\"startTime\\\":1748156765157,\\\"userId\\\":\\\"lMdwxlD2GRY5WXXj74Zoph1Oern1\\\",\\\"workoutPlanId\\\":\\\"5Nc9eTPlb73spDW8HXsZ\\\"}]. Return JSON.\"}]}],\"config\":{}}", "genkit:state": "error"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748223870230.2012, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/post_fetch_processing.ts:47:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/generate_content.ts:94:3)\n    at async ChatSessionPreview.sendMessage (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/models/chat_session.ts:352:58)\n    at async callGemini (/Users/<USER>/aifit/node_modules/@genkit-ai/vertexai/src/gemini.ts:1193:26)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/vertexai/src/gemini.ts:1249:11)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/action.ts:439:14)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/action.ts:335:26)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/tracing/instrumentation.ts:73:16)\n    at async <anonymous> (/Users/<USER>/aifit/node_modules/@genkit-ai/core/src/tracing/instrumentation.ts:115:24)"}, "description": "exception"}}]}}, "74de1efe9c54140c": {"spanId": "74de1efe9c54140c", "traceId": "********************************", "startTime": 1748223869020, "endTime": 1748223870230.2676, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "flow", "genkit:name": "generateAndSaveWorkoutFlow", "genkit:isRoot": true, "genkit:path": "/{generateAndSaveWorkoutFlow,t:flow}", "genkit:metadata:context": "{}", "genkit:input": "{\"userId\":\"lMdwxlD2GRY5WXXj74Zoph1Oern1\"}", "genkit:output": "{\"success\":false,\"error\":\"[VertexAI.ClientError]: got status: 404 Not Found. {\\\"error\\\":{\\\"code\\\":404,\\\"message\\\":\\\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\\\",\\\"status\\\":\\\"NOT_FOUND\\\"}}\",\"workoutPlan\":null}", "genkit:state": "success"}, "displayName": "generateAndSaveWorkoutFlow", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generateAndSaveWorkoutFlow", "startTime": 1748223869020, "endTime": 1748223870230.2676}