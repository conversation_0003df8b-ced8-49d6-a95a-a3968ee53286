{"traceId": "88f6e3262b2bdf056528a98da75443d2", "spans": {"f57cfde10b01797c": {"spanId": "f57cfde10b01797c", "traceId": "88f6e3262b2bdf056528a98da75443d2", "parentSpanId": "45cc470f40354345", "startTime": 1748233116830, "endTime": 1748233117019.3132, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.1", "gcp.firestore.settings.project_id": "po2vf2ae7tal9invaj7jkf4a06hsac", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1", "transactional": false, "doc_count": 1}, "displayName": "BatchGetDocuments", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": [{"time": 1748233116832.3457, "annotation": {"attributes": {}, "description": "Firestore.batchGetDocuments: Start"}}, {"time": 1748233117018.4648, "annotation": {"attributes": {}, "description": "Firestore.batchGetDocuments: First response received"}}, {"time": 1748233117019.2717, "annotation": {"attributes": {"response_count": 1}, "description": "Firestore.batchGetDocuments: Completed"}}]}}, "45cc470f40354345": {"spanId": "45cc470f40354345", "traceId": "88f6e3262b2bdf056528a98da75443d2", "parentSpanId": "3abc087421010fef", "startTime": 1748233116830, "endTime": 1748233117021.0593, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.1", "gcp.firestore.settings.project_id": "po2vf2ae7tal9invaj7jkf4a06hsac", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Get", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "3abc087421010fef": {"spanId": "3abc087421010fef", "traceId": "88f6e3262b2bdf056528a98da75443d2", "startTime": 1748233116830, "endTime": 1748233117024.9392, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "flow", "genkit:name": "generateAndSaveWorkoutFlow", "genkit:isRoot": true, "genkit:path": "/{generateAndSaveWorkoutFlow,t:flow}", "genkit:input": "{\"userId\":\"test123\"}", "genkit:output": "{\"success\":false,\"error\":\"User profile not found\",\"workoutPlan\":null}", "genkit:state": "success"}, "displayName": "generateAndSaveWorkoutFlow", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generateAndSaveWorkoutFlow", "startTime": 1748233116830, "endTime": 1748233117024.9392}