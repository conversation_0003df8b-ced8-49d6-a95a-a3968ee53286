{"traceId": "7dba08770d88433344c2c069f9a49561", "spans": {"0afd830c7312ae52": {"spanId": "0afd830c7312ae52", "traceId": "7dba08770d88433344c2c069f9a49561", "parentSpanId": "dc04711b509ae905", "startTime": 1748223079195, "endTime": 1748223079459.8179, "attributes": {"http.request.method": "POST", "http.request.method_original": "POST", "url.full": "https://us-central1-aiplatform.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash:streamGenerateContent?alt=sse", "url.path": "/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash:streamGenerateContent", "url.query": "?alt=sse", "url.scheme": "https", "server.address": "us-central1-aiplatform.googleapis.com", "server.port": 443, "user_agent.original": "model-builder/1.10.0 grpc-node/1.10.0", "network.peer.address": "2607:f8b0:4004:c0b::5f", "network.peer.port": 443, "http.response.status_code": 404, "http.response.header.content-length": 415}, "displayName": "POST", "links": [], "instrumentationLibrary": {"name": "@opentelemetry/instrumentation-undici", "version": "0.5.0"}, "spanKind": "CLIENT", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2}, "timeEvents": {"timeEvent": []}}, "dc04711b509ae905": {"spanId": "dc04711b509ae905", "traceId": "7dba08770d88433344c2c069f9a49561", "parentSpanId": "5fdb39c50e23bb50", "startTime": 1748223078666, "endTime": 1748223079464.227, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "vertexai/gemini-1.5-flash", "genkit:path": "/{menuSuggestionFlow,t:flow}/{generate,t:util}/{vertexai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"Suggest an item for the menu of a seafood themed restaurant\"}]}],\"config\":{\"temperature\":1},\"tools\":[],\"output\":{}}", "genkit:state": "error"}, "displayName": "vertexai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748223079464.1694, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/post_fetch_processing.ts:47:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContentStream (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/generate_content.ts:150:3)"}, "description": "exception"}}]}}, "5fdb39c50e23bb50": {"spanId": "5fdb39c50e23bb50", "traceId": "7dba08770d88433344c2c069f9a49561", "parentSpanId": "afc946c8e9b5a34a", "startTime": 1748223078630, "endTime": 1748223079464.5632, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:path": "/{menuSuggestionFlow,t:flow}/{generate,t:util}", "genkit:input": "{\"model\":\"vertexai/gemini-1.5-flash\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"Suggest an item for the menu of a seafood themed restaurant\"}]}],\"config\":{\"temperature\":1}}", "genkit:state": "error"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748223079464.5508, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/post_fetch_processing.ts:47:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContentStream (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/generate_content.ts:150:3)"}, "description": "exception"}}]}}, "afc946c8e9b5a34a": {"spanId": "afc946c8e9b5a34a", "traceId": "7dba08770d88433344c2c069f9a49561", "startTime": 1748223078628, "endTime": 1748223079464.6619, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "flow", "genkit:name": "menuSuggestionFlow", "genkit:isRoot": true, "genkit:path": "/{menuSuggestionFlow,t:flow}", "genkit:metadata:context": "{}", "genkit:input": "\"seafood\"", "genkit:state": "error"}, "displayName": "menuSuggestionFlow", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748223079464.6406, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/po2vf2ae7tal9invaj7jkf4a06hsac/locations/us-central1/publishers/google/models/gemini-1.5-flash` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/post_fetch_processing.ts:47:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContentStream (/Users/<USER>/aifit/node_modules/@google-cloud/vertexai/src/functions/generate_content.ts:150:3)"}, "description": "exception"}}]}}}, "displayName": "menuSuggestionFlow", "startTime": 1748223078628, "endTime": 1748223079464.6619}