{"name": "Onboarding Agent -prod", "nodes": [{"parameters": {"operation": "update", "tableId": "profiles", "matchType": "allFilters", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.user_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "fitness_guide", "fieldValue": "={{ $json.output }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2400, -1400], "id": "c781fb2f-dd04-4b01-bb0b-3974695e0911", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"content": "There should be a fixed message at the end of each guide produced by perplex which says that the app will incorporate all of this for you and we will track your progreess while you are able to continue to provide comments and changes.", "height": 180}, "type": "n8n-nodes-base.stickyNote", "position": [1420, -1340], "typeVersion": 1, "id": "df595cda-b25b-4aa3-877a-7a273973b3a6", "name": "<PERSON><PERSON>"}, {"parameters": {"httpMethod": "POST", "path": "a94d8272-cf1d-4af4-8c00-560e053aaf57", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2980, -860], "id": "fc475316-c47c-43a7-a954-2a5419479538", "name": "Webhook", "webhookId": "a94d8272-cf1d-4af4-8c00-560e053aaf57"}, {"parameters": {"model": {"__rl": true, "value": "o3-mini", "mode": "list", "cachedResultName": "o3-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1840, -480], "id": "e1d54257-5cba-4f46-94e5-6dd9d96f270b", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "649HkT30UTWRe8gE", "name": "OpenAi account 2"}}}, {"parameters": {"content": "You are an expert and reasonable writer specializing in creating hyper-personalized, science-backed workout guides. Your task is to produce a comprehensive, engaging, and motivational workout guide tailored to each user’s unique fitness level, goals, constraints, and preferences. The guide must be accessible to a general audience, and no longer than 1,500 words. It should include direct citations to credible, recent scientific sources that are clearly connected to the recommendations provided. You must use the citations given to you. Do not change or modify the citations. Do not create a specefic workout schedule like a weekly plan but a guide a comprehensive analysis that a workout schedule can be made based on. This guide is available to the user but is also used by a mobile application to create daily routines and schedules. \n\nFollow these guidelines for every article you generate:\n\n1. **Introduction and Personal Connection**\n   - Begin with a warm, engaging introduction that directly addresses the user.\n   - Reference any specific details the user provides to ensure the guide feels uniquely tailored for them.\n   - Set an inviting tone that makes the reader feel heard and understood and engaging.\n\n2. **Goal Setting and Expectations**\n   - Integrate any user-specific objectives directly into the narrative.\n   - Explore the importance of realistic timelines and gradual progress, using direct citations from credible sources on goal-setting and motivation.\n\n3. **Scientific Foundations in Accessible Language**\n   - If applicable, Explain key fitness and scientific principles such as progressive overload, specificity, and recovery in clear, simple language as it relates to the User. \n   - Relate these concepts directly to the user’s journey, showing how they apply to building strength, endurance, or flexibility.\n   - Provide direct citations to current, reputable scientific studies and sources, and explain how each source is relevant to the user’s personalized plan.\n\n4. **Comprehensive Workout Plan Breakdown**\n   - Outline the blueprint for designing an effective workout plan rather than prescribing a fixed schedule.\n   - Describe key components such as exercise selection (emphasizing compound movements and supplementary isolation exercises), recommended ranges for sets and reps, guidance on rest intervals, and strategies for determining workout intensity.\n   - Emphasize flexibility and adaptability, ensuring the reader knows how to modify the plan based on their current fitness level and available time.\n\n5. **Progression and Adaptation Strategies**\n   - Detail how the user can monitor and adjust their workout routine over time.\n   - Include strategies for gradually increasing intensity, overcoming plateaus, and troubleshooting common challenges.\n   - Support recommendations with direct citations from scientific literature regarding progression and adaptation in training.\n\n6. **Nutritional and Recovery Guidance**\n   - Provide general nutritional advice tailored to the user’s fitness goals (whether muscle gain, fat loss, or maintenance).\n   - Discuss the importance of balanced macronutrients, hydration, and meal timing, citing credible research.\n   - Outline key recovery strategies, including sleep, active recovery, and stress management, and explain how they complement the workout plan.\n\n7. **Progress Tracking and Troubleshooting**\n   - Do not include any tips on how to track. This guide is part of an app which will be doing the progress tracking for them. \n\n8. **Conclusion and Motivational Next Steps**\n   - End with a strong, motivational conclusion that reinforces the personalized nature of the guide.\n   - Do not include actionable next steps as the app that this guide is a part of will create the plans and actions based on this guide.\n   - Encourage long-term commitment and celebrate the user’s progress, ensuring the tone remains uplifting and supportive.\n\n**Formatting and Tone Requirements:**\n- Use clear and descriptive headings and subheadings to organize the article.\n- Ensure a friendly, engaging, and conversational and consistent tone throughout.\n- Ensure the language is accessible to a general audience, even when discussing scientific or technical topics.\n- Review and edit if necessary.\n- Keep the entire guide under 1,500 words.\n- Note: This guide is integrated with a mobile app that handles daily scheduling and progress tracking, so do not include instructions that conflict with the app’s functionalities such as suggesting a specefic schedule or tracking method\n- Include direct citations for all scientific claims, ensuring each citation is clearly linked to the relevant content. You must use the citations provided to you\n- Adapt the depth and focus of content based on any specific user constraints or goals provided.\n\nYour objective is to deliver a hyper-personalized, scientifically accurate, and motivational workout guide that empowers each user to create and adapt their own effective workout plan. Every article you generate should leave the reader feeling understood, motivated, and equipped with the knowledge to tailor their fitness journey to their unique needs.`\n\n** tools and function calling:**\n- you must take avantage of your tool\n-Researcher: This tool can do research for you by scraping the internet and answer your questions as it relates to writing the article and users needs. \n- Here are some examples and areas which you can ask the researcher to provide information for you:\n\n“Recent studies on designing personalized workout guides for different fitness levels and goals”\n“How to tailor workout recommendations based on user constraints and preferences, evidence-based approach”\n\n“Current research on progressive overload, specificity, and recovery in strength and endurance training”\n“Recent reviews or meta-analyses on progressive overload benefits and training adaptations”\n\n“Scientific literature on goal-setting strategies in fitness and motivation techniques backed by research”\n“Studies linking realistic timeline setting and gradual progress in exercise adherence”\n\n“Evidence-based guidelines for exercise selection (compound vs. isolation exercises) and effective workout blueprints”\n“Research on optimal set, rep, and rest interval recommendations for various fitness goals”\n\n“Recent credible sources on nutrition for muscle gain, fat loss, and workout recovery”\n“Scientific findings on sleep, hydration, and active recovery strategies in fitness”\n\n“Current research on methods to monitor, adapt, and overcome training plateaus in long-term workout routines”\n“Studies on gradual intensity increase strategies, such as the ‘2-for-2 rule’, with direct citations”\n- Make sure to ask the questions in relevance to the User's situation, preferences and goals.\n- Leverage the findings of this tool to write a better article\n\n", "height": 280}, "type": "n8n-nodes-base.stickyNote", "position": [-880, -1600], "typeVersion": 1, "id": "7444154c-55c3-4f42-8a35-acb6307c7863", "name": "Sticky Note2"}, {"parameters": {"content": "Nutritional and sleep sections and recovery tips should be added  to this article before shoowing to user."}, "type": "n8n-nodes-base.stickyNote", "position": [1160, -1320], "typeVersion": 1, "id": "0faa54c3-f193-4137-94bb-2ad6f72f2e34", "name": "Sticky Note3"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1060, -820], "id": "1ef06bb5-c11e-4ba1-8748-b5d5f91fa7d7", "name": "<PERSON><PERSON>", "retryOnFail": true}, {"parameters": {"fieldToSplitOut": "choices[0].message.content", "options": {"destinationFieldName": "researcher_input"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [800, -920], "id": "8155d30a-72ee-4113-83fa-e487a612934f", "name": "Split Out"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"messages\": [\n    {\n      \"role\": \"system\",\n        \"content\": \"You are a fitness-focused expert researcher with the primary task of writing a fitness guide for the users of a fitness app. You receive explicit user provided information detailing their fitness goals, preferences, constraints, and any relevant context. Your task is to synthesize this information with scientifically validated fitness principles to generate a hyper-personalized long term reference guide. This guide you write will serve as a flexible resource to make informed decisions on creating the user's next workout each time the user is ready for their next workout based on factors such as user's last session’s performance and time elapsed since the previous workout. Guidelines: Strictly Use Provided Information. Work exclusively with the data and constraints provided by the user. Do not infer or assume additional details. Ensure the guide remains hyper personalized to user's provided information. Comprehensive Yet Flexible Reference: Deliver a long term reference guide not a rigid day by day schedule. Ensure the guide is adaptable for both home and gym settings without relying on specific equipment. Explain the general guidelines on the types of exercises most effective in each phase of a user's journey toward their fitness goals. This must include explanations of types of excercises most suitable for the user. Core Focus Areas: Detail fundamental workout principles, recovery, and progression strategies. If applicapable, Include general research backed cardio recommendations as a supplementary element and explain the optimal way to incorporate these into the user's journey if needed. Outline how to transition between different training phases as the user gains experience and moves forward. Technical Accuracy and Personalization: Use technical research backed language as needed to ensure high accuracy. Incorporate the most rigorous and most applicable fitness concepts tailored to the user's explicit information. If the user mentions any injuries, provide relevant injury aware guidance; otherwise, focus solely on core workout principles, recovery, and progression. Decision Making Support: The guide should empower users and schedulers to decide on workouts by considering what was done in the last session. Ensure the final response is both well rounded, covering a variety of exercise and progression considerations, and adaptable so the user can decide which exercises to do based on their recent workout history. Do not generate tables or strict workout plans. This guide is primarily a reference that will be consulted each time the user wants to work out. The user already uses an app for tracking, so do not make additional tracking suggestions. Output Format: Present the response as a comprehensive written report without using tables. Ensure the language is clear, thorough, and serves as an enduring reference for making future workout decisions.\"\n},\n    {\n      \"role\": \"user\",\n      \"content\":\"Name:{{ $('Edit Fields1').item.json.user_input[0].display_name }}, Gender: {{ $('Edit Fields1').item.json.user_input[0].gender }}, age:{{ $json.user_input[0].age }}, height: {{ $json.user_input[0].height }} weight:{{ $json.user_input[0].weight }} height unit:{{ $json.user_input[0].height_unit }} ,weight unit:{{ $json.user_input[0].weight_unit }} ,primary goal:{{ $json.user_input[0].primarygoal[0] }} ,secondary goals:{{ $json.user_input[0].secondary_goal }} ,cardio level: {{ $json.user_input[0].cardio_fitness.description }} , weightlifting level: {{ $json.user_input[0].weightlifting_fitness.description }} , additional comments:{{ $json.user_input[0].additional_notes }}, {{ $json.user_input[0].fitness_additional_info }}\"\n    }\n  ],\n  \"model\": \"sonar-deep-research\"\n}", "options": {}}, "id": "a92ad4d0-f15f-4bbf-9750-d52277adfc64", "name": "Deep Search Perplexity", "type": "n8n-nodes-base.httpRequest", "position": [460, -900], "typeVersion": 4.2}, {"parameters": {"promptType": "define", "text": "=###User Data: \n\"Name:{{ $('Edit Fields1').item.json.user_input[0].display_name }}, Gender: {{ $('Edit Fields1').item.json.user_input[0].gender }}, age:{{ $json.user_input[0].age }}, height: {{ $json.user_input[0].height }} weight:{{ $json.user_input[0].weight }} height unit:{{ $json.user_input[0].height_unit }} ,weight unit:{{ $json.user_input[0].weight_unit }} ,primary goal:{{ $json.user_input[0].primarygoal[0] }} ,secondary goals:{{ $json.user_input[0].secondary_goal }} ,cardio level: {{ $json.user_input[0].cardio_fitness.description }} , weightlifting level: {{ $json.user_input[0].weightlifting_fitness.description }} , additional comments:{{ $json.user_input[0].additional_notes }}, {{ $json.user_input[0].fitness_additional_info }}\n\n###\nNote that the internatl thought processes of the resa<PERSON>cher agent is enclosed between <think>. you should ignore the thought processes and focus on the output.\nExternal Research Agent Input: {{ $json.researcher_input }}\n\n\n### No need to include nutritional guidlines and focus on fitness. The primary usage of your report is to be referenced everytime a workout session is created for the user. Therefore, it is a reference report and not seen by the user himself but will be referenced by an expert to create new workout sessions for him as he moves forward. ", "options": {"systemMessage": "=### Role:\nYou are a fitness-focused expert writer with the primary task of writing a fitness guide for the users of a fitness app. You receive explicit user-provided information detailing their fitness goals, preferences, constraints, and any relevant context along with a personlized research article. Your task is to synthesize this information with scientifically validated fitness principles to generate a hyper-personalized, long-term reference guide. This guide will serve as a flexible resource to make informed decisions on creating the user's next workout each time the user is ready for their next workout based on factors such as user's last session’s performance, and time elapsed since the previous workout.\n\n\n### Inputs processing: \n- You recive User information. \n- You also recive a a research article generated by a researcher AI. The researcher also hyperpersonlaized the research article based on the user's information. You must incorporate this research article in your thinking and writing. \n- Do not hellucinate or make up any assumptions outside the user's infomration, the resaerch article provided and what you know to be definitley true. \n- The thought processes and reasoning of the AI researcher is enclosed between <think> Which you should ignore and only focus on the output. \n\n###Your output must adhere to the following guidelines:\n\n1. Strictly Use Provided Information:\n\n- Work exclusively with the explicit data and constraints provided by the user. Do not infer or assume additional details.\n\n- Ensure the guide remains hyper personlized to user's provided infomration\n\n2. Comprehensive Yet Flexible Reference:\n\n- Deliver a long-term reference guide—not a rigid, day-by-day schedule.\n- Ensure the guide is adaptable for both home and gym settings without relying on specific equipment.\n- Explain the general guidelines on the types of exercises most effective in each phase of a user's journey toward their fitness goals. This must include explanations of types of excercises most suitable for the user. \n\n3. Core Focus Areas:\n\n- Detail fundamental workout principles, recovery, and progression strategies.\n- Include general, research-backed cardio recommendations as a -supplementary element and what is the optimal way to incorporate into user's journey if needed. \n- Outline how to transition between different training phases as the user gains experience and moves forward. \n- include the most optimal splits or workouts plan strategies for the user but keep it flexible and do not provide a strict weekly routine but suggessions based on what would be optimal. \n\n4. Technical Accuracy and Personalization:\n\n- Use technical, research-backed language as needed to ensure high accuracy.\n- Incorporate the latest and most applicable fitness concepts tailored to the user's explicit information.\n- If the user mentions any injuries, provide relevant injury-aware guidance; otherwise, focus solely on core workout principles, recovery, and progression. \n\n5. Decision-Making Support:\n\n- The guide should empower users and scheduler to decide on workouts by considering what they did in their last session.\n- Ensure your final response is both well-rounded (covering a variety of exercise and progression considerations) and adaptable (so the user can decide which exercises to do based on their recent workout history).\n- Do not generate tables or strict workout plans. this guide is primarily a reference that will be consulted each time user wants to workout\n- The user already uses an app for tracking, so do not make additional tracking suggestions.\n\n\n6. Output Format:\n\n- Present your response as a comprehensive, written report without using tables.\n- Ensure the language is clear, thorough, and serves as an enduring reference for making future workout decisions\n- Include an optimal routine example that can be followed for the user. This should not be a weekly schedule with exact days but it should be a routine splits that can be followed such as push pull legs or whatever makes the most sense for the user. \n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1400, -840], "id": "4cbd9103-d776-4308-856e-3c5ef30f2194", "name": "Fitness report writer for agents", "retryOnFail": false}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "options": {"systemMessage": "Assistant is a large language model trained by OpenAI.\n\nAssistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\n\nAssistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.\n\nOverall, Assistant is a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1340, -1160], "id": "695bbf15-95ef-432b-9519-e5e994f5e750", "name": "Article preview for user", "disabled": true}, {"parameters": {"fieldToSplitOut": "body.user_id", "options": {"destinationFieldName": "user_id"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [480, -1440], "id": "81b991f0-f16c-4597-960f-c88212ade225", "name": "Taking User Id"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output", "renameField": true, "outputFieldName": "fitness_guide"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1960, -880], "id": "60266794-9e35-4394-80a8-e9af0bc43ec0", "name": "Aggregate2"}, {"parameters": {"promptType": "define", "text": "=This is the Hyper personlized fitness guide for the user which you must reference in selecting the best exercises for his first workout: {{ $json.fitness_guide[0] }}\n\n### Here is users data You must also take into consideration the equipment available to the user. User Data: \n\"Name:{{ $json.user_input[0].display_name }}, Gender:{{ $json.user_input[0].gender }} , age:{{ $json.user_input[0].age }}, height: {{ $json.user_input[0].height }} weight:{{ $json.user_input[0].weight }} height unit:{{ $json.user_input[0].height_unit }} ,weight unit:{{ $json.user_input[0].weight_unit }} ,primary goal:{{ $json.user_input[0].primarygoal[0] }} ,secondary goals:{{ $json.user_input[0].secondary_goal }} ,cardio level: {{ $json.user_input[0].cardio_fitness.description }} , weightlifting level: {{ $json.user_input[0].weightlifting_fitness.description }} , additional comments:{{ $json.user_input[0].additional_notes }}, {{ $json.user_input[0].fitness_additional_info }} {{ $json.user_input[0] }}", "hasOutputParser": true, "options": {"systemMessage": "=Based on the excercise list below and the given the user's personal fitness report you have recieved, your job is to create the first workout session for the user. The user's situation along with recommendations about his fitness plan are described in the report and You must leverage the given report to you in the user's personlized fitness to ensure that the first workout is hyper personlized for the user's situation and needs. You are creating the first workout session by outputting a JSON which includes the list of excercises for the user's first workout. The fitness guide provided to you should be your reference for making these selections. guide along with deeply thinking about each excercises below and which ones would be the best selections for the first workout session for the user based on his personal fitness report and user's information given to you. \n\n### Objectives and Important Notes:\nYou must provide the names of the excercise in the JSON schema format given to you along with the sets, reps, weight in lbs and order_index for each excercise. \n- You are creating the first ever workout session for the user that he does\n- You must take into consideration user's provided info especailly equipment available, along with the guide provided.\n- The order_index number for each excercise is the order in which the user will do the excercise during their workout.\n- You must make sure each of these enteries are personilzed based on the User's personal fitness guide. \n- You must leverage the fitness guide provided to you to create the list of excercises for the user's first workout session. \n- DO NOT make any changes to the Names of the excercises and write them in your output JSON as exactly how they are written in the list below. \n- Double check that the names are spelled correctly and exactly represent how they are written in the list.\n- Do not make any assumptions or hellucinations about the user's goals and excercises avaialble outside of the information given to you.\n- you must double check to ensure that these excercises reflect the hyper personlized user fitness guide given to you. \n\n### here is an example of JSON output you must do the JSON schema is also provided to you: \n{\n  \"name\": [\"Bench Press\", \"Squat\"],\n  \"sets\": [3, 4],\n  \"reps\": [10, 8],\n  \"weight\": [150, 200],\n  \"order_index\": [1, 2]\n}\n\n\n### These are the list of excercies that you can choose from, make sure you use the names exactly as they are. The are in alphabetical order and therefore you have to determine the best exercises by making sure you take into consideration each exercise that closely resembles what you have decided to be the next workout for the user. In order to optimize your work, first think about what kind of exercises are best for the user and then go over each exercise in the list below to select based on which one in the list resembles closest to your determination of user’s needs.\n\n[\n  {\n    \"name\": \"Ab Wheel Rollout\"\n  },\n  {\n    \"name\": \"Alternating Barbell Split Jump\"\n  },\n  {\n    \"name\": \"Alternating Bodyweight Split Jump\"\n  },\n  {\n    \"name\": \"Alternating Dumbbell Bench Press\"\n  },\n  {\n    \"name\": \"Alternating Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Alternating Dumbell Split Jump\"\n  },\n {\n    \"name\": \"Anderson Front Squat\"\n  },\n  {\n    \"name\": \"Band-Assisted Chin-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Inverted Row\"\n  },\n  {\n    \"name\": \"Band-Assisted Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Pull-Up\"\n  },\n  {\n    \"name\": \"Band-Assisted Pushup\"\n  },\n  {\n    \"name\": \"Banded Curl\"\n  },\n  {\n    \"name\": \"Banded External Rotation at 90 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Banded Face Pull\"\n  },\n  {\n    \"name\": \"Banded Hip Extension\"\n  },\n  {\n    \"name\": \"Banded No Money\"\n  },\n  {\n    \"name\": \"Banded Pull-Down\"\n  },\n  {\n    \"name\": \"Band Press-Down\"\n  },\n  {\n    \"name\": \"Band Pull-Apart\"\n  },\n  {\n    \"name\": \"Band-Resisted Glute Bridge\"\n  },\n  {\n    \"name\": \"Band-Resisted Pushup\"\n  },\n  {\n    \"name\": \"Band-Resisted Squat\"\n  },\n  {\n    \"name\": \"Barbell Back Squat\"\n  },\n  {\n    \"name\": \"Barbell Bench Press\"\n  },\n  {\n    \"name\": \"Barbell Box Squat\"\n  },\n  {\n    \"name\": \"Barbell Curl\"\n  },\n  {\n    \"name\": \"Barbell Deadlift\"\n  },\n  {\n    \"name\": \"Barbell Front Squat\"\n  },\n  {\n    \"name\": \"Barbell Glute Bridge\"\n  },\n  {\n    \"name\": \"Barbell Hip Thrust\"\n  },\n  {\n    \"name\": \"Barbell Overhead Shrug\"\n  },\n  {\n    \"name\": \"Barbell Push Press\"\n  },\n  {\n    \"name\": \"Barbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Barbell Reverse Lunge With a Front Squat Grip\"\n  },\n  {\n    \"name\": \"Barbell Romanian Deadlift\"\n  },\n {\n    \"name\": \"Barbell Split Squat\"\n  },\n  {\n    \"name\": \"Barbell Sumo Deadlift\"\n  },\n  {\n    \"name\": \"Bear Crawl\"\n  },\n  {\n    \"name\": \"Bent-Over Dumbbell Row\"\n  },\n  {\n    \"name\": \"Bird Dog\"\n  },\n  {\n    \"name\": \"Bodyweight Cross-Over Step-Up\"\n  },\n  {\n    \"name\": \"Bodyweight Get-Up\"\n  },\n  {\n    \"name\": \"Bodyweight Lateral Squat\"\n  },\n  {\n    \"name\": \"Bodyweight Squat Thrust\"\n  },\n  {\n    \"name\": \"Bodyweight Squat to Box\"\n  },\n  {\n    \"name\": \"Bodyweight Step-Up\"\n  },\n  {\n    \"name\": \"Brady Band Series\"\n  },\n  {\n    \"name\": \"Brady Band Series - Without Band\"\n  },\n  {\n    \"name\": \"Burpee\"\n  },\n  {\n    \"name\": \"Burpee Without Pushup\"\n  },\n  {\n    \"name\": \"Cable External Rotation at 30 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Cable External Rotation at 90 Degrees Abduction\"\n  },\n  {\n    \"name\": \"Cable Pull-Down\"\n  },\n  {\n    \"name\": \"Chest-Supported Dumbbell Row\"\n  },\n  {\n    \"name\": \"Chin-Up\"\n  },\n  {\n    \"name\": \"Close-Grip Barbell Bench Press\"\n  },\n  {\n    \"name\": \"Close-Grip Pushup\"\n  },\n  {\n    \"name\": \"Dead Bug\"\n  },\n  {\n    \"name\": \"Dead Bug With Legs Only\"\n  },\n  {\n    \"name\": \"Deep Neck Flexor Activation and Suboccipital Stretch\"\n  },\n  {\n    \"name\": \"Dragon Flag\"\n  },\n  {\n    \"name\": \"Dumbbell Bench Press\"\n  },\n  {\n    \"name\": \"Dumbbell Cross-Over Step-Up\"\n  },\n  {\n    \"name\": \"Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Dumbbell External Rotation on Knee\"\n  },\n  {\n    \"name\": \"Dumbbell Floor Press\"\n  },\n  {\n    \"name\": \"Dumbbell Full Squat\"\n  },\n  {\n    \"name\": \"Dumbbell Hammer Curl\"\n  }\n{\n    \"name\": \"Dumbbell Overhead Shrug\"\n  },\n  {\n    \"name\": \"Dumbbell Push Press\"\n  },\n  {\n    \"name\": \"Dumbbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Dumbbell Reverse Lunge to Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Dumbbell Squat Thrust\"\n  },\n  {\n    \"name\": \"Dumbbell Step-Up\"\n  },\n  {\n    \"name\": \"Dumbbell Sumo Deadlift\"\n  },\n  {\n    \"name\": \"Dynamic Blackburn\"\n  },\n  {\n    \"name\": \"Eccentric Chin-Up\"\n  },\n  {\n    \"name\": \"Eccentric Pull-Up\"\n  },\n  {\n    \"name\": \"Explosive Pushup\"\n  },\n  {\n    \"name\": \"Face Pull\"\n  },\n  {\n    \"name\": \"Feet-Elevated Band-Resisted Pushup\"\n  },\n  {\n    \"name\": \"Feet-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Feet-Elevated Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Forearm Wall-Slide at 135 Degrees\"\n  },\n  {\n    \"name\": \"Goblet Lateral Lunge\"\n  },\n  {\n    \"name\": \"Goblet Lateral Lunge Walk\"\n  },\n  {\n    \"name\": \"Goblet Lateral Squat\"\n  },\n  {\n    \"name\": \"Goblet Lunge\"\n  },\n  {\n    \"name\": \"Goblet Reverse Lunge\"\n  },\n  {\n    \"name\": \"Goblet Split Squat\"\n  },\n  {\n    \"name\": \"Goblet Squat\"\n  },\n {\n    \"name\": \"Goblet Squat to Box\"\n  },\n  {\n    \"name\": \"Goblet Step-Up\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Chop\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Lift\"\n  },\n  {\n    \"name\": \"Half-Kneeling Band Overhead Shrug\"\n  },\n  {\n    \"name\": \"Half-Kneeling Cable Chop\"\n  },\n  {\n    \"name\": \"Half-Kneeling Cable Lift\"\n  },\n  {\n    \"name\": \"Half-Kneeling Pallof Press Iso\"\n  },\n  {\n    \"name\": \"Half-Kneeling Pallof Press Iso With Band\"\n  },\n  {\n    \"name\": \"Hand Cross-Over\"\n  },\n  {\n    \"name\": \"Hands-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Hands-Elevated Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Hanging Unilateral March\"\n  },\n  {\n    \"name\": \"Hinge to Side Plank\"\n  },\n  {\n    \"name\": \"Hip-Belt Squat\"\n  },\n  {\n    \"name\": \"Hip Flexor Stretch\"\n  },\n  {\n    \"name\": \"Inchworm\"\n  },\n  {\n    \"name\": \"Inverted Row\"\n  },\n  {\n    \"name\": \"Inverted Row With Weight Vest\"\n  },\n  {\n    \"name\": \"Kettlebell Armbar\"\n  },\n  {\n    \"name\": \"Knees-to-Feet Drill\"\n  },\n  {\n    \"name\": \"Landmine Rainbow\"\n  },\n  {\n    \"name\": \"Lat and Triceps Stretch\"\n  },\n  {\n    \"name\": \"Long-Lever Plank\"\n  },\n  {\n    \"name\": \"Lying Dumbbell Triceps Extension\"\n  },\n  {\n    \"name\": \"Mountain Climber\"\n  },\n  {\n    \"name\": \"Neutral-Grip Cable Pull-Down\"\n  },\n  {\n    \"name\": \"Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Neutral-Grip Seated Band Row\"\n  },\n  {\n    \"name\": \"Neutral-Grip Seated Cable Row\"\n  },\n  {\n    \"name\": \"No Money Drill\"\n  },\n  {\n    \"name\": \"Overhead Band Pallof Press\"\n  },\n  {\n    \"name\": \"Overhead Band Press\"\n  },\n  {\n    \"name\": \"Overhead Band Triceps Extension\"\n  },\n  {\n    \"name\": \"Overhead Barbell Squat\"\n  },\n  {\n    \"name\": \"Overhead Cable Triceps Extension\"\n  },\n  {\n    \"name\": \"Overhead Dumbbell Reverse Lunge\"\n  },\n  {\n    \"name\": \"Pallof Press\"\n  },\n  {\n    \"name\": \"Pallof Press to Overhead\"\n  },\n  {\n    \"name\": \"Pallof Press With Band\"\n  },\n  {\n    \"name\": \"Pigeon Stretch\"\n  },\n  {\n    \"name\": \"Plank\"\n  },\n  {\n    \"name\": \"Plank Arm March\"\n  },\n  {\n    \"name\": \"Plate Squat\"\n  },\n  {\n    \"name\": \"Prisoner Squat\"\n  },\n  {\n    \"name\": \"Pronated-Grip Seated Band Row\"\n  },\n  {\n    \"name\": \"Pronated-Grip Seated Cable Row\"\n  },\n  {\n    \"name\": \"Prone Hip External Rotation\"\n  },\n  {\n    \"name\": \"Prone Hip Internal Rotation\"\n  },\n  {\n    \"name\": \"Prone Row to External Rotation\"\n  },\n  {\n    \"name\": \"Prone T Raise\"\n  },\n  {\n    \"name\": \"Prone Y Raise\"\n  },\n  {\n    \"name\": \"Prone YTI\"\n  },\n  {\n    \"name\": \"Pull-Up\"\n  },\n  {\n    \"name\": \"Pull-Up With Iso\"\n  },\n  {\n    \"name\": \"Pushup\"\n  },\n  {\n    \"name\": \"Pushup Iso\"\n  },\n  {\n    \"name\": \"Pushup to Single-Arm Support\"\n  },\n  {\n    \"name\": \"Quadruped Extension-Rotation\"\n  },\n  {\n    \"name\": \"Rack Pull\"\n  },\n  {\n    \"name\": \"Reach, Rock, Lift\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Barbell Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Bodyweight Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Dumbbell Split Squat Jump\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Goblet Split Squat\"\n  },\n  {\n    \"name\": \"Rear-Foot-Elevated Single-Arm Dumbbell Split Squat\"\n  },\n  {\n    \"name\": \"Renegade Row\"\n  },\n  {\n    \"name\": \"Renegade Row With Pushup\"\n  },\n  {\n    \"name\": \"Renegade Row With Pushup and Feet Elevated\"\n  },\n  {\n    \"name\": \"Reverse Crunch\"\n  },\n  {\n    \"name\": \"Reverse Landmine Lunge\"\n  },\n  {\n    \"name\": \"Reverse Lunge With Posterolateral Reach\"\n  },\n  {\n    \"name\": \"Reverse Pattern Single-Leg Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Ring Plank\"\n  },\n  {\n    \"name\": \"Ring Pushup\"\n  },\n  {\n    \"name\": \"Ring Row\"\n  },\n  {\n    \"name\": \"Ring Row With Feet Elevated\"\n  },\n  {\n    \"name\": \"Rocked-Back Quadruped Extension-Rotation\"\n  },\n  {\n    \"name\": \"Rocking Ankle Mobilization\"\n  },\n  {\n    \"name\": \"Salute Plank\"\n  },\n  {\n    \"name\": \"Scapular Pushup\"\n  },\n  {\n    \"name\": \"Scapular Wall-Slide\"\n  },\n  {\n    \"name\": \"Seated Dumbbell Curl\"\n  },\n  {\n    \"name\": \"Seated Dumbbell Overhead Press\"\n  },\n  {\n    \"name\": \"Side-Lying Banded External Rotation With Abduction\"\n  },\n {\n    \"name\": \"Side-Lying Dumbbell External Rotation With Abduction\"\n  },\n  {\n    \"name\": \"Side-Lying Extension Rotation\"\n  },\n  {\n    \"name\": \"Side-Lying Windmill\"\n  },\n  {\n    \"name\": \"Side Plank\"\n  },\n  {\n    \"name\": \"Single-Arm Band Pull-Apart\"\n  },\n  {\n    \"name\": \"Single-Arm Band Row”\n },\n {\n    \"name\": \"Single-Arm Dumbbell Step-Up\"\n  },\n  {\n    \"name\": \"Single-Arm Half-Kneeling Band Press\"\n  },\n  {\n    \"name\": \"Single-Arm Half-Kneeling Band Pull-Down\"\n  },\n  {\n    \"name\": \"Single-Arm Plank\"\n  },\n  {\n    \"name\": \"Single-Arm Seated Overhead Dumbbell Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Band Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Cable Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Band Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Band Row\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Cable Press\"\n  },\n  {\n    \"name\": \"Single-Arm Standing Split-Stance Cable Row\"\n  },\n  {\n    \"name\": \"Single-Arm Walking Dumbbell Farmer’s Carry\"\n  },\n {\n    \"name\": \"Single-Leg Band-Resisted Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Barbell Glute Bridge\"\n  },\n  {\n    \"name\": \"Single-Leg Barbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Eccentric Squat to Box\"\n  },\n  {\n    \"name\": \"Single-Leg Feet-Elevated Pushup\"\n  },\n  {\n    \"name\": \"Single-Leg Glute Bridge\"\n  },\n  {\n    \"name\": \"Single-Leg Hip Thrust\"\n  },\n  {\n    \"name\": \"Single-Leg Plank\"\n  },\n  {\n    \"name\": \"Single-Leg Pushup\"\n  },\n  {\n    \"name\": \"Single-Leg Single-Arm Dumbbell Romanian Deadlift\"\n  },\n  {\n    \"name\": \"Single-Leg Squat to Box\"\n  },\n  {\n    \"name\": \"Single-Leg Supine Hips-Elevated Leg Curl\"\n  },\n  {\n    \"name\": \"Spiderman Pushup\"\n  },\n  {\n    \"name\": \"Split-Stance Dumbbell Push Press\"\n  },\n  {\n    \"name\": \"Standing Barbell Overhead Press\"\n  },\n  {\n    \"name\": \"Standing Split-Stance Landmine Press\"\n  },\n  {\n    \"name\": \"Standing Thoracic Extension Rotation\"\n  },\n  {\n    \"name\": \"Stir-The-Pot\"\n  },\n  {\n    \"name\": \"Supine Glute Bridge\"\n  },\n  {\n    \"name\": \"Supine Psoas March\"\n  },\n {\n    \"name\": \"T-Bar Row\"\n  },\n  {\n    \"name\": \"T-Pushup\"\n  },\n  {\n    \"name\": \"Trap Bar Deadlift\"\n  },\n  {\n    \"name\": \"Triceps Press-Down\"\n  },\n  {\n    \"name\": \"Turkish Get-up\"\n  },\n  {\n    \"name\": \"Walking Dumbbell Cross-Carry\"\n  },\n  {\n    \"name\": \"Walking Dumbbell Lunge\"\n  },\n  {\n    \"name\": \"Walking Farmer's Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Heartbeat Carry\"\n  },\n  {\n    \"name\": \"Walking Goblet Lunge\"\n  },\n  {\n    \"name\": \"Walking Knee to Chest\"\n  },\n  {\n    \"name\": \"Walking Single-Arm Bottom-Up Kettlebell Racked Carry\"\n  },\n  {\n    \"name\": \"Walking Spiderman\"\n  },\n  {\n    \"name\": \"Walking Spiderman With Overhead Reach\"\n  },\n  {\n    \"name\": \"Walking Two-Arm Waiter’s Carry\"\n  },\n  {\n    \"name\": \"Walking Waiter's Carry\"\n  },\n  {\n    \"name\": \"Walking Warrior Lunge\"\n  },\n  {\n    \"name\": \"Wall Glute Iso March\"\n  },\n  {\n    \"name\": \"Wall Hip Flexor Mobilization\"\n  },\n  {\n    \"name\": \"Wall-Press Abs\"\n  },\n  {\n    \"name\": \"Warrior Lunge With Overhead Reach\"\n  },\n  {\n    \"name\": \"Weighted Chin-Up\"\n  },\n  {\n    \"name\": \"Weighted Neutral-Grip Pull-Up\"\n  },\n  {\n    \"name\": \"Weighted Pushup\"\n  },\n  {\n    \"name\": \"Weighted Ring Pushup\"\n  },\n  {\n    \"name\": \"X-Band Walk\"\n  },\n  {\n    \"name\": \"Yoga Downward Dog Stretch\"\n  }\n]\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2460, -740], "id": "de389ac8-a09c-49f3-a979-217fbf88eb74", "name": "Determine the excercises for the first workout", "retryOnFail": true}, {"parameters": {"operation": "getAll", "tableId": "exercises", "returnAll": true, "filters": {"conditions": [{"keyName": "name", "condition": "ilike", "keyValue": "={{ $json.name }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [3240, -440], "id": "fc65f287-0b14-4c50-8bb0-778f9b4c332f", "name": "Supabase2", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "include": "specifiedFields", "fieldsToInclude": "id, name", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [3440, -440], "id": "0f909198-f0aa-462d-816c-3059452b36e3", "name": "Aggregate3", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"content": "## Getting excercises IDs based on names\nHere the AI node previously provides the names which ids will be found for and then the name along with the ID will be given to create the first workout session next. have the split out and other data modifiers to make data consistent regardless of the output of AI", "height": 500, "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [2960, -600], "typeVersion": 1, "id": "be5bd976-29c8-46cc-bf09-7b023d412cec", "name": "Sticky Note4"}, {"parameters": {"tableId": "workouts", "fieldsUi": {"fieldValues": [{"fieldId": "name", "fieldValue": "={{ $json.name }}"}, {"fieldId": "user_id", "fieldValue": "={{ $json.user_id[0] }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2180, 40], "id": "da162076-7db3-469a-9704-577b74ae72ce", "name": "Supabase", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id", "renameField": true, "outputFieldName": "workout_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2400, 20], "id": "7634528c-88ac-4ea3-9c1e-d5261a4e55fa", "name": "Aggregate4", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"content": "### maybe need to expliciltly ask to suggest the most optimal split or workout schedule too. for example push pull legs. and later maybe to have a section in the o3 node later that will create a optimal schedule so the scheduler can follow", "height": 220}, "type": "n8n-nodes-base.stickyNote", "position": [160, -1020], "typeVersion": 1, "id": "a76830dc-0720-4142-9ba6-0763d27f0a29", "name": "Sticky Note5"}, {"parameters": {"content": "## Creating the first workout session\n\nusing user id a workout session named first workout will be made so excercises can be put under", "height": 340, "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [1120, -200], "typeVersion": 1, "id": "e151b56c-e338-498f-aaf4-3dafdd2e17b6", "name": "Sticky Note6"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Workout Session\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"name\": {\n      \"type\": \"array\",\n      \"description\": \"An array representing the names of the exercises. Each name must match exactly with the provided exercise names list.\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"sets\": {\n      \"type\": \"array\",\n      \"description\": \"An array representing the number of sets for each exercise (int4 in PostgreSQL).\",\n      \"items\": {\n        \"type\": \"integer\",\n        \"minimum\": 1\n      }\n    },\n    \"reps\": {\n      \"type\": \"array\",\n      \"description\": \"An array representing the number of repetitions for each set of exercises (int4 in PostgreSQL).\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"weight\": {\n      \"type\": \"array\",\n      \"description\": \"An array representing the weight in pounds used for each exercise (integer, use pounds).\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"order_index\": {\n      \"type\": \"array\",\n      \"description\": \"An array representing the order of each exercise in the workout session.\",\n      \"items\": {\n        \"type\": \"integer\",\n        \"minimum\": 1\n      }\n    }\n  },\n  \"required\": [\"name\", \"sets\", \"reps\", \"weight\", \"order_index\"],\n  \"additionalProperties\": false\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2620, -520], "id": "1f151002-58c0-4a93-bd1e-63af437b5fb5", "name": "Structured Output Parser"}, {"parameters": {"content": "## Comment:\n**we should probably reduce the number of ecercises names given here by removing excercises that most people dont use ", "height": 120, "width": 320}, "type": "n8n-nodes-base.stickyNote", "position": [2260, -540], "typeVersion": 1, "id": "fd0cd3ff-fbe2-4eb4-8c1c-ac16a9e6091f", "name": "Sticky Note7"}, {"parameters": {"content": "## identifying excercises names for first workout. Probably need to include other attributes such as equipment needed for each name later on. maybe in the form of XML tags or jsons. ", "height": 280, "width": 360}, "type": "n8n-nodes-base.stickyNote", "position": [2440, -980], "typeVersion": 1, "id": "822c506b-fb33-4fd4-96ed-4bacc08137c4", "name": "Sticky Note8"}, {"parameters": {"tableId": "workout_exercises", "fieldsUi": {"fieldValues": [{"fieldId": "workout_id", "fieldValue": "={{ $json.workout_id[0] }}"}, {"fieldId": "exercise_id", "fieldValue": "={{ $json.excercise_id.id }}"}, {"fieldId": "sets", "fieldValue": "=\n{{ $json.sets }}"}, {"fieldId": "weight", "fieldValue": "={{ $json.weight }}\n"}, {"fieldId": "order_index", "fieldValue": "={{ $json.order_index }}\n"}, {"fieldId": "reps", "fieldValue": "={{ $json.reps }}"}, {"fieldId": "name", "fieldValue": "={{ $json.excercise_id.name }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [4420, -500], "id": "484598f2-ce4d-448a-89c2-7fad0df1879c", "name": "Supabase3", "credentials": {"supabaseApi": {"id": "uVVxVAJWIoyWMJ4R", "name": "Supabase account"}}}, {"parameters": {"fieldToSplitOut": "output.name, data, output.sets, output.reps, output.weight, output.order_index", "include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldsToInclude": "workout_id, query.workout_id", "options": {"destinationFieldName": "=name, excercise_id, sets, reps, weight, order_index"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [4140, -480], "id": "7bcdc02b-553f-4c34-81e3-c9d7c29e92ef", "name": "Split Out3"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [5000, -560], "id": "9e04b2a6-194f-4d8f-88b9-8881e0f6f3c6", "name": "Aggregate5", "notes": "For some reason the output receved by AI agent is only one item at a time so here we are combining different json outputs into one jason outputs. works like a charm so far. "}, {"parameters": {"content": "## Comments about user data input\n**Probably best to not include session time limitation and focus on overall strategy. then the time limits will be given to the exercise selector agent. \nFilter out any data relevant to each flow. better to receive all user data and filter here ", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "position": [-2400, -1000], "typeVersion": 1, "id": "fb5456fc-2135-4510-a404-e87bbc8bf440", "name": "Sticky Note9"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "body.user_input"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-1220, -840], "id": "4252b05e-6934-470a-9987-1a148405812c", "name": "Aggregate6"}, {"parameters": {"assignments": {"assignments": []}, "includeOtherFields": true, "include": "except", "excludeFields": "user_input[0][\"desired-workout-duration\"], user_input[0].equipment, user_input[0][\"flexible-duration\"], user_input[0].completed_at, user_input[0].workout_preferences", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, -620], "id": "f3db670d-7ccd-4475-84bd-a366517f36d4", "name": "Edit Fields1"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "body.user_id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [620, -100], "id": "59c43135-fe8d-4e63-baf8-b10bf45cd231", "name": "Aggregate7", "retryOnFail": true}, {"parameters": {"assignments": {"assignments": [{"id": "85237343-9d73-4177-a7ca-34d451127c56", "name": "name", "value": "first workout", "type": "string"}, {"id": "5b4bdac3-9f6b-49ce-bd6f-a9ef40b53ffe", "name": "description", "value": "User's first workout made after onboarding", "type": "string"}]}, "includeOtherFields": true, "include": "selected", "includeFields": "user_id", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [840, -100], "id": "638d4665-beef-4c88-a9fc-eb52246c4637", "name": "<PERSON>"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [2080, -1400], "id": "f28d5af2-5826-41c2-8fee-aa3b865e4532", "name": "Merge1", "retryOnFail": true}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [2160, -720], "id": "3f21f2cf-9571-4c07-b5e6-8b38c89aa852", "name": "Merge2", "retryOnFail": true}, {"parameters": {"fieldToSplitOut": "output.name, output.sets, output.reps, output.weight, output.order_index", "include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldsToInclude": "workout_id, query.workout_id", "options": {"destinationFieldName": "=name, sets, reps, weight, order_index"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2820, -500], "id": "d6d0b1d8-f12b-41cb-9004-96af97003858", "name": "Split Out4"}, {"parameters": {"content": "Needs more prompt engineering. also from user information other things such as user requested duration needs to be included, right now it is not ", "height": 140}, "type": "n8n-nodes-base.stickyNote", "position": [2780, -920], "typeVersion": 1, "id": "eb082033-69ec-4a64-bb0c-e164685b9b9c", "name": "Sticky Note12"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [3840, -440], "id": "7a070ddc-09f7-42af-bc79-241a2acf8860", "name": "Merge3", "retryOnFail": true}, {"parameters": {"content": "### Maybe add the fact that what is the most ", "height": 140}, "type": "n8n-nodes-base.stickyNote", "position": [640, -1020], "typeVersion": 1, "id": "45ead4de-0499-4078-b4ba-9d1fdfe31e9b", "name": "Sticky Note13"}, {"parameters": {"content": "maybe adding an AI summarizier of user preferences and inputs to feed to deep research would make it better than feeding raw data, same also with summarizing the input for the AI choosing the  workouts"}, "type": "n8n-nodes-base.stickyNote", "position": [-680, -900], "typeVersion": 1, "id": "e055c168-2566-4cce-8bba-383786adc666", "name": "Sticky Note17"}, {"parameters": {"content": "two things. \nRest interval for each excercise sets is now under the workout excercises table which will allow for dynamic changes based on user performance. \n\ntwo, I think we shuld change the reps under workout excercises to be defined as array and let the AI have dynamic reps for each set so not all 3 sets in each excercise for examole would have to have the same number of reps.\n\n**changed reps and weight to arrays. need to change here", "height": 420}, "type": "n8n-nodes-base.stickyNote", "position": [1740, -1280], "typeVersion": 1, "id": "6df30baa-a314-4dec-b46f-753e0fb356b7", "name": "Sticky Note18"}, {"parameters": {"assignments": {"assignments": [{"id": "22b0d1af-996f-4885-8b60-cde059237547", "name": "workout_name", "value": "={{ $json.workout_name }}", "type": "string"}, {"id": "790d7456-6397-4ee8-b7fd-1a3f16969db6", "name": "planned_workout", "value": "={{ $json.planned_workout }}", "type": "string"}, {"id": "8840bb15-602b-4885-a742-cdab186acb01", "name": "actual_workout", "value": "={{ $json.actual_workout }}", "type": "string"}, {"id": "38a1cb12-2022-4445-8690-f37bae9ad2bd", "name": "user_workout_feedback", "value": "={{ $json.feedback }}", "type": "string"}, {"id": "8190aa43-575e-4035-9fe7-fa3e8c345ab9", "name": "additional_metrics", "value": "={{ $json.additional_metrics }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-240, -920], "id": "ee534c7e-f3d4-47bc-a2cb-654adad775fe", "name": "Edit Fields2"}, {"parameters": {"content": "## Implement this\n\nThis is how can stringify the input so not have to have it individually in the prompt as it is right now. this will allow less mistakes to happen  and have everything togetehr, bekow is how using for next workout agent,", "height": 200}, "type": "n8n-nodes-base.stickyNote", "position": [-280, -1120], "typeVersion": 1, "id": "31f96e77-30a4-46b7-82d1-48b5084cfa9b", "name": "Sticky Note19"}, {"parameters": {"content": "need to implement a question in on boarding that asks what is your best guess in the weight you can bench press 12 times (barbel alone is 40 pounds) and maybe also squat?. maybe also a better way to get an estimate. how many pushups? and situps squats at home? so user can do it right there", "height": 700}, "type": "n8n-nodes-base.stickyNote", "position": [-2940, -700], "typeVersion": 1, "id": "54ed52f8-468e-4c5d-bb04-94081253a3bd", "name": "Sticky Note20"}, {"parameters": {"content": "I beleive we have to also make this AI create json format fitness guide with different sections similar to how we are making AI summary for each workout finsihed. this way one seciton can be for suggested workout sessions and rotations to follow.", "height": 200}, "type": "n8n-nodes-base.stickyNote", "position": [1380, -640], "typeVersion": 1, "id": "f0ff1092-1b8b-4e24-9a1e-39e415a487e3", "name": "Sticky Note21"}, {"parameters": {"content": "for AI summaries and descriptions, do a deep research to see what points and things would be most interesting or important to include in htose descriptions for both the user to see but also next AI"}, "type": "n8n-nodes-base.stickyNote", "position": [-1780, -1260], "typeVersion": 1, "id": "d2cb5961-b393-44e1-9759-a7f5021e673e", "name": "Sticky Note1"}, {"parameters": {"content": "for AI summaries and descriptions, do a deep research to see what points and things would be most interesting or important to include in htose descriptions for both the user to see but also next AI"}, "type": "n8n-nodes-base.stickyNote", "position": [-1780, -1260], "typeVersion": 1, "id": "81c6862d-e4bf-4e40-bd0c-841ef70ae603", "name": "Sticky Note10"}, {"parameters": {"content": "for AI summaries and descriptions, do a deep research to see what points and things would be most interesting or important to include in htose descriptions for both the user to see but also next AI"}, "type": "n8n-nodes-base.stickyNote", "position": [-1780, -1260], "typeVersion": 1, "id": "68365a12-8eaa-45a6-9c62-8391345a24f7", "name": "Sticky Note11"}, {"parameters": {"content": "for onboarding questions, would need probabkly deep research to see what to include and what more to indclude as well. also perhaps having a user input for each page as a comment anyways"}, "type": "n8n-nodes-base.stickyNote", "position": [-1340, -1280], "typeVersion": 1, "id": "95d48272-4ac6-4f5c-8d11-8ffa36fe5f5e", "name": "Sticky Note14"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "sciwell.app.n8n.cloud", "user-agent": "Expo/2.32.17 CFNetwork/1568.300.101 Darwin/24.3.0", "content-length": "785", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2600:4040:25fe:200:84f2:7ffa:6012:baa2", "cf-ew-via": "15", "cf-ipcountry": "US", "cf-ray": "91df631785572003-IAD", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "x-forwarded-for": "2600:4040:25fe:200:84f2:7ffa:6012:baa2, *************", "x-forwarded-host": "sciwell.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-9-7bb7d4c57c-cq6w8", "x-is-trusted": "yes", "x-real-ip": "2600:4040:25fe:200:84f2:7ffa:6012:baa2"}, "params": {}, "query": {}, "body": {"user_id": "4d8a8d40-def8-4179-a4cd-ec3eebbbdeb8", "user_input": {"display_name": "<PERSON><PERSON>", "gender": "male", "age": 27, "height": 5, "weight": 8, "height_unit": "ft", "weight_unit": "lbs", "primarygoal": ["sport_training"], "secondary_goal": "increase strength", "cardio_fitness": {"level": "4", "description": "Advanced - Strong cardio capacity, can perform for 45+ minutes"}, "weightlifting_fitness": {"level": "2", "description": "Novice - Familiar with basic lifts, developing strength"}, "equipment": ["kettlebells", "large_gym"], "desired-workout-duration": "30", "flexible-duration": false, "additional_notes": "", "fitness_additional_info": "", "completed_at": "2025-03-10T02:29:54.439Z", "workout_preferences": {"weight_unit_preference": "lbs", "auto_advance_to_next_set": true, "openfit_weight_unit_preference": "lbs"}}}, "webhookUrl": "https://sciwell.app.n8n.cloud/webhook/7364c003-28fa-48e5-b9dc-6c20e9b56b8d", "executionMode": "production"}}], "Edit Fields2": [{"json": {"workout_name": "Full Body Strength Training", "planned_workout": "{\"workout_name\":\"Full Body Strength Training\",\"exercises\":[{\"order\":1,\"exercise\":\"Bench Press\",\"planned_sets\":3,\"planned_reps\":10,\"planned_weight\":135,\"rest_interval\":90},{\"order\":2,\"exercise\":\"Squat\",\"planned_sets\":3,\"planned_reps\":8,\"planned_weight\":185,\"rest_interval\":120}]}", "actual_workout": "{\"workout_name\":\"Full Body Strength Training\",\"exercises\":[{\"exercise\":\"Bench Press\",\"actual_sets\":[{\"set_order\":1,\"performed_reps\":10,\"performed_weight\":135,\"rep_difference\":0,\"set_feedback_difficulty\":\"moderate\"},{\"set_order\":2,\"performed_reps\":10,\"performed_weight\":135,\"rep_difference\":0,\"set_feedback_difficulty\":\"moderate\"},{\"set_order\":3,\"performed_reps\":8,\"performed_weight\":135,\"rep_difference\":2,\"set_feedback_difficulty\":\"hard\"}]},{\"exercise\":\"Squat\",\"actual_sets\":[{\"set_order\":1,\"performed_reps\":8,\"performed_weight\":185,\"rep_difference\":0,\"set_feedback_difficulty\":\"easy\"},{\"set_order\":2,\"performed_reps\":8,\"performed_weight\":185,\"rep_difference\":0,\"set_feedback_difficulty\":\"moderate\"},{\"set_order\":3,\"performed_reps\":7,\"performed_weight\":185,\"rep_difference\":1,\"set_feedback_difficulty\":\"hard\"}]}]}", "user_workout_feedback": "Overall, I felt strong on bench press but was challenged by the squats towards the end.", "additional_metrics": "{\"duration\":60,\"calories_burned\":450}"}}]}, "connections": {"Webhook": {"main": [[{"node": "Taking User Id", "type": "main", "index": 0}, {"node": "Aggregate6", "type": "main", "index": 0}, {"node": "Aggregate7", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Fitness report writer for agents", "type": "ai_languageModel", "index": 0}, {"node": "Determine the excercises for the first workout", "type": "ai_languageModel", "index": 0}]]}, "Merge": {"main": [[{"node": "Fitness report writer for agents", "type": "main", "index": 0}, {"node": "Article preview for user", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Deep Search Perplexity": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Fitness report writer for agents": {"main": [[{"node": "Aggregate2", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Article preview for user": {"main": [[]]}, "Taking User Id": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Aggregate2": {"main": [[{"node": "Merge2", "type": "main", "index": 0}]]}, "Determine the excercises for the first workout": {"main": [[{"node": "Split Out4", "type": "main", "index": 0}, {"node": "Merge3", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Aggregate3", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "Aggregate4", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Determine the excercises for the first workout", "type": "ai_outputParser", "index": 0}]]}, "Supabase3": {"main": [[{"node": "Aggregate5", "type": "main", "index": 0}]]}, "Split Out3": {"main": [[{"node": "Supabase3", "type": "main", "index": 0}]]}, "Aggregate4": {"main": [[{"node": "Merge3", "type": "main", "index": 2}]]}, "Aggregate3": {"main": [[{"node": "Merge3", "type": "main", "index": 1}]]}, "Aggregate6": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Deep Search Perplexity", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "Merge2", "type": "main", "index": 1}]]}, "Aggregate7": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Determine the excercises for the first workout", "type": "main", "index": 0}]]}, "Split Out4": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Split Out3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3d28f64d-36c1-42a5-ad76-07764a4ced7e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6195c139c872709f5410ba81a9d6cc1e5c549703764aa55ff6b328be53cf73c2"}, "id": "D6DkzCept8tD7lYX", "tags": [{"createdAt": "2025-01-20T02:47:40.139Z", "updatedAt": "2025-01-20T02:47:40.139Z", "id": "OG1V0ctH1pvPrnbo", "name": "Ready For testing in app"}, {"createdAt": "2025-01-25T17:15:32.668Z", "updatedAt": "2025-01-25T17:15:32.668Z", "id": "gvHNr7P1PzjMofy4", "name": "Agents"}]}