const https = require('https');
const fs = require('fs');

// Read the test data
const testData = JSON.parse(fs.readFileSync('./test-sample.json', 'utf8'));

// Function to call the Firebase Function
async function testWorkoutAgent() {
  const postData = JSON.stringify(testData);
  
  const options = {
    hostname: '127.0.0.1',
    port: 5001,
    path: '/po2vf2ae7tal9invaj7jkf4a06hsac/us-central1/generateNextWorkout',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result);
        } catch (e) {
          resolve(data);
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Run the test
console.log('🏋️ Testing Next Workout Creator Agent...\n');
console.log('📋 Input Data:');
console.log(JSON.stringify(testData, null, 2));
console.log('\n⏳ Generating next workout...\n');

testWorkoutAgent()
  .then(result => {
    console.log('✅ Response:');
    console.log(JSON.stringify(result, null, 2));
  })
  .catch(error => {
    console.error('❌ Error:', error);
  });