{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(firebase deploy:*)", "Bash(firebase functions:list:*)", "Bash(firebase functions:config:get:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__firebase__firebase_get_environment", "Bash(npm install:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run serve:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run genkit:start:*)", "Bash(genkit flow:run nextWorkoutCreatorFlow --input '{\"userId\": \"test123\", \"fitness_guide\": \"Focus on compound movements for strength building.\", \"just_finished_workout_ai_summary\": \"Last workout: Bench press 3x8 at 135lbs, completed all reps with good form.\", \"previous_workout_summaries_and_dates\": \"Previous session was upper body strength training.\", \"user_preferences\": \"Goal: Build strength. Experience: Intermediate. Equipment: Full gym.\"}')", "Bash(genkit:*)", "Bash(wc:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)"], "deny": []}}