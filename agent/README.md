# AI Fitness Agent - Genkit Firebase Functions

A personalized workout generation agent built with Firebase Genkit that creates custom workout plans based on user profiles and workout history.

## Features

- **Generate Personalized Workouts**: AI-powered workout plan generation using Gemini 2.0 Flash
- **Firebase Integration**: Seamless integration with Firestore for data storage
- **Type Safety**: Full TypeScript support with Zod schema validation
- **Smart JSON Parsing**: Handles LLM responses with markdown formatting

## Architecture

- **Framework**: Firebase Genkit
- **AI Model**: Vertex AI Gemini 2.0 Flash
- **Database**: Cloud Firestore
- **Deployment**: Cloud Functions for Firebase
- **Authentication**: Firebase Auth (configurable)

## Prerequisites

- Node.js 22+
- Firebase CLI
- Google Cloud Project with Vertex AI API enabled
- Firebase project on Blaze plan

## Setup

### 1. Clone and Install Dependencies

```bash
git clone <your-repo>
cd aifit
npm install
```

### 2. Firebase Configuration

```bash
# Login to Firebase
firebase login

# Set your project
firebase use <your-project-id>
```

### 3. Environment Variables

Set up your API key in Cloud Secret Manager:

```bash
firebase functions:secrets:set GOOGLE_GENAI_API_KEY
```

### 4. Firestore Collections

Ensure your Firestore has these collections:
- `user_profiles` - User profile data
- `userWorkouts` - User workout history
- `workoutPlans` - Generated workout plans (auto-created)

## Development

### Local Development

```bash
# Start the Genkit development server
npm run genkit:start

# Or manually
./node_modules/.bin/genkit start -- tsx --watch src/genkit-sample.ts
```

Access the Genkit Developer UI at: http://localhost:4000

### Testing the Flow

**Generate Workout**:
- Input: `{"userId": "your-user-id"}`
- Returns: Personalized workout plan

## Deployment

### 1. Deploy to Firebase

```bash
firebase deploy --only functions
```

### 2. Enable Required Services

In Google Cloud Console:
1. **Enable Vertex AI API** for your project
2. **Grant IAM permissions**: Ensure Default compute service account has "Vertex AI User" role

## API Usage

### Using Firebase Functions SDK

```javascript
import { getFunctions, httpsCallable } from 'firebase/functions';

const functions = getFunctions();
const generateWorkout = httpsCallable(functions, 'generateAndSaveWorkout');

// Generate workout
const result = await generateWorkout({ userId: 'user123' });
console.log(result.data);
```

### Response Format

```json
{
  "success": true,
  "workoutPlan": {
    "id": "generated-id",
    "name": "Upper Body Strength",
    "description": "Focused on building upper body strength",
    "exercises": [
      {
        "name": "Push-ups",
        "sets": 3,
        "reps": 12,
        "duration": "30 seconds"
      }
    ],
    "duration": "45 minutes",
    "difficulty": "intermediate"
  }
}
```

## Data Models

### User Profile
```typescript
{
  userId: string;
  age?: number;
  fitnessLevel?: string;
  goals?: string[];
  preferences?: string[];
}
```

### Workout Plan
```typescript
{
  name: string;
  description: string;
  exercises: Array<{
    name: string;
    sets?: number;
    reps?: number;
    duration?: string;
  }>;
  duration: string;
  difficulty: string;
}
```

## Security

- API keys stored in Cloud Secret Manager
- Optional authentication and App Check enforcement
- CORS policies configurable

## Monitoring

- Built-in Firebase telemetry
- Error logging with structured data
- Genkit Developer UI for flow debugging

## License

MIT License

## Support

For issues and questions:
- Check the [Genkit documentation](https://genkit.dev/docs/firebase/)
- Review Firebase Functions logs
- Use the Genkit Developer UI for debugging 