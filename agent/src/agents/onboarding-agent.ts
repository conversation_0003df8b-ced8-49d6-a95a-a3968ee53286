import {z} from "genkit";
import {onCallGenkit} from "firebase-functions/https";
import {ai, api<PERSON>ey} from "../shared/genkit";
import {FirstWorkoutSchema, UserInputSchema} from "../shared/schemas";
import {exerciseList} from "../shared/exercises";
import {getFirestoreInstance} from "../shared/firebase";
import {cleanJsonResponse} from "../shared/utils";

// Define onboarding agent flow that generates fitness guide and first workout
const onboardingAgentFlow = ai.defineFlow({
  name: "onboardingAgentFlow",
  inputSchema: z.object({
    userId: z.string().describe("The user's unique ID"),
    userInput: UserInputSchema,
  }),
  outputSchema: z.object({
    success: z.boolean(),
    fitnessGuide: z.string().optional(),
    firstWorkout: z.any().optional(),
    error: z.string().optional(),
  }),
}, async ({userId, userInput}) => {
  try {
    // Step 1: Generate research-based fitness guide
    const researchPrompt = `Name: ${userInput.display_name}, Gender: ${userInput.gender}, age: ${userInput.age}, height: ${userInput.height} weight: ${userInput.weight} height unit: ${userInput.height_unit}, weight unit: ${userInput.weight_unit}, primary goal: ${userInput.primarygoal[0]}, secondary goals: ${userInput.secondary_goal}, cardio level: ${userInput.cardio_fitness.description}, weightlifting level: ${userInput.weightlifting_fitness.description}, additional comments: ${userInput.additional_notes}, ${userInput.fitness_additional_info}`;

    const researchSystemMessage = `You are a fitness-focused expert researcher with the primary task of writing a fitness guide for the users of a fitness app. You receive explicit user provided information detailing their fitness goals, preferences, constraints, and any relevant context. Your task is to synthesize this information with scientifically validated fitness principles to generate a hyper-personalized long term reference guide. This guide you write will serve as a flexible resource to make informed decisions on creating the user's next workout each time the user is ready for their next workout based on factors such as user's last session's performance and time elapsed since the previous workout. Guidelines: Strictly Use Provided Information. Work exclusively with the data and constraints provided by the user. Do not infer or assume additional details. Ensure the guide remains hyper personalized to user's provided information. Comprehensive Yet Flexible Reference: Deliver a long term reference guide not a rigid day by day schedule. Ensure the guide is adaptable for both home and gym settings without relying on specific equipment. Explain the general guidelines on the types of exercises most effective in each phase of a user's journey toward their fitness goals. This must include explanations of types of excercises most suitable for the user. Core Focus Areas: Detail fundamental workout principles, recovery, and progression strategies. If applicapable, Include general research backed cardio recommendations as a supplementary element and explain the optimal way to incorporate these into the user's journey if needed. Outline how to transition between different training phases as the user gains experience and moves forward. Technical Accuracy and Personalization: Use technical research backed language as needed to ensure high accuracy. Incorporate the most rigorous and most applicable fitness concepts tailored to the user's explicit information. If the user mentions any injuries, provide relevant injury aware guidance; otherwise, focus solely on core workout principles, recovery, and progression. Decision Making Support: The guide should empower users and schedulers to decide on workouts by considering what was done in the last session. Ensure the final response is both well rounded, covering a variety of exercise and progression considerations, and adaptable so the user can decide which exercises to do based on their recent workout history. Do not generate tables or strict workout plans. This guide is primarily a reference that will be consulted each time the user wants to work out. The user already uses an app for tracking, so do not make additional tracking suggestions. Output Format: Present the response as a comprehensive written report without using tables. Ensure the language is clear, thorough, and serves as an enduring reference for making future workout decisions.`;

    const {text: researchResponse} = await ai.generate({
      model: "vertexai/gemini-2.0-flash",
      prompt: researchPrompt,
      system: researchSystemMessage,
      config: {
        temperature: 0.7,
        maxOutputTokens: 2048,
      },
    });

    // Step 2: Generate comprehensive fitness guide using research
    const fitnessGuidePrompt = `###User Data: 
"Name: ${userInput.display_name}, Gender: ${userInput.gender}, age: ${userInput.age}, height: ${userInput.height} weight: ${userInput.weight} height unit: ${userInput.height_unit}, weight unit: ${userInput.weight_unit}, primary goal: ${userInput.primarygoal[0]}, secondary goals: ${userInput.secondary_goal}, cardio level: ${userInput.cardio_fitness.description}, weightlifting level: ${userInput.weightlifting_fitness.description}, additional comments: ${userInput.additional_notes}, ${userInput.fitness_additional_info}

###
Note that the internatl thought processes of the resaercher agent is enclosed between <think>. you should ignore the thought processes and focus on the output.
External Research Agent Input: ${researchResponse}

### No need to include nutritional guidlines and focus on fitness. The primary usage of your report is to be referenced everytime a workout session is created for the user. Therefore, it is a reference report and not seen by the user himself but will be referenced by an expert to create new workout sessions for him as he moves forward.`;

    const fitnessGuideSystemMessage = `### Role:
You are a fitness-focused expert writer with the primary task of writing a fitness guide for the users of a fitness app. You receive explicit user-provided information detailing their fitness goals, preferences, constraints, and any relevant context along with a personlized research article. Your task is to synthesize this information with scientifically validated fitness principles to generate a hyper-personalized, long-term reference guide. This guide will serve as a flexible resource to make informed decisions on creating the user's next workout each time the user is ready for their next workout based on factors such as user's last session's performance, and time elapsed since the previous workout.

### Inputs processing: 
- You recive User information. 
- You also recive a a research article generated by a researcher AI. The researcher also hyperpersonlaized the research article based on the user's information. You must incorporate this research article in your thinking and writing. 
- Do not hellucinate or make up any assumptions outside the user's infomration, the resaerch article provided and what you know to be definitley true. 
- The thought processes and reasoning of the AI researcher is enclosed between <think> Which you should ignore and only focus on the output. 

###Your output must adhere to the following guidelines:

1. Strictly Use Provided Information:

- Work exclusively with the explicit data and constraints provided by the user. Do not infer or assume additional details.

- Ensure the guide remains hyper personlized to user's provided infomration

2. Comprehensive Yet Flexible Reference:

- Deliver a long-term reference guide—not a rigid, day-by-day schedule.
- Ensure the guide is adaptable for both home and gym settings without relying on specific equipment.
- Explain the general guidelines on the types of exercises most effective in each phase of a user's journey toward their fitness goals. This must include explanations of types of excercises most suitable for the user. 

3. Core Focus Areas:

- Detail fundamental workout principles, recovery, and progression strategies.
- Include general, research-backed cardio recommendations as a -supplementary element and what is the optimal way to incorporate into user's journey if needed. 
- Outline how to transition between different training phases as the user gains experience and moves forward. 
- include the most optimal splits or workouts plan strategies for the user but keep it flexible and do not provide a strict weekly routine but suggessions based on what would be optimal. 

4. Technical Accuracy and Personalization:

- Use technical, research-backed language as needed to ensure high accuracy.
- Incorporate the latest and most applicable fitness concepts tailored to the user's explicit information.
- If the user mentions any injuries, provide relevant injury-aware guidance; otherwise, focus solely on core workout principles, recovery, and progression. 

5. Decision-Making Support:

- The guide should empower users and scheduler to decide on workouts by considering what they did in their last session.
- Ensure your final response is both well-rounded (covering a variety of exercise and progression considerations) and adaptable (so the user can decide which exercises to do based on their recent workout history).
- Do not generate tables or strict workout plans. this guide is primarily a reference that will be consulted each time user wants to workout
- The user already uses an app for tracking, so do not make additional tracking suggestions.

6. Output Format:

- Present your response as a comprehensive, written report without using tables.
- Ensure the language is clear, thorough, and serves as an enduring reference for making future workout decisions
- Include an optimal routine example that can be followed for the user. This should not be a weekly schedule with exact days but it should be a routine splits that can be followed such as push pull legs or whatever makes the most sense for the user.`;

    const {text: fitnessGuide} = await ai.generate({
      model: "vertexai/gemini-2.0-flash",
      prompt: fitnessGuidePrompt,
      system: fitnessGuideSystemMessage,
      config: {
        temperature: 0.7,
        maxOutputTokens: 2048,
      },
    });

    // Step 3: Generate first workout based on fitness guide
    const firstWorkoutPrompt = `This is the Hyper personlized fitness guide for the user which you must reference in selecting the best exercises for his first workout: ${fitnessGuide}

### Here is users data You must also take into consideration the equipment available to the user. User Data: 
"Name: ${userInput.display_name}, Gender: ${userInput.gender}, age: ${userInput.age}, height: ${userInput.height} weight: ${userInput.weight} height unit: ${userInput.height_unit}, weight unit: ${userInput.weight_unit}, primary goal: ${userInput.primarygoal[0]}, secondary goals: ${userInput.secondary_goal}, cardio level: ${userInput.cardio_fitness.description}, weightlifting level: ${userInput.weightlifting_fitness.description}, additional comments: ${userInput.additional_notes}, ${userInput.fitness_additional_info}`;

    const firstWorkoutSystemMessage = `Based on the excercise list below and the given the user's personal fitness report you have recieved, your job is to create the first workout session for the user. The user's situation along with recommendations about his fitness plan are described in the report and You must leverage the given report to you in the user's personlized fitness to ensure that the first workout is hyper personlized for the user's situation and needs. You are creating the first workout session by outputting a JSON which includes the list of excercises for the user's first workout. The fitness guide provided to you should be your reference for making these selections. guide along with deeply thinking about each excercises below and which ones would be the best selections for the first workout session for the user based on his personal fitness report and user's information given to you. 

### Objectives and Important Notes:
You must provide the names of the excercise in the JSON schema format given to you along with the sets, reps, weight in lbs and order_index for each excercise. 
- You are creating the first ever workout session for the user that he does
- You must take into consideration user's provided info especailly equipment available, along with the guide provided.
- The order_index number for each excercise is the order in which the user will do the excercise during their workout.
- You must make sure each of these enteries are personilzed based on the User's personal fitness guide. 
- You must leverage the fitness guide provided to you to create the list of excercises for the user's first workout session. 
- DO NOT make any changes to the Names of the excercises and write them in your output JSON as exactly how they are written in the list below. 
- Double check that the names are spelled correctly and exactly represent how they are written in the list.
- Do not make any assumptions or hellucinations about the user's goals and excercises avaialble outside of the information given to you.
- you must double check to ensure that these excercises reflect the hyper personlized user fitness guide given to you. 

### here is an example of JSON output you must do the JSON schema is also provided to you: 
{
  "name": ["Bench Press", "Squat"],
  "sets": [3, 4],
  "reps": [10, 8],
  "weight": [150, 200],
  "order_index": [1, 2]
}

### These are the list of excercies that you can choose from, make sure you use the names exactly as they are. The are in alphabetical order and therefore you have to determine the best exercises by making sure you take into consideration each exercise that closely resembles what you have decided to be the next workout for the user. In order to optimize your work, first think about what kind of exercises are best for the user and then go over each exercise in the list below to select based on which one in the list resembles closest to your determination of user's needs.

Exercise List: ${exerciseList.join(", ")}`;

    const {text: firstWorkoutResponse} = await ai.generate({
      model: "vertexai/gemini-2.0-flash",
      prompt: firstWorkoutPrompt,
      system: firstWorkoutSystemMessage,
      config: {
        temperature: 0.7,
        maxOutputTokens: 1024,
      },
    });

    // Parse first workout response
    const cleanFirstWorkout = cleanJsonResponse(firstWorkoutResponse);
    let firstWorkout: any;
    try {
      firstWorkout = JSON.parse(cleanFirstWorkout);
      FirstWorkoutSchema.parse(firstWorkout);
    } catch (parseError: any) {
      console.error("JSON parse error for first workout:", parseError);
      throw new Error(`Invalid JSON response from AI: ${parseError.message}`);
    }

    // Save fitness guide to user profile
    await getFirestoreInstance()
      .collection("user_profiles")
      .doc(userId)
      .update({
        fitness_guide: fitnessGuide,
        onboarding_completed: true,
        updated_at: new Date(),
      });

    // Create first workout session
    const workoutDoc = await getFirestoreInstance()
      .collection("workoutPlans")
      .add({
        userId,
        name: "first workout",
        description: "User's first workout made after onboarding",
        createdAt: new Date(),
        source: "onboarding_agent",
      });

    // Save exercises for first workout
    const exerciseBatch = getFirestoreInstance().batch();
    firstWorkout.name.forEach((exerciseName: string, index: number) => {
      const exerciseRef = getFirestoreInstance().collection("workout_exercises").doc();
      exerciseBatch.set(exerciseRef, {
        workout_id: workoutDoc.id,
        exercise_name: exerciseName,
        sets: firstWorkout.sets[index],
        reps: firstWorkout.reps[index],
        weight: firstWorkout.weight[index],
        order_index: firstWorkout.order_index[index],
        rest_interval: 90, // Default rest interval
        createdAt: new Date(),
      });
    });
    
    await exerciseBatch.commit();

    return {
      success: true,
      fitnessGuide,
      firstWorkout: {
        id: workoutDoc.id,
        ...firstWorkout,
      },
    };
  } catch (err: any) {
    console.error("Error in onboarding agent:", err);
    return {
      success: false,
      error: err.message || "Failed to complete onboarding",
    };
  }
});

export const onboardingAgent = onCallGenkit({
  secrets: [apiKey],
  timeoutSeconds: 540,
  memory: "2GiB",
  maxInstances: 10,
}, onboardingAgentFlow);