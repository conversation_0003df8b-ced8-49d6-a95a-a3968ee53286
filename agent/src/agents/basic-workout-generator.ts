import {z} from "genkit";
import {onCallGenkit} from "firebase-functions/https";
import {ai, apiKey} from "../shared/genkit";
import {WorkoutPlanSchema} from "../shared/schemas";
import {getFirestoreInstance} from "../shared/firebase";
import {cleanJsonResponse} from "../shared/utils";

// Define a flow to generate and save a basic workout plan for a user
const generateAndSaveWorkoutFlow = ai.defineFlow({
  name: "generateAndSaveWorkoutFlow",
  inputSchema: z.object({
    userId: z.string().describe("The user's unique ID"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    workoutPlan: z.any().nullable(),
    error: z.string().optional(),
  }),
}, async ({userId}) => {
  try {
    // Fetch user profile
    const profileSnap = await getFirestoreInstance()
      .collection("user_profiles")
      .doc(userId)
      .get();

    if (!profileSnap.exists) {
      throw new Error("User profile not found");
    }
    const profile = profileSnap.data();

    // Fetch workout history
    const historySnap = await getFirestoreInstance()
      .collection("userWorkouts")
      .where("userId", "==", userId)
      .get();
    const history = historySnap.docs.map((doc: any) => doc.data());

    // Create a comprehensive prompt for workout generation
    const promptText = `Generate a personalized workout plan based on this
user profile and workout history.

User Profile: ${JSON.stringify(profile)}
Workout History: ${JSON.stringify(history)}

Return ONLY a valid JSON object with this structure:
{
  "name": "Workout Plan Name",
  "description": "Brief description",
  "exercises": [
    {
      "name": "Exercise Name",
      "sets": 3,
      "reps": 12,
      "duration": "30 seconds"
    }
  ],
  "duration": "45 minutes",
  "difficulty": "intermediate"
}

Do not include any markdown formatting or code blocks.
Return only the JSON object.`;

    const {text} = await ai.generate({
      model: "vertexai/gemini-2.0-flash",
      prompt: promptText,
    });

    // Clean and parse the response
    const cleanText = cleanJsonResponse(text);
    const plan = JSON.parse(cleanText);

    // Validate the plan structure
    const validatedPlan = WorkoutPlanSchema.parse(plan);

    // Save the workout plan to Firestore
    const saved = await getFirestoreInstance()
      .collection("workoutPlans")
      .add({
        userId,
        ...validatedPlan,
        createdAt: new Date(),
      });

    return {
      success: true,
      workoutPlan: {id: saved.id, ...validatedPlan},
    };
  } catch (err: any) {
    console.error("Error generating workout plan:", err);
    return {
      success: false,
      error: err.message || "Failed to generate workout plan",
      workoutPlan: null,
    };
  }
});

export const generateAndSaveWorkout = onCallGenkit({
  secrets: [apiKey],
  timeoutSeconds: 540,
  memory: "1GiB",
  maxInstances: 10,
}, generateAndSaveWorkoutFlow);