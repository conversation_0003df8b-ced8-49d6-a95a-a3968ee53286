import {z} from "genkit";
import {onCallGenkit} from "firebase-functions/https";
import {ai, apiKey} from "../shared/genkit";
import {NextWorkoutSchema} from "../shared/schemas";
import {exerciseList} from "../shared/exercises";
import {getFirestoreInstance} from "../shared/firebase";
import {cleanJsonResponse} from "../shared/utils";

// Define the Next Workout Creator Agent flow
const nextWorkoutCreatorFlow = ai.defineFlow({
  name: "nextWorkoutCreatorFlow",
  inputSchema: z.object({
    userId: z.string().describe("The user's unique ID"),
    fitness_guide: z.string().optional().describe("Researched fitness guide based on user preferences"),
    just_finished_workout_ai_summary: z.string().optional().describe("AI summary of just finished workout"),
    previous_workout_summaries_and_dates: z.string().optional().describe("Previous workout summaries with dates"),
    user_preferences: z.string().optional().describe("User preferences and goals"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    nextWorkout: z.any().nullable(),
    error: z.string().optional(),
  }),
}, async ({userId, fitness_guide, just_finished_workout_ai_summary, previous_workout_summaries_and_dates, user_preferences}) => {
  try {
    // Fetch user profile if not provided
    let profile = {};
    let history = [];
    
    if (!user_preferences) {
      const profileSnap = await getFirestoreInstance()
        .collection("user_profiles")
        .doc(userId)
        .get();
      
      if (profileSnap.exists) {
        profile = profileSnap.data();
      }
    }

    if (!previous_workout_summaries_and_dates) {
      const historySnap = await getFirestoreInstance()
        .collection("userWorkouts")
        .where("userId", "==", userId)
        .orderBy("createdAt", "desc")
        .limit(10)
        .get();
      history = historySnap.docs.map((doc: any) => ({
        ...doc.data(),
        date: doc.data().createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
      }));
    }

    // Create the comprehensive prompt from the original n8n agent
    const promptText = `I am providing you with several pieces of information to help you generate the next personalized workout plan. Please use all of the following inputs:

###
Researched Fitness Guide that is made based on user preferences and you should pay detailed attention to as a reference for determining the next workout:
${fitness_guide || JSON.stringify(profile)}
###
Just-Finished Workout AI Summary:
${just_finished_workout_ai_summary || "No recent workout data available"}
###
Previous Workout Summaries (with Dates, pay attention to dates for context of what user has done before the just finished workout and what makes sense the next workout to be based on these):
${previous_workout_summaries_and_dates || JSON.stringify(history)}
###
User Preferences and Goals:
${user_preferences || JSON.stringify(profile)}

###
Based on these inputs, please generate the next workout plan in JSON format using the structure given to you in system prompt:

The JSON should have a top-level property "next_workout" which details:

workout_name (e.g., "Full Body Strength Progression"),

an exercises array with objects for each exercise. Each exercise object should include:

name (e.g., "Bench Press"),

sets (an integer),

reps (an array of integers, one per set),

weight (an array of numbers, one per set),

rest_interval (in seconds),

order_index (an integer for ordering exercises in the workout session).

The JSON should also include a "workout_rationale" property. This should be a narrative explanation detailing why you selected each exercise and the rationale behind the specific rep/weight/rest recommendations. Explain how this plan addresses the user's goals, preferences, and previous performance trends as outlined in the provided inputs.

You must only use exercise names from this exact list: ${exerciseList.join(", ")}

CRITICAL: Return ONLY a valid JSON object with no additional text, explanations, or formatting. 
Do not include markdown code blocks, backticks, or any other text before or after the JSON.
The response must start with { and end with }.`;

    const systemMessage = `You are a highly advanced, personalized fitness coach AI. Your objective is to generate the next workout plan for a user using the following inputs:
- Detailed summaries of the user's recent workouts (1–3 sessions), which include performance metrics, planned vs. actual performance, and feedback.
- The user's preferences and training goals (e.g., strength maximization with a focus on reaching failure, progressive overload, balanced recovery, etc.).
- A research-based training guide that outlines best practices for exercise progression, recovery optimization, and safety in high-intensity training.

Your Task:

1. Analyze the Inputs:
   - Evaluate the recent workout summaries to identify performance trends, areas of fatigue, and exercises that need adjustment. The name of the exercise you provide must exactly match the name of the exercise on the list below.
   - Consider the user's training goals and preferences. For example, if the user aims to reach muscular failure safely, ensure the plan incorporates strategies (e.g., slight weight reductions or rep adjustments) that help achieve this without compromising form.
   - Reference the research-based guide to support your recommendations, ensuring your plan aligns with best practices (such as appropriate rest intervals, progressive overload, and recovery management).

2. Generate the Next Workout:
   - Choose exercises that complement the user's past performance and training goals from the exercise list below.
   - For each exercise, determine:
     - Sets: The number of sets to perform.
     - Reps: A dynamic array that details the planned repetitions for each set. This allows for variations between sets (e.g., a pyramid or reverse pyramid scheme).
     - Weight: A corresponding dynamic array for the weight to be used in each set. Ensure the arrays for reps and weight are the same length as the number of sets.
     - Rest Interval: The recommended rest period (in seconds) between sets, if applicable.
     - Order Index: The order in which the exercises should be executed.

3. Explain Your Recommendations:
   - In a separate section called workout_rationale, provide a detailed narrative explanation covering:
     - Why you selected each exercise.
     - The rationale behind the chosen rep and weight schemes, including exact numbers (e.g., 'reduce Bench Press weight from 135 lbs to 130 lbs for the final set to safely achieve 10 reps').
     - How the recommendations address previous performance issues (e.g., fatigue in the final sets, inability to reach failure, etc.).
     - How the plan aligns with the user's specific goals and the research-based guidelines.

Output Requirements:

Your output must be a JSON object with exactly two top-level properties:
- next_workout – an object that details the workout plan.
- workout_rationale – a text explanation of the decisions made in designing the workout.

The JSON must adhere to the following structure exactly:

{
  "next_workout": {
    "workout_name": "string",         // The name of the next workout (e.g., 'Full Body Strength Progression')
    "exercises": [
      {
        "name": "string",             // Name of the exercise, must match the name from the exercise list below exactly how it is written and given to you.
        "sets": "integer",            // Total number of sets
        "reps": [ "integer", ... ],   // Array of planned reps per set (must match the number of sets)
        "weight": [ "number", ... ],  // Array of planned weights per set (must match the number of sets)
        "rest_interval": "integer",   // Recommended rest interval in seconds (optional but recommended)
        "order_index": "integer"      // The sequence order for the exercise in the workout
      }
    ]
  },
  "workout_rationale": "string"         // A comprehensive explanation detailing the rationale behind the workout plan. It will be shown to user and it should be satisfying for the user to read and be happy with the outcome
}

Additional Guidelines:
- Ensure that every array (for reps and weight) correctly reflects the number of sets.
- Be explicit: if adjustments are made (e.g., reducing weight or changing rep schemes), state the exact numbers and reasoning.
- The rationale should be clear, concise, and actionable, serving as a complete explanation for the user with the goal of maintaining user retention therefore it needs to be personalized for them and their preferences and goals and the deep research guide made for them.
- The output should be fully self-contained so that any downstream system or human reviewer can understand the workout plan and its underlying logic without needing to reference the raw input data.

### These are the list of exercises that you can choose from, make sure you use the names exactly as they are. They are in alphabetical order and therefore you have to determine the best exercises by making sure you take into consideration each exercise that closely resembles what you have decided to be the next workout for the user. In order to optimize your work, first think about what kind of exercises are best for the user and then go over each exercise in the list below to select based on which one in the list resembles closest to your determination of user's needs.

Exercise List: ${exerciseList.join(", ")}`;

    const {text} = await ai.generate({
      model: "vertexai/gemini-2.0-flash",
      prompt: promptText,
      config: {
        temperature: 0.7,
        maxOutputTokens: 2048,
      },
      system: systemMessage,
    });

    // Clean and parse the response
    const cleanText = cleanJsonResponse(text);
    console.log("Raw AI response:", text);
    console.log("Cleaned text:", cleanText);
    
    let workoutPlan;
    try {
      workoutPlan = JSON.parse(cleanText);
    } catch (parseError: any) {
      console.error("JSON parse error:", parseError);
      console.error("Attempted to parse:", cleanText);
      throw new Error(`Invalid JSON response from AI: ${parseError.message}`);
    }

    // Validate the plan structure
    const validatedPlan = NextWorkoutSchema.parse(workoutPlan);

    // Save the workout plan to Firestore
    const saved = await getFirestoreInstance()
      .collection("workoutPlans")
      .add({
        userId,
        ...validatedPlan,
        createdAt: new Date(),
        source: "genkit_agent",
      });

    // Save individual exercises to workout_exercises collection
    const exercises = validatedPlan.next_workout.exercises;
    const exerciseBatch = getFirestoreInstance().batch();
    
    exercises.forEach((exercise) => {
      const exerciseRef = getFirestoreInstance().collection("workout_exercises").doc();
      exerciseBatch.set(exerciseRef, {
        workout_id: saved.id,
        exercise_name: exercise.name,
        sets: exercise.sets,
        reps: exercise.reps,
        weight: exercise.weight,
        rest_interval: exercise.rest_interval,
        order_index: exercise.order_index,
        createdAt: new Date(),
      });
    });
    
    await exerciseBatch.commit();

    return {
      success: true,
      nextWorkout: {
        id: saved.id,
        ...validatedPlan,
      },
    };
  } catch (err: any) {
    console.error("Error generating next workout plan:", err);
    return {
      success: false,
      error: err.message || "Failed to generate next workout plan",
      nextWorkout: null,
    };
  }
});

export const generateNextWorkout = onCallGenkit({
  secrets: [apiKey],
  timeoutSeconds: 540,
  memory: "1GiB",
  maxInstances: 10,
}, nextWorkoutCreatorFlow);