import {initializeApp} from "firebase-admin/app";
import {getFirestore} from "firebase-admin/firestore";
import {enableFirebaseTelemetry} from "@genkit-ai/firebase";

// Firebase app and Firestore instance
let firestore: any;

/**
 * Gets the Firestore instance, initializing it if needed
 * @return {any} - The Firestore instance
 */
export function getFirestoreInstance() {
  if (!firestore) {
    const app = initializeApp({
      projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
    });
    firestore = getFirestore(app);
    // Enable Firebase telemetry
    enableFirebaseTelemetry();
  }
  return firestore;
}