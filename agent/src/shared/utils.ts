/**
 * Cleans JSON response from LLM by removing markdown code blocks
 * @param {string} text - The raw text response from the LLM
 * @return {string} - The cleaned JSON string
 */
export function cleanJsonResponse(text: string): string {
  let cleanText = text.trim();
  if (cleanText.startsWith("```json")) {
    cleanText = cleanText.replace(/^```json\s*/, "")
      .replace(/\s*```$/, "");
  } else if (cleanText.startsWith("```")) {
    cleanText = cleanText.replace(/^```\s*/, "").replace(/\s*```$/, "");
  }
  return cleanText;
}