import {z} from "genkit";

// Enhanced workout plan schema for next workout agent
export const NextWorkoutSchema = z.object({
  next_workout: z.object({
    workout_name: z.string(),
    exercises: z.array(z.object({
      name: z.string(),
      sets: z.number(),
      reps: z.array(z.number()),
      weight: z.array(z.number()),
      rest_interval: z.number(),
      order_index: z.number(),
    })),
  }),
  workout_rationale: z.string(),
});

// Basic workout plan schema for backward compatibility
export const WorkoutPlanSchema = z.object({
  name: z.string(),
  description: z.string(),
  exercises: z.array(z.object({
    name: z.string(),
    sets: z.number().optional(),
    reps: z.number().optional(),
    duration: z.string().optional(),
  })),
  duration: z.string(),
  difficulty: z.string(),
});

// First workout schema for onboarding
export const FirstWorkoutSchema = z.object({
  name: z.array(z.string()),
  sets: z.array(z.number()),
  reps: z.array(z.number()),
  weight: z.array(z.number()),
  order_index: z.array(z.number()),
});

// User input schema for onboarding
export const UserInputSchema = z.object({
  display_name: z.string(),
  gender: z.string(),
  age: z.number(),
  height: z.number(),
  weight: z.number(),
  height_unit: z.string(),
  weight_unit: z.string(),
  primarygoal: z.array(z.string()),
  secondary_goal: z.string().optional(),
  cardio_fitness: z.object({
    level: z.string(),
    description: z.string(),
  }),
  weightlifting_fitness: z.object({
    level: z.string(),
    description: z.string(),
  }),
  equipment: z.array(z.string()).optional(),
  additional_notes: z.string().optional(),
  fitness_additional_info: z.string().optional(),
});