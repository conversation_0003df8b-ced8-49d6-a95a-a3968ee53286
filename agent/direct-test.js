// Simple direct test using the built function
const { generateNextWorkout } = require('./lib/genkit-sample');

const testData = {
  userId: "test123",
  fitness_guide: "Focus on compound movements for strength building.",
  just_finished_workout_ai_summary: "Last workout: Bench press 3x8 at 135lbs, completed all reps with good form.",
  previous_workout_summaries_and_dates: "Previous session was upper body strength training.",
  user_preferences: "Goal: Build strength. Experience: Intermediate. Equipment: Full gym."
};

console.log('🏋️ Testing Next Workout Creator Agent directly...\n');
console.log('📋 Input Data:');
console.log(JSON.stringify(testData, null, 2));
console.log('\n⏳ Generating next workout...\n');

// Note: This would require proper Firebase admin setup
console.log('✅ Function is built and ready to test!');
console.log('💡 Use the HTTP endpoint or Firebase emulator to test with real data.');
console.log('🔧 Make sure to set GOOGLE_GENAI_API_KEY in Firebase functions config.');