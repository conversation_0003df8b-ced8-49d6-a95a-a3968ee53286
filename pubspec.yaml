name: agentic_fit
description: "Your AI-powered fitness companion"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # Firebase
  firebase_core: ^3.13.1
  firebase_auth: ^5.0.0
  cloud_firestore: ^5.0.0
  cloud_functions: ^5.0.0
  firebase_vertexai: ^0.2.2+1

  # Google Sign In (ensure compatibility)
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1

  # UI & Utilities
  cupertino_icons: ^1.0.8
  fl_chart: ^0.68.0
  google_fonts: ^6.1.0
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3
  # State management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  http: ^1.2.1
  uuid: ^4.4.0
  image_picker: ^1.1.1
  video_player: ^2.8.6
  vimeo_video_player: ^1.0.1
  cached_network_image: ^3.3.1
  flutter_animate: ^4.5.0
  intl: ^0.19.0
  # For environment variables
  flutter_dotenv: ^5.1.0
  # Secure storage for API keys
  flutter_secure_storage: ^9.2.2
  # Audio packages for voice chat - temporarily disabled due to build issues
  # record: ^5.1.2
  # flutter_soloud: ^2.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  change_app_package_name: ^1.4.0
  build_runner: ^2.4.9
  riverpod_generator: ^2.4.0
  custom_lint: ^0.6.4
  riverpod_lint: ^2.3.10

flutter:
  uses-material-design: true
  assets:
    - .env
    - assets/images/
    - assets/images/fitness_categories/
    - assets/images/onboarding/
    - assets/images/exercises/
    - assets/icons/
    - assets/videos/

  # Removing local font declarations since we're using Google Fonts
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
