rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Exercises collection - authenticated users can read all
    match /exercises/{exerciseId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid != null;
    }
    
    // User workouts - for custom workout plans and active sessions
    match /userWorkouts/{workoutId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        (request.resource.data.createdBy == request.auth.uid || 
         request.resource.data.userId == request.auth.uid);
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid || 
         resource.data.userId == request.auth.uid);
    }
    
    // Workout history - users can read/write their own workout history
    match /workoutHistory/{historyId} {
      allow read: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
      allow update, delete: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Legacy collections for migration purposes only
    match /comprehensive_onboarding/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /fitness_levels/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /workout_preferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /fitness_goals/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /users_consolidated/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /workoutPlans/{workoutId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        (resource == null || request.auth.uid == resource.data.createdBy);
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.createdBy;
    }
    
    match /userProgress/{progressId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
  }
}
