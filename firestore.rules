rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isOwnerField() {
      return isAuthenticated() && request.auth.uid == resource.data.userId;
    }
    
    function isOwnerFieldRequest() {
      return isAuthenticated() && request.auth.uid == request.resource.data.userId;
    }
    
    function isCreatedBy() {
      return isAuthenticated() && request.auth.uid == resource.data.createdBy;
    }
    
    function isCreatedByRequest() {
      return isAuthenticated() && request.auth.uid == request.resource.data.createdBy;
    }
    
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId);
    }
    
    // Exercises collection - all authenticated users can read
    match /exercises/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Admin only via Firebase Admin SDK
    }
    
    // Workout history - users can manage their own data
    match /workoutHistory/{historyId} {
      allow read: if isOwnerField();
      allow update, delete: if isOwnerField();
      allow create: if isOwnerFieldRequest();
    }
    
    // User workouts (custom workouts) - users can manage their own data
    match /userWorkouts/{workoutId} {
      allow read: if isCreatedBy() || isOwnerField();
      allow update, delete: if isCreatedBy() || isOwnerField();
      allow create: if isCreatedByRequest() || isOwnerFieldRequest();
    }
    
    // User fitness guides - users can manage their own data
    match /userFitnessGuides/{guideId} {
      allow read: if isOwnerField();
      allow update, delete: if isOwnerField();
      allow create: if isOwnerFieldRequest();
    }
    
    // Conversations - users can manage their own conversations
    match /conversations/{conversationId} {
      allow read: if isOwnerField();
      allow update, delete: if isOwnerField();
      allow create: if isOwnerFieldRequest();
      
      // Conversation messages
      match /messages/{messageId} {
        allow read, write: if isAuthenticated() && 
          get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId == request.auth.uid;
      }
    }
    
    // Fitness knowledge (RAG) - all authenticated users can read
    match /fitnessKnowledge/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Admin only via Firebase Admin SDK
    }
    
    // User preferences - users can manage their own data
    match /userPreferences/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // User stats - users can manage their own data
    match /userStats/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Workout plans - all authenticated users can read
    match /workoutPlans/{planId} {
      allow read: if isAuthenticated();
      allow write: if false; // Admin only via Firebase Admin SDK
    }
  }
}