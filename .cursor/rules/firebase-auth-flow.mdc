---
description:
globs:
alwaysApply: false
---
# Firebase Authentication & Onboarding Flow

## Authentication Wrapper Pattern
The main authentication logic is in [lib/main.dart](mdc:lib/main.dart) using the `AuthWrapper` pattern:

- Uses `StreamBuilder<User?>` with `FirebaseAuth.instance.authStateChanges()`
- Implements cached authentication check before Firebase stream
- Has robust error handling for onboarding status checks
- Prioritizes user document preferences over collection-specific checks

## Onboarding Status Logic
When checking if user needs onboarding:
1. First check `userData.preferences['comprehensiveOnboardingComplete']` from users collection
2. Fallback to `ComprehensiveOnboardingService.hasCompletedComprehensiveOnboarding()`
3. Default to NOT showing onboarding on errors (better UX)

## Key Services
- [lib/services/auth_service.dart](mdc:lib/services/auth_service.dart) - Core authentication
- [lib/services/comprehensive_onboarding_service.dart](mdc:lib/services/comprehensive_onboarding_service.dart) - Onboarding data
- [lib/services/consolidated_user_service.dart](mdc:lib/services/consolidated_user_service.dart) - Future consolidated approach

## Firebase Security Rules
[firestore.rules](mdc:firestore.rules) must include permissions for all collections:
- `users/{userId}` - Main user documents
- `comprehensive_onboarding/{userId}` - Onboarding data
- `user_profiles/{userId}` - Profile data
- `fitness_levels/{userId}` - Fitness data
- `workout_preferences/{userId}` - Preference data

## Common Issues
- Permission denied errors indicate missing Firestore rules
- Always deploy rules after changes: `firebase deploy --only firestore:rules`
- Use `userData.preferences` as primary source of truth for onboarding status
- Implement graceful fallbacks for network/permission errors
