# Flutter Integration Guide for Workout Agent Firebase Functions

## Overview

This guide provides comprehensive instructions for integrating the deployed Workout Agent Firebase Functions into your Flutter application. All functions are deployed as Firebase Callable Functions with built-in authentication and error handling.

## Prerequisites

1. **Firebase SDK for Flutter** installed in your project:
   ```yaml
   # pubspec.yaml
   dependencies:
     firebase_core: ^2.24.0
     firebase_auth: ^4.15.0
     cloud_functions: ^4.5.0
   ```

2. **Firebase initialized** in your Flutter app
3. **User authenticated** via Firebase Auth (required for all functions)

## Available Functions

### 1. **recommendNextExercise**
Provides personalized exercise recommendations based on user's current workout and fitness profile.

### 2. **generateFitnessGuide**
Creates a comprehensive fitness guide during user onboarding.

### 3. **createFirstWorkout**
Generates the user's first workout plan based on their fitness guide.

### 4. **completeOnboarding**
Executes the complete onboarding flow (generates guide + first workout).

### 5. **analyzeLastWorkout**
Analyzes the user's most recent workout performance.

### 6. **generateNextWorkout**
Creates a progressive workout plan based on previous performance.

### 7. **fitnessChat**
AI-powered fitness coach chat for answering questions and providing guidance.

## Implementation Examples

### Basic Setup

```dart
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';

class WorkoutAgentService {
  final FirebaseFunctions _functions = FirebaseFunctions.instance;
  
  // Optional: For development/testing with emulator
  void useEmulator() {
    _functions.useFunctionsEmulator('localhost', 5001);
  }
}
```

### 1. Complete Onboarding

```dart
Future<Map<String, dynamic>> completeUserOnboarding() async {
  try {
    // Ensure user is authenticated
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User must be authenticated');
    }
    
    // Call the function
    final HttpsCallable callable = _functions.httpsCallable('completeOnboarding');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
    });
    
    // Handle the response
    final data = result.data as Map<String, dynamic>;
    
    if (data['success'] == true) {
      print('Onboarding completed successfully');
      print('Guide ID: ${data['guideId']}');
      print('Workout ID: ${data['workoutId']}');
      
      return data;
    } else {
      throw Exception(data['message'] ?? 'Onboarding failed');
    }
  } on FirebaseFunctionsException catch (e) {
    print('Firebase Functions error: ${e.code} - ${e.message}');
    rethrow;
  } catch (e) {
    print('Unexpected error: $e');
    rethrow;
  }
}
```

### 2. Generate Fitness Guide Only

```dart
Future<String> generateFitnessGuide() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User must be authenticated');
    
    final HttpsCallable callable = _functions.httpsCallable('generateFitnessGuide');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
    });
    
    final data = result.data as Map<String, dynamic>;
    
    if (data['success'] == true) {
      return data['guideId']; // Returns the guide document ID
    } else {
      throw Exception(data['message'] ?? 'Failed to generate guide');
    }
  } catch (e) {
    print('Error generating fitness guide: $e');
    rethrow;
  }
}
```

### 3. Create First Workout

```dart
Future<String> createFirstWorkout({String? guideId}) async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User must be authenticated');
    
    final HttpsCallable callable = _functions.httpsCallable('createFirstWorkout');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
      'guideId': guideId, // Optional - will use latest guide if not provided
    });
    
    final data = result.data as Map<String, dynamic>;
    
    if (data['success'] == true) {
      return data['workoutId']; // Returns the workout document ID
    } else {
      throw Exception(data['message'] ?? 'Failed to create workout');
    }
  } catch (e) {
    print('Error creating first workout: $e');
    rethrow;
  }
}
```

### 4. Recommend Next Exercise

```dart
Future<ExerciseRecommendation> getNextExercise({bool saveWorkout = false}) async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User must be authenticated');
    
    final HttpsCallable callable = _functions.httpsCallable('recommendNextExercise');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
      'saveWorkout': saveWorkout, // Set to true to save as a workout template
    });
    
    final data = result.data as Map<String, dynamic>;
    
    return ExerciseRecommendation(
      exercise: Exercise.fromJson(data['recommendation']),
      reason: data['reason'],
      alternatives: (data['alternatives'] as List<dynamic>)
          .map((e) => Exercise.fromJson(e))
          .toList(),
      musclesWorked: List<String>.from(data['targetMuscles']),
    );
  } catch (e) {
    print('Error getting exercise recommendation: $e');
    rethrow;
  }
}
```

### 5. Analyze Last Workout

```dart
Future<WorkoutAnalysis> analyzeLastWorkout({String? workoutId}) async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User must be authenticated');
    
    final HttpsCallable callable = _functions.httpsCallable('analyzeLastWorkout');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
      'workoutId': workoutId, // Optional - analyzes most recent if not provided
    });
    
    final data = result.data as Map<String, dynamic>;
    
    return WorkoutAnalysis(
      overallPerformance: data['overallPerformance'],
      strengths: List<String>.from(data['strengths']),
      areasForImprovement: List<String>.from(data['areasForImprovement']),
      recoveryStatus: data['recoveryStatus'],
      nextWorkoutRecommendation: data['nextWorkoutRecommendation'],
    );
  } catch (e) {
    print('Error analyzing workout: $e');
    rethrow;
  }
}
```

### 6. Generate Next Workout

```dart
Future<String> generateNextWorkout({
  bool skipRecoveryCheck = false,
  List<String>? targetMuscles,
  String? workoutType,
}) async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User must be authenticated');
    
    final HttpsCallable callable = _functions.httpsCallable('generateNextWorkout');
    final HttpsCallableResult result = await callable.call({
      'userId': user.uid,
      'skipRecoveryCheck': skipRecoveryCheck,
      'targetMuscles': targetMuscles,
      'workoutType': workoutType, // 'strength', 'cardio', 'mixed'
    });
    
    final data = result.data as Map<String, dynamic>;
    
    if (data['success'] == true) {
      return data['workoutId']; // Returns the new workout document ID
    } else {
      throw Exception(data['message'] ?? 'Failed to generate workout');
    }
  } catch (e) {
    print('Error generating next workout: $e');
    rethrow;
  }
}
```

### 7. Fitness Chat

```dart
class FitnessChat {
  final FirebaseFunctions _functions = FirebaseFunctions.instance;
  String? _conversationId;
  
  Future<ChatResponse> sendMessage(String message) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User must be authenticated');
      
      final HttpsCallable callable = _functions.httpsCallable('fitnessChat');
      final HttpsCallableResult result = await callable.call({
        'userId': user.uid,
        'message': message,
        'conversationId': _conversationId,
      });
      
      final data = result.data as Map<String, dynamic>;
      
      // Update conversation ID for continuity
      _conversationId = data['conversationId'];
      
      return ChatResponse(
        response: data['response'],
        conversationId: data['conversationId'],
        suggestions: data['suggestions'] != null 
            ? List<String>.from(data['suggestions'])
            : null,
      );
    } catch (e) {
      print('Error in fitness chat: $e');
      rethrow;
    }
  }
  
  void startNewConversation() {
    _conversationId = null;
  }
}
```

## Data Models

```dart
// Example data models for type safety

class Exercise {
  final String id;
  final String name;
  final String description;
  final String primaryMuscleGroup;
  final List<String> secondaryMuscleGroups;
  final String difficulty;
  final int? reps;
  final int? sets;
  final int? duration;
  
  Exercise.fromJson(Map<String, dynamic> json);
}

class ExerciseRecommendation {
  final Exercise exercise;
  final String reason;
  final List<Exercise> alternatives;
  final List<String> musclesWorked;
  
  ExerciseRecommendation({
    required this.exercise,
    required this.reason,
    required this.alternatives,
    required this.musclesWorked,
  });
}

class WorkoutAnalysis {
  final String overallPerformance;
  final List<String> strengths;
  final List<String> areasForImprovement;
  final String recoveryStatus;
  final String nextWorkoutRecommendation;
  
  WorkoutAnalysis({
    required this.overallPerformance,
    required this.strengths,
    required this.areasForImprovement,
    required this.recoveryStatus,
    required this.nextWorkoutRecommendation,
  });
}

class ChatResponse {
  final String response;
  final String conversationId;
  final List<String>? suggestions;
  
  ChatResponse({
    required this.response,
    required this.conversationId,
    this.suggestions,
  });
}
```

## Error Handling

All functions return consistent error responses. Always wrap calls in try-catch blocks:

```dart
try {
  // Function call
} on FirebaseFunctionsException catch (e) {
  // Handle Firebase-specific errors
  switch (e.code) {
    case 'unauthenticated':
      print('User needs to be authenticated');
      break;
    case 'permission-denied':
      print('User lacks permission');
      break;
    case 'invalid-argument':
      print('Invalid data provided: ${e.message}');
      break;
    default:
      print('Firebase error: ${e.code} - ${e.message}');
  }
} catch (e) {
  // Handle other errors
  print('Unexpected error: $e');
}
```

## Best Practices

1. **Always authenticate users** before calling functions
2. **Handle errors gracefully** with appropriate user feedback
3. **Cache results** when appropriate (e.g., fitness guides don't change often)
4. **Use loading indicators** as AI responses may take a few seconds
5. **Implement retry logic** for network failures
6. **Validate inputs** before sending to functions

## Testing

For local testing with Firebase Emulator:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Use emulator in debug mode
  if (kDebugMode) {
    FirebaseFunctions.instance.useFunctionsEmulator('localhost', 5001);
  }
  
  runApp(MyApp());
}
```

## Function URLs

Production function endpoints (for reference only - use Firebase SDK):
- Base URL: `https://us-central1-po2vf2ae7tal9invaj7jkf4a06hsac.cloudfunctions.net/`
- Functions are callable functions, not HTTP endpoints

## Support

For issues or questions about the Firebase Functions:
1. Check function logs in Firebase Console
2. Ensure user is properly authenticated
3. Verify function parameters match the expected schema
4. Check network connectivity and Firebase project configuration

---

Last Updated: ${new Date().toISOString()}
Generated for: Workout Agent Firebase Functions v1.0