---
model: googleai/gemini-1.5-flash
config:
  temperature: 0.3
  maxOutputTokens: 2048
input:
  schema:
    type: object
    properties:
      userProfile:
        type: object
        properties:
          fitness:
            type: object
            properties:
              strengthLevel:
                type: number
              cardioLevel:
                type: number
              goals:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                    priority:
                      type: number
              exercisesToAvoid:
                type: array
                items:
                  type: string
          preferences:
            type: object
            properties:
              durationMinutes:
                type: number
              environments:
                type: array
                items:
                  type: string
      availableExercises:
        type: array
        items:
          type: object
          properties:
            id:
              type: string
            name:
              type: string
            primaryMuscleGroup:
              type: string
            difficulty:
              type: string
            equipment:
              type: array
              items:
                type: string
output:
  format: json
  schema:
    type: object
    properties:
      name:
        type: string
      description:
        type: string
      exercises:
        type: array
        items:
          type: object
          properties:
            name:
              type: string
            sets:
              type: number
            reps:
              type: string
            restSeconds:
              type: number
            notes:
              type: string
      targetMuscles:
        type: array
        items:
          type: string
---

Create the FIRST workout for a new user:

## User Profile:
- Fitness Level: Strength {{userProfile.fitness.strengthLevel}}, Cardio {{userProfile.fitness.cardioLevel}}
- Goals: {{userProfile.fitness.goals}}
- Duration: {{userProfile.preferences.durationMinutes}} minutes
- Environment: {{userProfile.preferences.environments}}
- Exercises to Avoid: {{userProfile.fitness.exercisesToAvoid}}

## Available Exercises:
{{availableExercises}}

Create a balanced, conservative first workout that:
1. Is appropriate for their first session (don't overdo it)
2. Includes 4-6 exercises maximum
3. Focuses on compound movements and proper form
4. Leaves them feeling accomplished, not exhausted
5. Sets the tone for their fitness journey

### Training Guidelines Based on Fitness Level ({{userProfile.fitness.strengthLevel}}):
- Adjust sets, reps, and rest based on fitness level
- Beginners (0-0.33): 2-3 sets, 12-15 reps, 60-90s rest
- Intermediate (0.34-0.66): 3 sets, 8-12 reps, 45-75s rest
- Advanced (0.67+): 3-4 sets, 6-10 reps, 30-60s rest
- Focus on proper form regardless of level

Remember:
- This is their FIRST workout, so be conservative with volume
- Ensure exercise names match EXACTLY from the available exercises list
- Consider their specific goals: {{userProfile.fitness.goals}}
- Create a positive first experience that builds confidence