# Quick Reference - Workout Agent Functions

## Function Signatures

```dart
// 1. Complete Onboarding (Guide + First Workout)
completeOnboarding({
  required String userId
}) → { success, guideId, workoutId, message }

// 2. Generate Fitness Guide
generateFitnessGuide({
  required String userId
}) → { success, guideId, guide, message }

// 3. Create First Workout
createFirstWorkout({
  required String userId,
  String? guideId  // Optional
}) → { success, workoutId, workout, message }

// 4. Recommend Next Exercise
recommendNextExercise({
  required String userId,
  bool saveWorkout = false
}) → { recommendation, reason, alternatives, targetMuscles }

// 5. Analyze Last Workout
analyzeLastWorkout({
  required String userId,
  String? workoutId  // Optional
}) → { overallPerformance, strengths, areasForImprovement, recoveryStatus, nextWorkoutRecommendation }

// 6. Generate Next Workout
generateNextWorkout({
  required String userId,
  bool skipRecoveryCheck = false,
  List<String>? targetMuscles,
  String? workoutType  // 'strength', 'cardio', 'mixed'
}) → { success, workoutId, workout, message, skippedRecovery }

// 7. Fitness Chat
fitnessChat({
  required String userId,
  required String message,
  String? conversationId  // For continuing conversations
}) → { response, conversationId, suggestions?, relatedExercises? }
```

## Typical User Flow

```
1. New User Registration
   ↓
2. completeOnboarding() → Creates fitness guide + first workout
   ↓
3. User works out for a few days
   ↓
4. analyzeLastWorkout() → Get performance insights
   ↓
5. generateNextWorkout() → Get progressive workout plan
   ↓
6. recommendNextExercise() → Get real-time exercise suggestions
   ↓
7. fitnessChat() → Ask questions anytime
```

## Quick Implementation

```dart
// Initialize
final functions = FirebaseFunctions.instance;

// Call any function
final callable = functions.httpsCallable('functionName');
final result = await callable.call({'userId': userId, ...params});
final data = result.data as Map<String, dynamic>;
```

## Error Codes
- `unauthenticated` - User not logged in
- `permission-denied` - User lacks permissions
- `invalid-argument` - Bad input data
- `not-found` - Resource doesn't exist
- `internal` - Server error