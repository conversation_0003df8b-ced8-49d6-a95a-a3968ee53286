# Firebase Security Rules for Workout Agent

## Firestore Security Rules

Copy and paste these rules into your Firebase Console under Firestore Database > Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Helper function to check if user owns the resource being accessed
    function ownsResource() {
      return isAuthenticated() && request.auth.uid == resource.data.userId;
    }
    
    // Users collection - users can only read/write their own profile
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Exercises collection - all authenticated users can read, admins can write
    match /exercises/{exerciseId} {
      allow read: if isAuthenticated();
      // Note: Add admin check here if you have admin users
      // allow write: if isAuthenticated() && request.auth.token.admin == true;
    }
    
    // User workouts - users can only access their own workouts
    match /userWorkouts/{workoutId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid) &&
        (!("userId" in request.resource.data) || request.resource.data.userId == request.auth.uid);
    }
    
    // User fitness guides - users can only access their own guides
    match /userFitnessGuides/{guideId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid) &&
        (!("userId" in request.resource.data) || request.resource.data.userId == request.auth.uid);
    }
    
    // Workout history - users can only access their own history
    match /workoutHistory/{historyId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid) &&
        (!("userId" in request.resource.data) || request.resource.data.userId == request.auth.uid);
    }
    
    // Conversations - users can only access their own conversations
    match /conversations/{conversationId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid) &&
        (!("userId" in request.resource.data) || request.resource.data.userId == request.auth.uid);
      
      // Messages subcollection within conversations
      match /messages/{messageId} {
        allow read, write: if isAuthenticated() && 
          get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId == request.auth.uid;
      }
    }
    
    // Fitness knowledge base - all authenticated users can read (for RAG)
    match /fitnessKnowledge/{documentId} {
      allow read: if isAuthenticated();
      // Note: Add admin check for write access
      // allow write: if isAuthenticated() && request.auth.token.admin == true;
    }
    
    // User preferences - users can only access their own preferences
    match /userPreferences/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Workout templates - all authenticated users can read
    match /workoutTemplates/{templateId} {
      allow read: if isAuthenticated();
      // Note: Add admin or trainer check for write access
      // allow write: if isAuthenticated() && request.auth.token.trainer == true;
    }
    
    // User progress tracking - users can only access their own progress
    match /userProgress/{userId} {
      allow read, write: if isOwner(userId);
      
      // Progress entries subcollection
      match /entries/{entryId} {
        allow read, write: if isOwner(userId);
      }
    }
    
    // Nutrition plans - users can only access their own plans
    match /nutritionPlans/{planId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid) &&
        (!("userId" in request.resource.data) || request.resource.data.userId == request.auth.uid);
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

## Important Notes

1. **Authentication Required**: All rules require users to be authenticated. Make sure your Flutter app implements proper authentication flow.

2. **User Data Isolation**: Each user can only access their own data. The rules check that the `userId` field matches the authenticated user's ID.

3. **Shared Resources**: Collections like `exercises`, `fitnessKnowledge`, and `workoutTemplates` are readable by all authenticated users but require admin privileges to modify.

4. **Subcollections**: For nested data like conversation messages, the rules verify ownership at the parent level.

5. **Default Deny**: The catch-all rule at the end denies access to any documents not explicitly covered by other rules.

## Testing Your Rules

1. Use the Firebase Console Rules Playground to test different scenarios
2. Test with authenticated and unauthenticated users
3. Verify users cannot access other users' data
4. Ensure shared resources are accessible to all authenticated users

## Security Best Practices

1. Always validate the `userId` field in both read and write operations
2. Use the `resource` object to check existing data
3. Use `request.resource` to validate incoming data
4. Consider adding rate limiting rules for write operations
5. Regularly audit and update your security rules

## Adding Admin Access

To add admin functionality, you'll need to set custom claims on admin users:

```javascript
// In your admin SDK (server-side)
await admin.auth().setCustomUserClaims(uid, { admin: true });
```

Then update the rules to check for admin claims:

```javascript
function isAdmin() {
  return isAuthenticated() && request.auth.token.admin == true;
}
```