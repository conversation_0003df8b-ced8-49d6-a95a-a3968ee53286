# Flutter App Fix Guide

## Overview
This guide helps you fix the Firestore permission errors and type conversion issues in your Flutter app.

## Issues Identified

1. **Type Conversion Error**: The app crashes when parsing user data from Firestore due to type mismatches (likely int vs double)
2. **Permission Errors**: Secondary issue caused by the app failing to properly save user data

## Fix Implementation

### Step 1: Copy Fixed Files

Copy these files to your Flutter project:
1. `consolidated_user_model_fixed.dart` → `lib/models/`
2. `consolidated_user_service_fixed.dart` → `lib/services/`

### Step 2: Update Imports

In your Flutter app, update the imports to use the fixed versions:

```dart
// Before
import 'models/consolidated_user_model.dart';
import 'services/consolidated_user_service.dart';

// After
import 'models/consolidated_user_model_fixed.dart';
import 'services/consolidated_user_service_fixed.dart';
```

### Step 3: Update Service Usage

Replace service instantiation:

```dart
// Before
final userService = ConsolidatedUserService();

// After
final userService = ConsolidatedUserServiceFixed();
```

### Step 4: Update Firestore Rules

In Firebase Console → Firestore → Rules, update to:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Exercises - read for all authenticated users
    match /exercises/{document=**} {
      allow read: if request.auth != null;
      allow write: if false;
    }
    
    // User's workout history
    match /workoutHistory/{historyId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // User's workouts
    match /userWorkouts/{workoutId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // User's fitness guides
    match /userFitnessGuides/{guideId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Conversations
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Conversation messages
    match /conversations/{conversationId}/messages/{messageId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId;
    }
    
    // Fitness knowledge (RAG)
    match /fitnessKnowledge/{document=**} {
      allow read: if request.auth != null;
      allow write: if false;
    }
  }
}
```

### Step 5: Test the Fix

Run this test code to verify the fix:

```dart
// Add to a test screen or debug button
Future<void> testUserDataParsing() async {
  try {
    final userService = ConsolidatedUserServiceFixed();
    final user = await userService.getCurrentUser();
    
    if (user != null) {
      print('✅ User data parsed successfully');
      print('User ID: ${user.uid}');
      print('Email: ${user.email}');
      print('Has completed onboarding: ${user.hasCompletedOnboarding}');
    } else {
      print('❌ No user found');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
}
```

## Key Improvements in Fixed Version

1. **Type Converters**:
   - Safe int/double conversion
   - Handles null values
   - Supports string-to-number conversion
   - DateTime/Timestamp handling

2. **Better Error Handling**:
   - Doesn't crash on type mismatches
   - Provides default values
   - Logs specific parsing errors

3. **Firestore Integration**:
   - Direct `fromFirestore` method
   - Automatic timestamp handling
   - Field name variations support

4. **Caching**:
   - Reduces Firestore reads
   - Improves performance
   - Offline support

## Common Issues and Solutions

### Issue: "The getter 'millisecondsSinceEpoch' was called on null"
**Fix**: The fixed model handles null DateTime values properly

### Issue: "type 'int' is not a subtype of type 'double'"
**Fix**: TypeConverters automatically handle numeric conversions

### Issue: "type '_Map<String, dynamic>' is not a subtype of type 'List<dynamic>'"
**Fix**: Safe list parsing with proper type checking

## Verification Steps

1. Clean app data: `flutter clean`
2. Get packages: `flutter pub get`
3. Run app: `flutter run`
4. Check logs for any parsing errors
5. Verify user data loads correctly

## Additional Debugging

If issues persist, add this to your main.dart:

```dart
// Enable detailed logging
FirebaseFirestore.instance.settings = const Settings(
  persistenceEnabled: true,
  cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
);

// Add error tracking
FlutterError.onError = (FlutterErrorDetails details) {
  print('Flutter Error: ${details.exception}');
  print('Stack trace: ${details.stack}');
};
```

## Success Indicators

- ✅ No more type conversion errors in logs
- ✅ User data loads successfully
- ✅ Fitness chat connects properly
- ✅ No Firestore permission errors

---

After implementing these fixes, your app should work correctly with the deployed Firebase Functions!