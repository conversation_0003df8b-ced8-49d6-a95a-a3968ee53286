# RAG Quick Start Guide

## Overview

The Workout Agent now includes RAG (Retrieval-Augmented Generation) capabilities to provide more accurate and contextual fitness information. This enhances the AI responses with real fitness knowledge stored in Firestore.

## Components

### 1. **Retrievers** (Available in Genkit UI)
- `fitnessKnowledgeRetriever` - General fitness knowledge
- `exerciseRetriever` - Exercise-specific information
- `nutritionRetriever` - Nutrition guidelines
- `techniqueRetriever` - Exercise technique details

### 2. **Indexers** (Available in Genkit UI)
- `exerciseIndexer` - Index exercise documents
- `nutritionIndexer` - Index nutrition content
- `researchIndexer` - Index fitness research
- `techniqueIndexer` - Index technique guides

### 3. **Flows**
- `indexSampleFitnessData` - Indexes pre-built sample data
- `testRagRetrieval` - Tests retrieval functionality
- `fitnessChat` - Now enhanced with RAG context

## Quick Start Steps

### 1. Start Genkit Developer UI
```bash
npm run genkit
```

### 2. Index Sample Data
In the Genkit UI (http://localhost:4000):
1. Navigate to "Flows"
2. Find `indexSampleFitnessData`
3. Click "Run" with empty input `{}`
4. Wait for completion (indexes ~15 documents)

### 3. Test Retrieval
1. Find `testRagRetrieval` flow
2. Run with input:
   ```json
   {
     "query": "how to do a proper squat"
   }
   ```
3. Verify relevant documents are returned

### 4. Use Enhanced Chat
The `fitnessChat` flow now automatically:
- Retrieves relevant context for user queries
- Includes accurate fitness information in responses
- Returns related exercises when applicable

## Sample Knowledge Includes

- **Exercise Techniques**: Squat, Deadlift, Bench Press
- **Nutrition**: Protein intake, Pre-workout nutrition, Hydration
- **Recovery**: Sleep, Active recovery, Stretching
- **Common Q&A**: Sets/reps, Progressive overload, Form vs weight

## Adding Custom Knowledge

### Option 1: Use Indexers in Genkit UI
1. Navigate to "Indexers"
2. Choose appropriate indexer (e.g., `exerciseIndexer`)
3. Run with your document:
   ```json
   {
     "documents": [{
       "id": "custom-exercise-1",
       "title": "Pull-ups Guide",
       "content": "Pull-ups are a compound exercise...",
       "category": "exercise",
       "tags": ["back", "biceps", "compound"]
     }]
   }
   ```

### Option 2: Programmatically
```typescript
import { exerciseIndexer } from './src/rag/indexers';

await exerciseIndexer({
  documents: [{
    id: 'my-exercise',
    title: 'Exercise Name',
    content: 'Detailed description...',
    category: 'exercise',
    tags: ['muscle-group']
  }]
});
```

## Monitoring RAG Performance

1. Check retrieval quality in `testRagRetrieval`
2. Monitor chat responses for accuracy
3. View Firestore `fitnessKnowledge` collection
4. Check Genkit traces for retrieval timing

## Benefits

- ✅ More accurate exercise descriptions
- ✅ Evidence-based nutrition advice
- ✅ Proper form instructions
- ✅ Contextual responses
- ✅ Reduced AI hallucinations

## Troubleshooting

- **No results returned**: Check if data is indexed
- **Irrelevant results**: Adjust retriever's `distanceThreshold`
- **Slow retrieval**: Check Firestore vector index status
- **Missing embeddings**: Ensure embedder API key is configured

---

The RAG system significantly improves the quality and accuracy of fitness guidance provided by the AI coach!