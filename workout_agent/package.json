{"main": "lib/src/index.js", "scripts": {"genkit": "genkit start -o -- tsx --watch src/genkit-runner.ts", "genkit:agent": "genkit start -o -- tsx --watch src/workout-recommendation-agent.ts", "genkit:onboarding": "genkit start -o -- tsx --watch src/flows/onboarding-flows-v2.ts", "genkit:next-workout": "genkit start -o -- tsx --watch src/flows/next-workout-flows-v2.ts", "lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "name": "functions", "engines": {"node": "22"}, "dependencies": {"@genkit-ai/dotprompt": "^0.9.12", "@genkit-ai/firebase": "^1.10.0", "@genkit-ai/googleai": "^1.10.0", "dotenv": "^16.5.0", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "genkit": "^1.10.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "tsx": "^4.19.4", "typescript": "^4.9.5"}, "private": true}