/**
 * Shared Genkit Configuration
 * Single instance to be used across all flows with Firebase integration
 */

import {genkit} from "genkit";
import {googleAI} from "@genkit-ai/googleai";
import {enableFirebaseTelemetry} from "@genkit-ai/firebase";
import * as admin from "firebase-admin";

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  // In production (Firebase Functions), initialize without credentials
  // Firebase will automatically use Application Default Credentials
  if (process.env.FUNCTIONS_EMULATOR_HOST) {
    // For local development with emulator
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const serviceAccount = require("../po2vf2ae7tal9invaj7jkf4a06hsac-firebase-adminsdk-fbsvc-4c3d1779a6.json");
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
      projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
    });
  } else {
    // For production Firebase Functions
    admin.initializeApp();
  }
}

// Enable Firebase telemetry for monitoring and debugging
enableFirebaseTelemetry({
  projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
});

// Initialize Genkit with Google AI plugin
export const ai = genkit({
  plugins: [
    googleAI({
      apiKey: "AIzaSyDG5SmBVzfEkNabJ6dy-shXGs1eXERmBoc",
    }),
  ],
});

// Export Firestore instance
export const db = admin.firestore();
