/**
 * Fitness Knowledge Base with RAG
 * Firestore-based retriever for fitness knowledge documents
 */

import {z} from "genkit";
import {ai, db} from "../genkit-config";
import {defineFirestoreRetriever} from "@genkit-ai/firebase";
import {textEmbedding004} from "@genkit-ai/googleai";
import {Document} from "@genkit-ai/ai/retriever";

// Define schema for fitness knowledge documents
export const FitnessDocumentSchema = z.object({
  id: z.string(),
  category: z.enum(["exercise", "nutrition", "recovery", "technique", "general"]),
  title: z.string(),
  content: z.string(),
  tags: z.array(z.string()),
  muscleGroups: z.array(z.string()).optional(),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  metadata: z.record(z.any()).optional(),
});

export type FitnessDocument = z.infer<typeof FitnessDocumentSchema>;

// Define collections
export const COLLECTIONS = {
  exercises: "fitness-knowledge/exercises/documents",
  nutrition: "fitness-knowledge/nutrition/documents",
  recovery: "fitness-knowledge/recovery/documents",
  techniques: "fitness-knowledge/techniques/documents",
  general: "fitness-knowledge/general/documents",
} as const;

// Create Firestore retriever for fitness knowledge
export const fitnessKnowledgeRetriever = defineFirestoreRetriever(ai, {
  name: "fitnessKnowledgeRetriever",
  firestore: db,
  collection: "fitness-knowledge-vectors",
  contentField: "content",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
});

// Create specialized retrievers for each category
export const exerciseRetriever = defineFirestoreRetriever(ai, {
  name: "exerciseRetriever",
  firestore: db,
  collection: "exercise-vectors",
  contentField: "content",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
});

export const nutritionRetriever = defineFirestoreRetriever(ai, {
  name: "nutritionRetriever",
  firestore: db,
  collection: "nutrition-vectors",
  contentField: "content",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
});

export const techniqueRetriever = defineFirestoreRetriever(ai, {
  name: "techniqueRetriever",
  firestore: db,
  collection: "technique-vectors",
  contentField: "content",
  vectorField: "embedding",
  embedder: textEmbedding004,
  distanceMeasure: "COSINE",
});

// Helper function to retrieve context based on category
export async function retrieveFitnessContext(
  query: string,
  options?: {
    category?: "exercise" | "nutrition" | "recovery" | "technique" | "general";
    limit?: number;
    filters?: Record<string, any>;
  }
): Promise<Document[]> {
  const limit = options?.limit ?? 5;

  let retriever;
  switch (options?.category) {
  case "exercise":
    retriever = exerciseRetriever;
    break;
  case "nutrition":
    retriever = nutritionRetriever;
    break;
  case "technique":
    retriever = techniqueRetriever;
    break;
  default:
    retriever = fitnessKnowledgeRetriever;
  }

  const results = await ai.retrieve({
    retriever,
    query,
    options: {k: limit},
  });

  return results;
}

// Helper function to format retrieved documents for prompt context
export function formatRetrievedContext(documents: Document[]): string {
  if (!documents || documents.length === 0) {
    return "No relevant context found.";
  }

  return documents
    .map((doc, index) => {
      const content = doc.content[0]?.text || "";
      const metadata = doc.metadata || {};

      return `Context ${index + 1}:
Title: ${metadata.title || "N/A"}
Category: ${metadata.category || "N/A"}
Content: ${content}
${metadata.tags ? `Tags: ${metadata.tags.join(", ")}` : ""}
---`;
    })
    .join("\n\n");
}

// Export all retrievers for Genkit UI
export const retrievers = {
  fitnessKnowledgeRetriever,
  exerciseRetriever,
  nutritionRetriever,
  techniqueRetriever,
};
