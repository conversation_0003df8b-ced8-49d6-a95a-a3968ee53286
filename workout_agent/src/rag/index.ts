/**
 * RAG Module Exports
 * Consolidates all RAG components for easy import and Genkit UI visibility
 */

// Export retrievers
export {
  fitnessKnowledgeRetriever,
  exerciseRetriever,
  nutritionRetriever,
  techniqueRetriever,
  retrievers,
  retrieveFitnessContext,
  formatRetrievedContext,
} from "./fitness-knowledge-base";

// Export indexers
export {
  exerciseIndexer,
  nutritionIndexer,
  researchIndexer,
  techniqueIndexer,
  indexers,
  indexFitnessDocument,
  bulkIndexDocuments,
} from "./indexers";

// Export sample data and indexing flow
export {
  exerciseTechniques,
  nutritionGuidelines,
  recoveryPractices,
  fitnessQA,
  getAllSampleDocuments,
  indexSampleDataFlow,
} from "./sample-knowledge";

// Export test flow
export {testRagRetrievalFlow} from "./test-rag-flow";

// Export types
export type {FitnessDocument} from "./fitness-knowledge-base";
