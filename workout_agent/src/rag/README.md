# RAG (Retrieval-Augmented Generation) for Workout Agent

This module provides RAG capabilities to enhance the workout agent with a comprehensive fitness knowledge base.

## Components

### 1. Retrievers (`fitness-knowledge-base.ts`)
- **fitnessKnowledgeRetriever**: General retriever for all fitness content
- **exerciseRetriever**: Specialized for exercise descriptions and techniques
- **nutritionRetriever**: Focused on nutrition guidelines and meal planning
- **techniqueRetriever**: For proper form and exercise execution

### 2. Indexers (`indexers.ts`)
- **exerciseIndexer**: Indexes exercise-related documents
- **nutritionIndexer**: Indexes nutrition content
- **researchIndexer**: Indexes fitness research and articles
- **techniqueIndexer**: Indexes exercise technique guides

### 3. Sample Knowledge (`sample-knowledge.ts`)
Includes pre-built fitness knowledge covering:
- Exercise techniques (squat, deadlift, bench press)
- Nutrition guidelines (protein requirements, pre-workout nutrition, hydration)
- Recovery practices (sleep, active recovery)
- Common fitness Q&A (DOMS, breaking plateaus)

## Usage

### 1. Index Sample Data
First, populate the knowledge base with sample data:

```typescript
// In Genkit UI, run the indexSampleFitnessData flow
// Or programmatically:
await indexSampleDataFlow({ categories: ["exercise", "nutrition", "recovery", "general"] });
```

### 2. Test Retrieval
Use the test flow to verify RAG is working:

```typescript
// In Genkit UI, run testRagRetrieval flow with:
{
  "query": "how to do a proper squat",
  "category": "technique",
  "limit": 3
}
```

### 3. Use in Chat Flow
The chat flow automatically uses RAG to enhance responses:

```typescript
// The chat flow retrieves relevant context for each user message
const retrievedDocs = await retrieveFitnessContext(input.message, { limit: 5 });
```

## Adding New Knowledge

### Method 1: Individual Document
```typescript
import { indexFitnessDocument } from "./rag/indexers";

await indexFitnessDocument(
  {
    id: "unique-doc-id",
    category: "exercise",
    title: "Pull-up Progression Guide",
    content: "",
    tags: ["pull-up", "back", "progression"],
    muscleGroups: ["lats", "biceps", "core"],
    difficulty: "intermediate"
  },
  "Full content of the pull-up guide..."
);
```

### Method 2: Bulk Import
```typescript
import { bulkIndexDocuments } from "./rag/indexers";

const documents = [
  {
    document: { /* FitnessDocument */ },
    content: "Document content..."
  },
  // ... more documents
];

await bulkIndexDocuments(documents);
```

## Firestore Collections

The RAG system creates these Firestore collections:
- `fitness-knowledge-vectors`: Main collection with all documents
- `exercise-vectors`: Exercise-specific documents
- `nutrition-vectors`: Nutrition-specific documents
- `technique-vectors`: Technique-specific documents

Each document includes:
- Content text
- Vector embeddings (using Google's textEmbedding004)
- Metadata (category, tags, muscle groups, etc.)

## Best Practices

1. **Document Structure**: Keep documents focused on a single topic
2. **Tags**: Use consistent, descriptive tags for better retrieval
3. **Categories**: Use the predefined categories for consistency
4. **Content Length**: Aim for 200-1000 words per document
5. **Updates**: Re-index documents when content changes significantly

## Monitoring

Check retrieval quality using the test flow:
- Verify relevant documents are returned
- Check document scores
- Ensure proper categorization

## Troubleshooting

1. **No documents returned**: 
   - Ensure sample data is indexed
   - Check Firestore collections exist
   - Verify embedder is working

2. **Poor relevance**:
   - Review document tags and categories
   - Consider query reformulation
   - Check embedding model performance

3. **Slow retrieval**:
   - Ensure Firestore indexes are created
   - Consider reducing retrieval limit
   - Check network latency