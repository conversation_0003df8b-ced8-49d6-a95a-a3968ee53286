/**
 * RAG Indexers for Fitness Content
 * Implements indexers for different types of fitness knowledge
 */

import {ai, db} from "../genkit-config";
import {Document} from "@genkit-ai/ai/retriever";
import {textEmbedding004} from "@genkit-ai/googleai";
import {FitnessDocument, FitnessDocumentSchema} from "./fitness-knowledge-base";
import {FieldValue} from "firebase-admin/firestore";

// Exercise Indexer - for exercise descriptions and techniques
export const exerciseIndexer = ai.defineIndexer(
  {
    name: "exerciseIndexer",
    embedderInfo: {
      label: "Text Embedding 004",
      dimensions: 768,
    },
  },
  async (docs: Document[]) => {
    const batch = db.batch();

    for (const doc of docs) {
      // Validate document schema
      const parsedDoc = FitnessDocumentSchema.safeParse(doc.metadata);
      if (!parsedDoc.success) {
        console.error("Invalid exercise document:", parsedDoc.error);
        continue;
      }

      const fitnessDoc = parsedDoc.data;

      // Generate embedding
      const embedding = await ai.embed({
        embedder: textEmbedding004,
        content: doc.content[0]?.text || "",
      });

      // Create document with vector
      const vectorDoc = {
        content: doc.content[0]?.text || "",
        embedding: embedding,
        category: fitnessDoc.category,
        title: fitnessDoc.title,
        tags: fitnessDoc.tags,
        muscleGroups: fitnessDoc.muscleGroups || [],
        difficulty: fitnessDoc.difficulty || "intermediate",
        metadata: {
          ...fitnessDoc.metadata,
          indexedAt: FieldValue.serverTimestamp(),
        },
      };

      // Add to exercise vectors collection
      const docRef = db.collection("exercise-vectors").doc(fitnessDoc.id);
      batch.set(docRef, vectorDoc);

      // Also add to main knowledge collection
      const mainRef = db.collection("fitness-knowledge-vectors").doc(fitnessDoc.id);
      batch.set(mainRef, vectorDoc);
    }

    await batch.commit();
    console.log(`Indexed ${docs.length} exercise documents`);
  }
);

// Nutrition Indexer - for nutrition guidelines and meal planning
export const nutritionIndexer = ai.defineIndexer(
  {
    name: "nutritionIndexer",
    embedderInfo: {
      label: "Text Embedding 004",
      dimensions: 768,
    },
  },
  async (docs: Document[]) => {
    const batch = db.batch();

    for (const doc of docs) {
      const parsedDoc = FitnessDocumentSchema.safeParse(doc.metadata);
      if (!parsedDoc.success) {
        console.error("Invalid nutrition document:", parsedDoc.error);
        continue;
      }

      const fitnessDoc = parsedDoc.data;

      // Generate embedding
      const embedding = await ai.embed({
        embedder: textEmbedding004,
        content: doc.content[0]?.text || "",
      });

      // Create document with vector
      const vectorDoc = {
        content: doc.content[0]?.text || "",
        embedding: embedding,
        category: fitnessDoc.category,
        title: fitnessDoc.title,
        tags: fitnessDoc.tags,
        metadata: {
          ...fitnessDoc.metadata,
          indexedAt: FieldValue.serverTimestamp(),
        },
      };

      // Add to nutrition vectors collection
      const docRef = db.collection("nutrition-vectors").doc(fitnessDoc.id);
      batch.set(docRef, vectorDoc);

      // Also add to main knowledge collection
      const mainRef = db.collection("fitness-knowledge-vectors").doc(fitnessDoc.id);
      batch.set(mainRef, vectorDoc);
    }

    await batch.commit();
    console.log(`Indexed ${docs.length} nutrition documents`);
  }
);

// Research/Article Indexer - for fitness research and in-depth articles
export const researchIndexer = ai.defineIndexer(
  {
    name: "researchIndexer",
    embedderInfo: {
      label: "Text Embedding 004",
      dimensions: 768,
    },
  },
  async (docs: Document[]) => {
    const batch = db.batch();

    for (const doc of docs) {
      const parsedDoc = FitnessDocumentSchema.safeParse(doc.metadata);
      if (!parsedDoc.success) {
        console.error("Invalid research document:", parsedDoc.error);
        continue;
      }

      const fitnessDoc = parsedDoc.data;

      // Generate embedding
      const embedding = await ai.embed({
        embedder: textEmbedding004,
        content: doc.content[0]?.text || "",
      });

      // Create document with vector
      const vectorDoc = {
        content: doc.content[0]?.text || "",
        embedding: embedding,
        category: fitnessDoc.category,
        title: fitnessDoc.title,
        tags: fitnessDoc.tags,
        metadata: {
          ...fitnessDoc.metadata,
          documentType: "research",
          indexedAt: FieldValue.serverTimestamp(),
        },
      };

      // Add to main knowledge collection
      const docRef = db.collection("fitness-knowledge-vectors").doc(fitnessDoc.id);
      batch.set(docRef, vectorDoc);
    }

    await batch.commit();
    console.log(`Indexed ${docs.length} research documents`);
  }
);

// Technique Indexer - for proper form and exercise execution
export const techniqueIndexer = ai.defineIndexer(
  {
    name: "techniqueIndexer",
    embedderInfo: {
      label: "Text Embedding 004",
      dimensions: 768,
    },
  },
  async (docs: Document[]) => {
    const batch = db.batch();

    for (const doc of docs) {
      const parsedDoc = FitnessDocumentSchema.safeParse(doc.metadata);
      if (!parsedDoc.success) {
        console.error("Invalid technique document:", parsedDoc.error);
        continue;
      }

      const fitnessDoc = parsedDoc.data;

      // Generate embedding
      const embedding = await ai.embed({
        embedder: textEmbedding004,
        content: doc.content[0]?.text || "",
      });

      // Create document with vector
      const vectorDoc = {
        content: doc.content[0]?.text || "",
        embedding: embedding,
        category: fitnessDoc.category,
        title: fitnessDoc.title,
        tags: fitnessDoc.tags,
        muscleGroups: fitnessDoc.muscleGroups || [],
        difficulty: fitnessDoc.difficulty || "intermediate",
        metadata: {
          ...fitnessDoc.metadata,
          documentType: "technique",
          indexedAt: FieldValue.serverTimestamp(),
        },
      };

      // Add to technique vectors collection
      const docRef = db.collection("technique-vectors").doc(fitnessDoc.id);
      batch.set(docRef, vectorDoc);

      // Also add to main knowledge collection
      const mainRef = db.collection("fitness-knowledge-vectors").doc(fitnessDoc.id);
      batch.set(mainRef, vectorDoc);
    }

    await batch.commit();
    console.log(`Indexed ${docs.length} technique documents`);
  }
);

// Helper function to index a single document
export async function indexFitnessDocument(
  document: FitnessDocument,
  content: string
) {
  const doc = {
    content: [{text: content}],
    metadata: document,
  };

  switch (document.category) {
  case "exercise":
    await ai.index({
      indexer: exerciseIndexer,
      documents: [doc],
    });
    break;
  case "nutrition":
    await ai.index({
      indexer: nutritionIndexer,
      documents: [doc],
    });
    break;
  case "technique":
    await ai.index({
      indexer: techniqueIndexer,
      documents: [doc],
    });
    break;
  default:
    await ai.index({
      indexer: researchIndexer,
      documents: [doc],
    });
  }
}

// Helper function to bulk index documents
export async function bulkIndexDocuments(
  documents: Array<{ document: FitnessDocument; content: string }>
) {
  const exerciseDocs = [];
  const nutritionDocs = [];
  const techniqueDocs = [];
  const researchDocs = [];

  for (const {document, content} of documents) {
    const doc = {
      content: [{text: content}],
      metadata: document,
    };

    switch (document.category) {
    case "exercise":
      exerciseDocs.push(doc);
      break;
    case "nutrition":
      nutritionDocs.push(doc);
      break;
    case "technique":
      techniqueDocs.push(doc);
      break;
    default:
      researchDocs.push(doc);
    }
  }

  // Index in parallel
  await Promise.all([
    exerciseDocs.length > 0 ? ai.index({indexer: exerciseIndexer, documents: exerciseDocs}) : Promise.resolve(),
    nutritionDocs.length > 0 ? ai.index({indexer: nutritionIndexer, documents: nutritionDocs}) : Promise.resolve(),
    techniqueDocs.length > 0 ? ai.index({indexer: techniqueIndexer, documents: techniqueDocs}) : Promise.resolve(),
    researchDocs.length > 0 ? ai.index({indexer: researchIndexer, documents: researchDocs}) : Promise.resolve(),
  ]);

  console.log(`Bulk indexed ${documents.length} documents`);
}

// Export all indexers for Genkit UI
export const indexers = {
  exerciseIndexer,
  nutritionIndexer,
  researchIndexer,
  techniqueIndexer,
};
