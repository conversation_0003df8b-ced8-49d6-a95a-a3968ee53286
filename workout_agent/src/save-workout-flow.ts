/**
 * Save Workout Flow
 * Flows for saving workout templates and logging completed workouts
 */

import {genkit, z} from "genkit";
import {googleAI, gemini15Flash} from "@genkit-ai/googleai";
import * as admin from "firebase-admin";

// Initialize Firebase Admin
import * as serviceAccount from "../po2vf2ae7tal9invaj7jkf4a06hsac-firebase-adminsdk-fbsvc-4c3d1779a6.json";

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
    projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
  });
}

const db = admin.firestore();

// Initialize Genkit
export const ai = genkit({
  plugins: [googleAI({
    apiKey: "AIzaSyDG5SmBVzfEkNabJ6dy-shXGs1eXERmBoc",
  })],
  model: gemini15Flash,
});

/**
 * Save workout as a reusable template in userWorkouts
 */
export const saveWorkoutTemplateFlow = ai.defineFlow(
  {
    name: "saveWorkoutTemplate",
    inputSchema: z.object({
      userId: z.string(),
      name: z.string(),
      description: z.string(),
      category: z.enum(["strength", "cardio", "flexibility", "mixed", "yoga", "hiit"]),
      difficulty: z.enum(["beginner", "intermediate", "advanced"]),
      duration: z.number().describe("Duration in minutes"),
      exercises: z.array(z.object({
        exerciseId: z.string(),
        exerciseName: z.string(),
        sets: z.number(),
        reps: z.string().describe("Can be number or duration like \"30 seconds\""),
        restTime: z.number().optional(),
        notes: z.string().optional(),
      })),
      isPublic: z.boolean().optional(),
      tags: z.array(z.string()).optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      workoutId: z.string().optional(),
      message: z.string(),
    }),
  },
  async (input) => {
    try {
      // Format exercises for userWorkouts collection
      const exercisesForTemplate = await Promise.all(
        input.exercises.map(async (ex) => {
          // Try to get exercise details
          const exerciseDoc = await db.collection("exercises").doc(ex.exerciseId).get();
          const exerciseData = exerciseDoc.exists ? exerciseDoc.data() : null;

          return {
            exerciseId: ex.exerciseId,
            notes: {
              name: ex.exerciseName,
              equipment: exerciseData?.equipment?.[0] || "bodyweight",
              targetMuscles: exerciseData ?
                [exerciseData.primaryMuscleGroup, ...(exerciseData.secondaryMuscleGroups || [])] :
                ["full_body"],
              instructions: ex.notes || "",
            },
            sets: ex.sets,
            reps: isNaN(Number(ex.reps)) ? 1 : Number(ex.reps),
            restTime: ex.restTime || 60,
            duration: isNaN(Number(ex.reps)) ? 30 : 0, // If reps is like "30 seconds", use duration
          };
        })
      );

      // Get equipment list from exercises
      const equipment = new Set<string>();
      const targetMuscleGroups = new Set<string>();

      for (const ex of input.exercises) {
        const exerciseDoc = await db.collection("exercises").doc(ex.exerciseId).get();
        if (exerciseDoc.exists) {
          const data = exerciseDoc.data()!;
          data.equipment?.forEach((eq: string) => equipment.add(eq));
          if (data.primaryMuscleGroup) targetMuscleGroups.add(data.primaryMuscleGroup);
        }
      }

      // Create userWorkout entry
      const workoutData = {
        userId: input.userId,
        name: input.name,
        description: input.description,
        category: input.category,
        difficulty: input.difficulty,
        duration: input.duration,
        exercises: exercisesForTemplate,
        equipment: Array.from(equipment).filter(Boolean),
        targetMuscleGroups: Array.from(targetMuscleGroups),
        isCustom: true,
        isPublic: input.isPublic || false,
        tags: input.tags || [input.category, input.difficulty],
        imageUrl: "https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400",
        createdBy: input.userId,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      const workoutRef = await db.collection("userWorkouts").add(workoutData);

      return {
        success: true,
        workoutId: workoutRef.id,
        message: `Workout template "${input.name}" saved successfully!`,
      };
    } catch (error) {
      console.error("Error saving workout template:", error);
      return {
        success: false,
        message: `Failed to save workout template: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }
);

/**
 * Log a completed workout to history
 */
export const logCompletedWorkoutFlow = ai.defineFlow(
  {
    name: "logCompletedWorkout",
    inputSchema: z.object({
      userId: z.string(),
      workoutTemplateId: z.string().optional().describe("ID from userWorkouts collection"),
      workoutName: z.string(),
      duration: z.number().describe("Actual duration in minutes"),
      exercises: z.array(z.object({
        exerciseId: z.string(),
        exerciseName: z.string(),
        setsCompleted: z.array(z.object({
          reps: z.number(),
          weight: z.number().optional(),
          restTaken: z.number().optional(),
        })),
        notes: z.string().optional(),
      })),
      difficulty: z.enum(["easy", "moderate", "hard"]).optional(),
      overallNotes: z.string().optional(),
    }),
    outputSchema: z.object({
      success: z.boolean(),
      historyId: z.string().optional(),
      message: z.string(),
      statsUpdated: z.boolean(),
    }),
  },
  async (input) => {
    try {
      // Fetch exercises to get muscle groups and calories
      const exerciseIds = input.exercises.map((e) => e.exerciseId);
      const exerciseDocs = await Promise.all(
        exerciseIds.map((id) => db.collection("exercises").doc(id).get())
      );

      const exerciseData = exerciseDocs.map((doc) => doc.exists ? doc.data() : null);

      // Calculate muscles worked and calories
      const musclesWorked = new Set<string>();
      let totalCalories = 0;

      exerciseData.forEach((data) => {
        if (data) {
          if (data.primaryMuscleGroup) {
            musclesWorked.add(data.primaryMuscleGroup);
          }
          if (data.secondaryMuscleGroups) {
            data.secondaryMuscleGroups.forEach((muscle: string) => musclesWorked.add(muscle));
          }
          const exerciseDuration = input.duration / input.exercises.length;
          totalCalories += (data.caloriesPerMinute || 6) * exerciseDuration;
        }
      });

      // Create workout history entry
      const workoutData = {
        userId: input.userId,
        workoutPlanId: input.workoutTemplateId || null,
        workoutPlanName: input.workoutName,
        startTime: admin.firestore.Timestamp.now(),
        endTime: admin.firestore.Timestamp.now(),
        duration: input.duration,
        exercises: input.exercises,
        musclesWorked: Array.from(musclesWorked),
        totalCaloriesBurned: Math.round(totalCalories),
        difficulty: input.difficulty || "moderate",
        overallNotes: input.overallNotes || "",
        completedAt: admin.firestore.Timestamp.now(),
        createdAt: admin.firestore.Timestamp.now(),
      };

      const workoutRef = await db.collection("workoutHistory").add(workoutData);

      // Update user stats
      const userRef = db.collection("users").doc(input.userId);
      const currentMonth = new Date().toISOString().slice(0, 7);

      await userRef.update({
        "stats.totalWorkouts": admin.firestore.FieldValue.increment(1),
        "stats.totalMinutes": admin.firestore.FieldValue.increment(input.duration),
        "stats.totalCaloriesBurned": admin.firestore.FieldValue.increment(Math.round(totalCalories)),
        "stats.lastWorkoutDate": admin.firestore.Timestamp.now(),
        "stats.currentStreak": admin.firestore.FieldValue.increment(1),
        [`stats.monthlyStats.${currentMonth}.workouts`]: admin.firestore.FieldValue.increment(1),
        [`stats.monthlyStats.${currentMonth}.minutes`]: admin.firestore.FieldValue.increment(input.duration),
        [`stats.monthlyStats.${currentMonth}.calories`]: admin.firestore.FieldValue.increment(Math.round(totalCalories)),
        "updatedAt": admin.firestore.Timestamp.now(),
      });

      return {
        success: true,
        historyId: workoutRef.id,
        message: `Workout completed! You burned approximately ${Math.round(totalCalories)} calories.`,
        statsUpdated: true,
      };
    } catch (error) {
      console.error("Error logging workout:", error);
      return {
        success: false,
        message: `Failed to log workout: ${error instanceof Error ? error.message : "Unknown error"}`,
        statsUpdated: false,
      };
    }
  }
);
