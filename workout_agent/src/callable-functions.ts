/**
 * Callable Functions for Flutter App
 * These functions can be called directly from your Flutter app
 * using Cloud Functions
 */

import {onCall, HttpsError} from "firebase-functions/v2/https";
import {
  recommendNextExerciseFlow,
} from "./workout-recommendation-agent";
import {
  generateFitnessGuideFlow,
  createFirstWorkoutFlow,
  completeOnboardingFlow,
} from "./flows/onboarding-flows-v2";
import {
  analyzeLastWorkoutFlow,
  generateNextWorkoutFlow,
} from "./flows/next-workout-flows-v2";
import {fitnesssChatFlow} from "./flows/chat-flow";

// Recommend Next Exercise
export const recommendNextExercise = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId, saveWorkout} = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await recommendNextExerciseFlow({
      userId,
      saveWorkout: saveWorkout || false,
    });
  }
);

// Generate Fitness Guide
export const generateFitnessGuide = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId} = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await generateFitnessGuideFlow({userId});
  }
);

// Create First Workout
export const createFirstWorkout = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId, guideId} = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await createFirstWorkoutFlow({userId, guideId});
  }
);

// Complete Onboarding
export const completeOnboarding = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId} = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await completeOnboardingFlow({userId});
  }
);

// Analyze Last Workout
export const analyzeLastWorkout = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId, workoutId} = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await analyzeLastWorkoutFlow({userId, workoutId});
  }
);

// Generate Next Workout
export const generateNextWorkout = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId, skipRecoveryCheck, targetMuscles, workoutType} =
      request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "userId is required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await generateNextWorkoutFlow({
      userId,
      skipRecoveryCheck,
      targetMuscles,
      workoutType,
    });
  }
);

// Fitness Chat
export const fitnessChat = onCall(
  {
    cors: true,
    maxInstances: 10,
  },
  async (request) => {
    console.log("fitnessChat called with auth:", request.auth ? "present" : "missing");
    console.log("Auth UID:", request.auth?.uid);
    console.log("Request userId:", request.data?.userId);
    // Check authentication
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "User must be authenticated");
    }

    const {userId, message, conversationId} = request.data;

    if (!userId || !message) {
      throw new HttpsError("invalid-argument", "userId and message are required");
    }

    // Verify the userId matches the authenticated user
    if (request.auth.uid !== userId) {
      throw new HttpsError("permission-denied", "User can only access their own data");
    }

    return await fitnesssChatFlow({
      userId,
      message,
      conversationId,
    });
  }
);
