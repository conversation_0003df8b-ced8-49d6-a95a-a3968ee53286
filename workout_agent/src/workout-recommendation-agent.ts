/**
 * Workout Recommendation Agent
 *
 * An intelligent agent that recommends exercises based on:
 * - User profile data
 * - Workout history
 * - Exercise database
 */

import {z} from "genkit";
import * as admin from "firebase-admin";
import {ai, db} from "./genkit-config";

// Export ai for other modules that import from this file
export {ai};

// ============= REUSABLE TOOLS =============

/**
 * Tool to get user profile and fitness data
 */
export const getUserProfileTool = ai.defineTool(
  {
    name: "getUserProfile",
    description: "Get comprehensive user profile including fitness level, goals, and preferences",
    inputSchema: z.object({
      userId: z.string(),
    }),
    outputSchema: z.object({
      userId: z.string(),
      profile: z.object({
        age: z.number(),
        gender: z.string(),
        height: z.number(),
        weight: z.number(),
        preferredUnits: z.string(),
      }),
      fitness: z.object({
        cardioLevel: z.number(),
        strengthLevel: z.number(),
        flexibilityLevel: z.number(),
        goals: z.array(z.object({
          type: z.string(),
          priority: z.number(),
        })),
        exercisesToAvoid: z.array(z.string()),
      }),
      preferences: z.object({
        workoutsPerWeek: z.number(),
        durationMinutes: z.number(),
        environments: z.array(z.string()),
        theme: z.string(),
        notifications: z.boolean(),
      }),
      stats: z.object({
        totalWorkouts: z.number(),
        currentStreak: z.number(),
        lastWorkoutDate: z.any(),
      }),
    }),
  },
  async (input) => {
    if (!input.userId) {
      throw new Error("userId is required");
    }

    const userDoc = await db.collection("users").doc(input.userId).get();
    if (!userDoc.exists) {
      throw new Error(`User ${input.userId} not found`);
    }

    const userData = userDoc.data()!;

    // Calculate age from dateOfBirth
    const birthDate = userData.profile?.dateOfBirth?.toDate() || new Date("1990-01-01");
    const age = new Date().getFullYear() - birthDate.getFullYear();

    return {
      userId: input.userId,
      profile: {
        age,
        gender: userData.profile?.gender || "unspecified",
        height: userData.profile?.height || 170,
        weight: userData.profile?.weight || 70,
        preferredUnits: userData.profile?.preferredUnits || "metric",
      },
      fitness: {
        cardioLevel: userData.fitness?.cardioLevel || 0.5,
        strengthLevel: userData.fitness?.strengthLevel || 0.5,
        flexibilityLevel: userData.fitness?.flexibilityLevel || 0.5,
        goals: userData.fitness?.goals || [
          {type: "general_fitness", priority: 1},
        ],
        exercisesToAvoid: userData.fitness?.exercisesToAvoid || [],
      },
      preferences: {
        workoutsPerWeek: userData.preferences?.workoutsPerWeek || 3,
        durationMinutes: userData.preferences?.durationMinutes || 30,
        environments: userData.preferences?.environments || ["home"],
        theme: userData.preferences?.theme || "system",
        notifications: userData.preferences?.notifications ?? true,
      },
      stats: {
        totalWorkouts: userData.stats?.totalWorkouts || 0,
        currentStreak: userData.stats?.currentStreak || 0,
        lastWorkoutDate: userData.stats?.lastWorkoutDate || null,
      },
    };
  }
);

/**
 * Tool to analyze workout history and patterns
 */
export const analyzeWorkoutHistoryTool = ai.defineTool(
  {
    name: "analyzeWorkoutHistory",
    description: "Analyze user workout history to identify patterns, progress, and areas for improvement",
    inputSchema: z.object({
      userId: z.string(),
      daysToAnalyze: z.number().default(30),
    }),
    outputSchema: z.object({
      totalWorkouts: z.number(),
      averageFrequency: z.number(),
      muscleGroupFrequency: z.record(z.number()),
      lastWorkoutDate: z.string().optional(),
      progressTrends: z.object({
        strengthImprovement: z.number(),
        enduranceImprovement: z.number(),
        consistencyScore: z.number(),
      }),
      recentExercises: z.array(z.object({
        exerciseId: z.string(),
        name: z.string(),
        lastPerformed: z.string(),
        performanceScore: z.number(),
      })),
    }),
  },
  async (input) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - input.daysToAnalyze);

    // Query with new schema
    const workoutsSnapshot = await db.collection("workoutHistory")
      .where("userId", "==", input.userId)
      .orderBy("completedAt", "desc")
      .limit(30)
      .get();

    const workouts = workoutsSnapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
    })) as any[];

    // Analyze muscle group frequency from new schema
    const muscleGroupFrequency: Record<string, number> = {};
    const recentExercises: any[] = [];
    const exercisePerformance: Record<string, { name: string, performances: number[] }> = {};

    for (const workout of workouts) {
      // Track muscles worked
      if (workout.musclesWorked) {
        for (const muscle of workout.musclesWorked) {
          muscleGroupFrequency[muscle] = (muscleGroupFrequency[muscle] || 0) + 1;
        }
      }

      // Track exercise performance with new schema
      if (workout.exercises) {
        for (const exercise of workout.exercises) {
          if (!exercisePerformance[exercise.exerciseId]) {
            exercisePerformance[exercise.exerciseId] = {
              name: exercise.exerciseName,
              performances: [],
            };
          }

          // Calculate average performance from sets
          const avgReps = exercise.setsCompleted?.reduce((sum: number, set: any) =>
            sum + (set.reps || 0), 0) / (exercise.setsCompleted?.length || 1) || 0;

          exercisePerformance[exercise.exerciseId].performances.push(avgReps);
        }
      }
    }

    // Calculate recent exercises with performance scores
    for (const [exerciseId, data] of Object.entries(exercisePerformance)) {
      const performances = data.performances;
      const avgPerformance = performances.reduce((a, b) => a + b, 0) / performances.length;
      const improvement = performances.length > 1 ?
        (performances[performances.length - 1] - performances[0]) / performances[0] : 0;

      recentExercises.push({
        exerciseId,
        name: data.name,
        lastPerformed: new Date().toISOString(),
        performanceScore: avgPerformance * (1 + improvement),
      });
    }

    return {
      totalWorkouts: workouts.length,
      averageFrequency: workouts.length / (input.daysToAnalyze / 7), // per week
      muscleGroupFrequency,
      lastWorkoutDate: workouts[0]?.completedAt?.toDate?.()?.toISOString(),
      progressTrends: {
        strengthImprovement: 0.15, // Placeholder - would calculate from actual data
        enduranceImprovement: 0.10,
        consistencyScore: Math.min(workouts.length / (input.daysToAnalyze / 3), 1), // expect 3 days/week
      },
      recentExercises: recentExercises.slice(0, 10),
    };
  }
);

/**
 * Tool to search for exercises with advanced filtering
 */
export const searchExercisesTool = ai.defineTool(
  {
    name: "searchExercises",
    description: "Search exercise database with filters for muscle group, equipment, difficulty, and user constraints",
    inputSchema: z.object({
      muscleGroups: z.array(z.string()).optional(),
      equipment: z.array(z.string()).optional(),
      difficulty: z.enum(["beginner", "intermediate", "advanced"]).optional(),
      excludeExercises: z.array(z.string()).optional(),
      limit: z.number().default(10),
    }),
    outputSchema: z.array(z.object({
      id: z.string(),
      name: z.string(),
      description: z.string(),
      primaryMuscleGroup: z.string(),
      secondaryMuscleGroups: z.array(z.string()),
      equipment: z.array(z.string()),
      difficulty: z.string(),
      instructions: z.array(z.string()),
      tips: z.array(z.string()).optional(),
      caloriesPerMinute: z.number(),
    })),
  },
  async (input) => {
    let query: admin.firestore.Query = db.collection("exercises");

    // Apply filters
    if (input.difficulty) {
      query = query.where("difficulty", "==", input.difficulty);
    }

    if (input.muscleGroups && input.muscleGroups.length > 0) {
      // Convert to lowercase for matching
      const lowerMuscleGroups = input.muscleGroups.map((m) => m.toLowerCase());
      query = query.where("primaryMuscleGroup", "in", lowerMuscleGroups);
    }

    const limitValue = Number(input.limit) || 10;
    const snapshot = await query.limit(limitValue * 2).get(); // Get extra for filtering
    let exercises = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      secondaryMuscleGroups: doc.data().secondaryMuscleGroups || [],
      instructions: doc.data().instructions || [],
      tips: doc.data().tips || [],
    })) as any[];

    // Filter by equipment
    if (input.equipment && input.equipment.length > 0) {
      const hasNoEquipment = input.equipment.includes("bodyweight") ||
                           input.equipment.includes("none") ||
                           input.equipment.includes("homeNoEquipment");

      exercises = exercises.filter((ex) => {
        if (ex.equipment.length === 0 && hasNoEquipment) return true;
        return ex.equipment.some((eq: string) => input.equipment!.includes(eq));
      });
    }

    // Exclude specific exercises
    if (input.excludeExercises && input.excludeExercises.length > 0) {
      exercises = exercises.filter((ex) =>
        !input.excludeExercises!.includes(ex.id)
      );
    }

    return exercises.slice(0, limitValue);
  }
);

// ============= MAIN RECOMMENDATION AGENT =============

export const recommendNextExerciseFlow = ai.defineFlow(
  {
    name: "recommendNextExercise",
    inputSchema: z.object({
      userId: z.string().describe("The user ID to generate recommendations for"),
      saveWorkout: z.boolean().optional().describe("Whether to save the workout to history"),
    }),
    outputSchema: z.object({
      recommendedExercise: z.object({
        exercise: z.any(),
        reasoning: z.string(),
        sets: z.number(),
        reps: z.number(),
        restTime: z.number(),
        alternatives: z.array(z.any()),
      }),
      workoutPlan: z.object({
        theme: z.string(),
        totalDuration: z.number(),
        exercises: z.array(z.any()),
        cooldownSuggestions: z.array(z.string()),
      }),
      insights: z.object({
        basedOnHistory: z.array(z.string()),
        personalizedTips: z.array(z.string()),
      }),
      workoutSaved: z.boolean().optional(),
      workoutId: z.string().optional(),
    }),
  },
  async (input) => {
    // Validate input
    if (!input.userId) {
      throw new Error("userId is required");
    }

    try {
      // Gather all necessary data using tools
      const [userProfile, workoutHistory] = await Promise.all([
        getUserProfileTool({userId: input.userId}),
        analyzeWorkoutHistoryTool({userId: input.userId, daysToAnalyze: 30}),
      ]);

      // Determine which muscle groups need work
      const underworkedMuscles = identifyUnderworkedMuscles(workoutHistory.muscleGroupFrequency);

      // Map equipment preferences
      const equipment = userProfile.preferences.environments.includes("homeNoEquipment") ?
        ["bodyweight"] :
        userProfile.preferences.environments.includes("gym") ?
          ["barbell", "dumbbell", "cable", "machine", "bodyweight"] :
          ["bodyweight"];

      // Search for appropriate exercises
      const exercises = await searchExercisesTool({
        muscleGroups: underworkedMuscles.slice(0, 3),
        equipment,
        difficulty: determineAppropiateDifficulty(userProfile.fitness),
        excludeExercises: [
          ...userProfile.fitness.exercisesToAvoid,
          ...workoutHistory.recentExercises.slice(0, 5).map((e) => e.exerciseId),
        ],
        limit: 10,
      });

      // Use AI to create personalized recommendation
      const recommendationPrompt = `
      Based on this user data, recommend the next exercise and create a complete workout plan:
      
      User Profile:
      - Age: ${userProfile.profile.age} years, ${userProfile.profile.gender}
      - Fitness Level: Strength ${userProfile.fitness.strengthLevel}, Cardio ${userProfile.fitness.cardioLevel}
      - Goals: ${userProfile.fitness.goals.map((g) => g.type).join(", ")}
      - Environment: ${userProfile.preferences.environments.join(", ")}
      - Preferred Duration: ${userProfile.preferences.durationMinutes} minutes
      
      Workout History Analysis:
      - Total Workouts Last Month: ${workoutHistory.totalWorkouts}
      - Frequency: ${workoutHistory.averageFrequency.toFixed(1)} per week
      - Underworked Muscles: ${underworkedMuscles.join(", ")}
      - Consistency Score: ${(workoutHistory.progressTrends.consistencyScore * 100).toFixed(0)}%
      
      Available Exercises:
      ${exercises.map((ex) => `- ${ex.name}: ${ex.description} (${ex.primaryMuscleGroup})`).join("\n")}
      
      Create a recommendation that:
      1. Selects the best next exercise with clear reasoning
      2. Provides a complete workout plan for today
      3. Includes personalized tips based on the user's history and goals
      4. Respects their ${userProfile.preferences.durationMinutes} minute time limit
      `;

      const aiRecommendation = await ai.generate({
        model: "googleai/gemini-1.5-flash",
        prompt: recommendationPrompt,
        output: {
          format: "json",
          schema: z.object({
            nextExercise: z.object({
              name: z.string(),
              reasoning: z.string(),
              sets: z.number(),
              reps: z.number(),
              restSeconds: z.number(),
            }),
            workoutPlan: z.object({
              theme: z.string(),
              exercises: z.array(z.object({
                name: z.string(),
                sets: z.number(),
                reps: z.number(),
                notes: z.string(),
              })),
            }),
            tips: z.array(z.string()),
          }),
        },
      });

      const recommendation = aiRecommendation.output!;

      // Find the full exercise details
      const recommendedExercise = exercises.find((ex) =>
        ex.name.toLowerCase().includes(recommendation.nextExercise.name.toLowerCase()) ||
        recommendation.nextExercise.name.toLowerCase().includes(ex.name.toLowerCase())
      ) || exercises[0];

      // Prepare the workout plan result
      const workoutResult = {
        recommendedExercise: {
          exercise: recommendedExercise,
          reasoning: recommendation.nextExercise.reasoning,
          sets: recommendation.nextExercise.sets,
          reps: recommendation.nextExercise.reps,
          restTime: recommendation.nextExercise.restSeconds,
          alternatives: exercises.slice(1, 4),
        },
        workoutPlan: {
          theme: recommendation.workoutPlan.theme,
          totalDuration: userProfile.preferences.durationMinutes,
          exercises: recommendation.workoutPlan.exercises,
          cooldownSuggestions: [
            "5 minutes light stretching focusing on worked muscles",
            "Deep breathing exercises for 2-3 minutes",
            "Foam rolling if available",
          ],
        },
        insights: {
          basedOnHistory: [
            `You've been consistent with ${workoutHistory.averageFrequency.toFixed(0)} workouts per week`,
            `Your ${underworkedMuscles[0].toLowerCase()} muscles need more attention`,
            `Great progress on strength: +${(workoutHistory.progressTrends.strengthImprovement * 100).toFixed(0)}%`,
          ],
          personalizedTips: recommendation.tips,
        },
        workoutSaved: false as boolean,
        workoutId: undefined as string | undefined,
      };

      // Save workout if requested
      if (input.saveWorkout) {
        try {
          // Map exercises to the format expected by userWorkouts
          const exercisesForWorkout = recommendation.workoutPlan.exercises.map((ex: any, index: number) => {
            // Try to find the matching exercise from our search results
            const matchingExercise = exercises.find((e) =>
              e.name.toLowerCase().includes(ex.name.toLowerCase()) ||
              ex.name.toLowerCase().includes(e.name.toLowerCase())
            );

            return {
              exerciseId: matchingExercise?.id || `temp_${index}`,
              notes: {
                name: ex.name,
                equipment: matchingExercise?.equipment?.[0] || "bodyweight",
                targetMuscles: matchingExercise ?
                  [matchingExercise.primaryMuscleGroup, ...(matchingExercise.secondaryMuscleGroups || [])] :
                  ["full_body"],
              },
              sets: ex.sets,
              reps: ex.reps,
              restTime: recommendation.nextExercise.restSeconds || 60,
              duration: 0, // Not applicable for strength exercises
            };
          });

          // Create userWorkout entry
          const workoutData = {
            userId: input.userId,
            name: `AI ${recommendation.workoutPlan.theme} - ${new Date().toLocaleDateString()}`,
            description: `AI-generated ${recommendation.workoutPlan.theme.toLowerCase()} workout based on your profile and recent activity`,
            category: recommendation.workoutPlan.theme.toLowerCase().includes("strength") ? "strength" :
              recommendation.workoutPlan.theme.toLowerCase().includes("cardio") ? "cardio" : "mixed",
            difficulty: determineAppropiateDifficulty(userProfile.fitness),
            duration: userProfile.preferences.durationMinutes,
            exercises: exercisesForWorkout,
            equipment: [...new Set(exercises.flatMap((ex: any) => ex.equipment))].filter(Boolean),
            targetMuscleGroups: [...new Set(exercises.map((ex: any) => ex.primaryMuscleGroup))],
            isCustom: true,
            isPublic: false,
            tags: ["ai-generated", recommendation.workoutPlan.theme.toLowerCase(), "personalized"],
            imageUrl: "https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400",
            createdBy: input.userId,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
          };

          const workoutRef = await db.collection("userWorkouts").add(workoutData);

          workoutResult.workoutSaved = true;
          workoutResult.workoutId = workoutRef.id;
        } catch (saveError) {
          console.error("Error saving workout:", saveError);
          // Don't fail the whole flow if save fails
        }
      }

      return workoutResult;
    } catch (error) {
      console.error("Error in recommendNextExercise:", error);

      // Return a default recommendation if something fails
      return {
        recommendedExercise: {
          exercise: {
            id: "ex_squats_001",
            name: "Bodyweight Squats",
            description: "A fundamental lower body exercise",
            primaryMuscleGroup: "legs",
            equipment: [],
            difficulty: "beginner",
          },
          reasoning: "Starting with a basic exercise. Please ensure your profile is complete.",
          sets: 3,
          reps: 15,
          restTime: 60,
          alternatives: [],
        },
        workoutPlan: {
          theme: "General Fitness",
          totalDuration: 30,
          exercises: [
            {name: "Warm-up", sets: 1, reps: 5, notes: "5 minutes light movement"},
            {name: "Bodyweight Squats", sets: 3, reps: 15, notes: "Focus on form"},
            {name: "Push-ups", sets: 3, reps: 10, notes: "Modify as needed"},
            {name: "Plank", sets: 3, reps: 30, notes: "30 second holds"},
          ],
          cooldownSuggestions: ["5 minutes stretching"],
        },
        insights: {
          basedOnHistory: ["Unable to load full history - showing default workout"],
          personalizedTips: ["Complete your profile for better recommendations"],
        },
        workoutSaved: false,
        workoutId: undefined,
      };
    }
  }
);

// ============= HELPER FUNCTIONS =============

function identifyUnderworkedMuscles(muscleGroupFrequency: Record<string, number>): string[] {
  const allMuscleGroups = ["chest", "back", "shoulders", "arms", "legs", "core", "glutes"];
  const frequencies = allMuscleGroups.map((muscle) => ({
    muscle,
    frequency: muscleGroupFrequency[muscle] || 0,
  }));

  // Sort by frequency (ascending) to find underworked muscles
  frequencies.sort((a, b) => a.frequency - b.frequency);

  return frequencies.map((f) => f.muscle);
}

function determineAppropiateDifficulty(fitness: any): "beginner" | "intermediate" | "advanced" {
  const avgLevel = (fitness.strengthLevel + fitness.cardioLevel) / 2;
  if (avgLevel < 0.33) return "beginner";
  if (avgLevel < 0.67) return "intermediate";
  return "advanced";
}

// Export tools for reuse in other agents
export const workoutTools = {
  getUserProfileTool,
  analyzeWorkoutHistoryTool,
  searchExercisesTool,
};
