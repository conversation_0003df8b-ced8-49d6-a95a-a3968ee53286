// Flutter Usage Example for Workout Agent Functions

import 'package:cloud_functions/cloud_functions.dart';

class WorkoutAgentService {
  final FirebaseFunctions _functions = FirebaseFunctions.instance;
  
  // Configure functions for your region if needed
  WorkoutAgentService() {
    // Optional: Set region if not using default
    // _functions = FirebaseFunctions.instanceFor(region: 'us-central1');
  }

  // 1. Generate Fitness Guide (for onboarding)
  Future<Map<String, dynamic>> generateFitnessGuide(String userId) async {
    try {
      final callable = _functions.httpsCallable('generateFitnessGuide');
      final result = await callable.call({
        'userId': userId,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error generating fitness guide: $e');
      rethrow;
    }
  }

  // 2. Create First Workout
  Future<Map<String, dynamic>> createFirstWorkout(String userId, {String? guideId}) async {
    try {
      final callable = _functions.httpsCallable('createFirstWorkout');
      final result = await callable.call({
        'userId': userId,
        if (guideId != null) 'guideId': guideId,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error creating first workout: $e');
      rethrow;
    }
  }

  // 3. Complete Onboarding (does both guide + first workout)
  Future<Map<String, dynamic>> completeOnboarding(String userId) async {
    try {
      final callable = _functions.httpsCallable('completeOnboarding');
      final result = await callable.call({
        'userId': userId,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error completing onboarding: $e');
      rethrow;
    }
  }

  // 4. Recommend Next Exercise
  Future<Map<String, dynamic>> recommendNextExercise(
    String userId, {
    bool saveWorkout = false,
  }) async {
    try {
      final callable = _functions.httpsCallable('recommendNextExercise');
      final result = await callable.call({
        'userId': userId,
        'saveWorkout': saveWorkout,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error recommending exercise: $e');
      rethrow;
    }
  }

  // 5. Analyze Last Workout
  Future<Map<String, dynamic>> analyzeLastWorkout(
    String userId, {
    String? workoutId,
  }) async {
    try {
      final callable = _functions.httpsCallable('analyzeLastWorkout');
      final result = await callable.call({
        'userId': userId,
        if (workoutId != null) 'workoutId': workoutId,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error analyzing workout: $e');
      rethrow;
    }
  }

  // 6. Generate Next Workout
  Future<Map<String, dynamic>> generateNextWorkout(
    String userId, {
    bool? skipRecoveryCheck,
    List<String>? targetMuscles,
    String? workoutType,
  }) async {
    try {
      final callable = _functions.httpsCallable('generateNextWorkout');
      final result = await callable.call({
        'userId': userId,
        if (skipRecoveryCheck != null) 'skipRecoveryCheck': skipRecoveryCheck,
        if (targetMuscles != null) 'targetMuscles': targetMuscles,
        if (workoutType != null) 'workoutType': workoutType,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error generating workout: $e');
      rethrow;
    }
  }

  // 7. Fitness Chat
  Future<Map<String, dynamic>> sendChatMessage(
    String userId,
    String message, {
    String? conversationId,
  }) async {
    try {
      final callable = _functions.httpsCallable('fitnessChat');
      final result = await callable.call({
        'userId': userId,
        'message': message,
        if (conversationId != null) 'conversationId': conversationId,
      });
      return result.data as Map<String, dynamic>;
    } catch (e) {
      print('Error in chat: $e');
      rethrow;
    }
  }
}

// Example Usage in a Flutter Widget
class WorkoutScreen extends StatefulWidget {
  @override
  _WorkoutScreenState createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends State<WorkoutScreen> {
  final WorkoutAgentService _workoutService = WorkoutAgentService();
  bool _isLoading = false;
  Map<String, dynamic>? _workoutData;

  // Example: Generate next workout
  Future<void> _generateWorkout() async {
    setState(() => _isLoading = true);
    
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final result = await _workoutService.generateNextWorkout(
        userId,
        targetMuscles: ['chest', 'triceps'],
        workoutType: 'strength',
      );
      
      setState(() {
        _workoutData = result;
        _isLoading = false;
      });
      
      // Access the workout data
      if (result['success'] == true) {
        final workout = result['workout'];
        print('Generated workout: ${workout['name']}');
        print('Exercises: ${workout['exercises']}');
      }
    } catch (e) {
      setState(() => _isLoading = false);
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  // Example: Chat with AI
  Future<void> _sendChatMessage(String message) async {
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final result = await _workoutService.sendChatMessage(
        userId,
        message,
        conversationId: 'conv_${DateTime.now().millisecondsSinceEpoch}',
      );
      
      if (result['response'] != null) {
        // Display AI response
        print('AI Response: ${result['response']}');
        
        // Show suggested follow-ups if any
        if (result['suggestions'] != null) {
          print('Suggestions: ${result['suggestions']}');
        }
      }
    } catch (e) {
      // Handle error
      print('Chat error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Your UI implementation
    return Container();
  }
}

// Example: Onboarding Flow
class OnboardingService {
  final WorkoutAgentService _workoutService = WorkoutAgentService();
  
  Future<void> completeUserOnboarding(String userId) async {
    try {
      // Option 1: Complete everything at once
      final result = await _workoutService.completeOnboarding(userId);
      
      if (result['success'] == true) {
        print('Onboarding completed!');
        print('Guide ID: ${result['guideId']}');
        print('First Workout ID: ${result['workoutId']}');
      }
      
      // Option 2: Step by step
      // Step 1: Generate fitness guide
      final guideResult = await _workoutService.generateFitnessGuide(userId);
      final guideId = guideResult['guideId'];
      
      // Step 2: Create first workout
      final workoutResult = await _workoutService.createFirstWorkout(
        userId,
        guideId: guideId,
      );
      
    } catch (e) {
      print('Onboarding error: $e');
      rethrow;
    }
  }
}