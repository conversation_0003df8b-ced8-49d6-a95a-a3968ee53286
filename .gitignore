# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# Environment variables
.env

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Test and debug files
test_*.dart
*_test.dart
debug_*.dart
temp_*.dart
deploy_*.dart
migrate_*.dart

# Firebase emulator
emulator.log
firebase-debug.log
firestore-debug.log
ui-debug.log
.firebase/

# Archives
archived_files/
archive/

# Node modules (for agent folder)
node_modules/

# IDE specific
.vscode/
local.properties

# OS files
Thumbs.db
.DS_Store
