{"indexes": [{"collectionGroup": "workoutHistory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "DESCENDING"}]}, {"collectionGroup": "workoutHistory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "userWorkouts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "DESCENDING"}]}, {"collectionGroup": "userWorkouts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isCompleted", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "DESCENDING"}]}, {"collectionGroup": "workoutPlans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isCustom", "order": "ASCENDING"}, {"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "exercises", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}]}, {"collectionGroup": "workoutPlans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "preferences.onboardingComplete", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fitnessProfile.goals", "arrayConfig": "CONTAINS"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "users", "fieldPath": "personalInfo.name", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "stats.totalWorkouts", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}