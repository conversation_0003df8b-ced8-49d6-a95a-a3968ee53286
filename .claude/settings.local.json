{"permissions": {"allow": ["Bash(flutter pub:*)", "<PERSON><PERSON>(dart test:*)", "Bash(flutter analyze:*)", "WebFetch(domain:generateandsaveworkout-hidgimzz2q-uc.a.run.app)", "Bash(grep:*)", "Bash(firebase projects:list:*)", "Bash(firebase functions:list:*)", "Bash(firebase functions:log:*)", "WebFetch(domain:firebase.google.com)", "Bash(dart analyze:*)", "WebFetch(domain:pub.dev)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(flutter clean:*)", "Bash(gem install:*)", "<PERSON><PERSON>(flutter run:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(flutter packages pub:*)", "<PERSON><PERSON>(cat:*)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(dart run:*)", "Bash(rg:*)", "Bash(firebase deploy:*)", "Bash(firebase --version)", "<PERSON><PERSON>(claude --mcp-debug)", "Bash(firebase experimental:mcp:*)", "Bash(npx firebase-tools@latest experimental:mcp:*)", "Bash(npm install:*)", "Bash(timeout 5 firebase experimental:mcp --help)", "<PERSON><PERSON>(gtimeout:*)", "Bash(firebase help:*)", "Bash(node:*)", "mcp__firebase__firebase_get_environment", "mcp__firebase__firestore_list_collections", "mcp__firebase__firestore_query_collection", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "mcp__firebase__firestore_get_documents", "Bash(git checkout:*)"], "deny": []}}